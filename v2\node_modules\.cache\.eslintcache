[{"D:\\Via\\New folder\\v2\\src\\index.js": "1", "D:\\Via\\New folder\\v2\\src\\App.js": "2", "D:\\Via\\New folder\\v2\\src\\reportWebVitals.js": "3", "D:\\Via\\New folder\\v2\\src\\pages\\Login.jsx": "4", "D:\\Via\\New folder\\v2\\src\\pages\\Watches.jsx": "5", "D:\\Via\\New folder\\v2\\src\\pages\\Bracelets.jsx": "6", "D:\\Via\\New folder\\v2\\src\\pages\\HowItWorks.jsx": "7", "D:\\Via\\New folder\\v2\\src\\pages\\Home.jsx": "8", "D:\\Via\\New folder\\v2\\src\\pages\\VirtualTryOn.jsx": "9", "D:\\Via\\New folder\\v2\\src\\pages\\WhyViaTryon.jsx": "10", "D:\\Via\\New folder\\v2\\src\\pages\\SearchResults.jsx": "11", "D:\\Via\\New folder\\v2\\src\\pages\\ProductDetails.jsx": "12", "D:\\Via\\New folder\\v2\\src\\pages\\Contact.jsx": "13", "D:\\Via\\New folder\\v2\\src\\pages\\Requirements.jsx": "14", "D:\\Via\\New folder\\v2\\src\\components\\DemoForm.jsx": "15", "D:\\Via\\New folder\\v2\\src\\components\\Footer.jsx": "16", "D:\\Via\\New folder\\v2\\src\\components\\Navbar.jsx": "17", "D:\\Via\\New folder\\v2\\src\\pages\\admin\\Clients.jsx": "18", "D:\\Via\\New folder\\v2\\src\\pages\\admin\\TryOnAnalytics.jsx": "19", "D:\\Via\\New folder\\v2\\src\\pages\\admin\\AdminDashboard.jsx": "20", "D:\\Via\\New folder\\v2\\src\\pages\\admin\\Settings.jsx": "21", "D:\\Via\\New folder\\v2\\src\\pages\\client\\ClientDashboard.jsx": "22", "D:\\Via\\New folder\\v2\\src\\pages\\client\\analytics\\ClientAnalytics.jsx": "23", "D:\\Via\\New folder\\v2\\src\\utils\\imageLoader.js": "24", "D:\\Via\\New folder\\v2\\src\\services\\api.js": "25", "D:\\Via\\New folder\\v2\\src\\data\\productCollections.js": "26", "D:\\Via\\New folder\\v2\\src\\context\\CartContext.js": "27", "D:\\Via\\New folder\\v2\\src\\pages\\client\\analytics\\UserEngagement.jsx": "28", "D:\\Via\\New folder\\v2\\src\\pages\\client\\analytics\\TimeAnalysis.jsx": "29", "D:\\Via\\New folder\\v2\\src\\pages\\client\\analytics\\Overview.jsx": "30", "D:\\Via\\New folder\\v2\\src\\pages\\client\\analytics\\ConversionRates.jsx": "31", "D:\\Via\\New folder\\v2\\src\\pages\\client\\analytics\\ProductPerformance.jsx": "32", "D:\\Via\\New folder\\v2\\src\\pages\\client\\analytics\\DeviceStats.jsx": "33", "D:\\Via\\New folder\\v2\\src\\components\\admin\\AdminNavbar.jsx": "34", "D:\\Via\\New folder\\v2\\src\\components\\admin\\AdminSidebar.jsx": "35", "D:\\Via\\New folder\\v2\\src\\pages\\client\\analytics\\GeographicData.jsx": "36", "D:\\Via\\New folder\\v2\\src\\components\\client\\ClientSidebar.jsx": "37", "D:\\Via\\New folder\\v2\\src\\components\\client\\ClientNavbar.jsx": "38", "D:\\Via\\New folder\\v2\\src\\utils\\backgroundRemover.js": "39", "D:\\Via\\New folder\\v2\\src\\components\\EmbedCodeGenerator.jsx": "40", "D:\\Via\\New folder\\v2\\src\\pages\\client\\ClientSettings.jsx": "41", "D:\\Via\\New folder\\v2\\src\\pages\\admin\\analytics\\AdminAnalytics.jsx": "42", "D:\\Via\\New folder\\v2\\src\\pages\\admin\\analytics\\UserEngagement.jsx": "43", "D:\\Via\\New folder\\v2\\src\\pages\\admin\\analytics\\DeviceTechnical.jsx": "44", "D:\\Via\\New folder\\v2\\src\\pages\\admin\\analytics\\Overview.jsx": "45", "D:\\Via\\New folder\\v2\\src\\pages\\admin\\analytics\\ProductAnalytics.jsx": "46", "D:\\Via\\New folder\\v2\\src\\pages\\admin\\analytics\\TryOnAnalytics.jsx": "47", "D:\\Via\\New folder\\v2\\src\\pages\\admin\\analytics\\ClientPerformance.jsx": "48", "D:\\Via\\New folder\\v2\\src\\pages\\admin\\analytics\\GeographicAnalytics.jsx": "49", "D:\\Via\\New folder\\v2\\src\\pages\\admin\\analytics\\BusinessMetrics.jsx": "50"}, {"size": 653, "mtime": 1749282898641, "results": "51", "hashOfConfig": "52"}, {"size": 5034, "mtime": 1749314161665, "results": "53", "hashOfConfig": "52"}, {"size": 362, "mtime": 1746042995461, "results": "54", "hashOfConfig": "52"}, {"size": 7675, "mtime": 1749282897977, "results": "55", "hashOfConfig": "52"}, {"size": 19373, "mtime": 1748874413890, "results": "56", "hashOfConfig": "52"}, {"size": 19487, "mtime": 1748874429932, "results": "57", "hashOfConfig": "52"}, {"size": 17189, "mtime": 1748864169684, "results": "58", "hashOfConfig": "52"}, {"size": 41162, "mtime": 1748975550119, "results": "59", "hashOfConfig": "52"}, {"size": 50127, "mtime": 1749318036758, "results": "60", "hashOfConfig": "52"}, {"size": 16901, "mtime": 1748864143496, "results": "61", "hashOfConfig": "52"}, {"size": 6108, "mtime": 1746344994148, "results": "62", "hashOfConfig": "52"}, {"size": 16294, "mtime": 1748293996467, "results": "63", "hashOfConfig": "52"}, {"size": 25359, "mtime": 1748867397685, "results": "64", "hashOfConfig": "52"}, {"size": 7100, "mtime": 1748867056681, "results": "65", "hashOfConfig": "52"}, {"size": 11616, "mtime": 1746342271795, "results": "66", "hashOfConfig": "52"}, {"size": 6479, "mtime": 1748866960444, "results": "67", "hashOfConfig": "52"}, {"size": 14287, "mtime": 1749152911428, "results": "68", "hashOfConfig": "52"}, {"size": 20074, "mtime": 1749203894531, "results": "69", "hashOfConfig": "52"}, {"size": 667, "mtime": 1749282899180, "results": "70", "hashOfConfig": "52"}, {"size": 17374, "mtime": 1749203517435, "results": "71", "hashOfConfig": "52"}, {"size": 12177, "mtime": 1749206358128, "results": "72", "hashOfConfig": "52"}, {"size": 17624, "mtime": 1749205884454, "results": "73", "hashOfConfig": "52"}, {"size": 3728, "mtime": 1749208975350, "results": "74", "hashOfConfig": "52"}, {"size": 8174, "mtime": 1748283061371, "results": "75", "hashOfConfig": "52"}, {"size": 1190, "mtime": 1748291506520, "results": "76", "hashOfConfig": "52"}, {"size": 10601, "mtime": 1748277235110, "results": "77", "hashOfConfig": "52"}, {"size": 4297, "mtime": 1748283089634, "results": "78", "hashOfConfig": "52"}, {"size": 6733, "mtime": 1749196502414, "results": "79", "hashOfConfig": "52"}, {"size": 6434, "mtime": 1749208975823, "results": "80", "hashOfConfig": "52"}, {"size": 8425, "mtime": 1749206130605, "results": "81", "hashOfConfig": "52"}, {"size": 5620, "mtime": 1749196502795, "results": "82", "hashOfConfig": "52"}, {"size": 6238, "mtime": 1749196501461, "results": "83", "hashOfConfig": "52"}, {"size": 6156, "mtime": 1749196502910, "results": "84", "hashOfConfig": "52"}, {"size": 8877, "mtime": 1749150209342, "results": "85", "hashOfConfig": "52"}, {"size": 7144, "mtime": 1749144357974, "results": "86", "hashOfConfig": "52"}, {"size": 5658, "mtime": 1749196503295, "results": "87", "hashOfConfig": "52"}, {"size": 8337, "mtime": 1749282896763, "results": "88", "hashOfConfig": "52"}, {"size": 8526, "mtime": 1749205011761, "results": "89", "hashOfConfig": "52"}, {"size": 5096, "mtime": 1748291546769, "results": "90", "hashOfConfig": "52"}, {"size": 9697, "mtime": 1749203944731, "results": "91", "hashOfConfig": "52"}, {"size": 19275, "mtime": 1749205741948, "results": "92", "hashOfConfig": "52"}, {"size": 4959, "mtime": 1749207899224, "results": "93", "hashOfConfig": "52"}, {"size": 14670, "mtime": 1749208140551, "results": "94", "hashOfConfig": "52"}, {"size": 15177, "mtime": 1749208295350, "results": "95", "hashOfConfig": "52"}, {"size": 9248, "mtime": 1749207936115, "results": "96", "hashOfConfig": "52"}, {"size": 15869, "mtime": 1749208086306, "results": "97", "hashOfConfig": "52"}, {"size": 11225, "mtime": 1749207979437, "results": "98", "hashOfConfig": "52"}, {"size": 15191, "mtime": 1749208031739, "results": "99", "hashOfConfig": "52"}, {"size": 13394, "mtime": 1749208238262, "results": "100", "hashOfConfig": "52"}, {"size": 12125, "mtime": 1749208187816, "results": "101", "hashOfConfig": "52"}, {"filePath": "102", "messages": "103", "suppressedMessages": "104", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1gdfhta", {"filePath": "105", "messages": "106", "suppressedMessages": "107", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "108", "messages": "109", "suppressedMessages": "110", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "111", "messages": "112", "suppressedMessages": "113", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "114", "messages": "115", "suppressedMessages": "116", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "117", "messages": "118", "suppressedMessages": "119", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "120", "messages": "121", "suppressedMessages": "122", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "123", "messages": "124", "suppressedMessages": "125", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "126", "messages": "127", "suppressedMessages": "128", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "129", "messages": "130", "suppressedMessages": "131", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "132", "messages": "133", "suppressedMessages": "134", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "135", "messages": "136", "suppressedMessages": "137", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "138", "messages": "139", "suppressedMessages": "140", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "141", "messages": "142", "suppressedMessages": "143", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "144", "messages": "145", "suppressedMessages": "146", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "147", "messages": "148", "suppressedMessages": "149", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "150", "messages": "151", "suppressedMessages": "152", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "153", "messages": "154", "suppressedMessages": "155", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "156", "messages": "157", "suppressedMessages": "158", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "159", "messages": "160", "suppressedMessages": "161", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "162", "messages": "163", "suppressedMessages": "164", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "165", "messages": "166", "suppressedMessages": "167", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "168", "messages": "169", "suppressedMessages": "170", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "171", "messages": "172", "suppressedMessages": "173", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "174", "messages": "175", "suppressedMessages": "176", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "177", "messages": "178", "suppressedMessages": "179", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "180", "messages": "181", "suppressedMessages": "182", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "183", "messages": "184", "suppressedMessages": "185", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "186", "messages": "187", "suppressedMessages": "188", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "189", "messages": "190", "suppressedMessages": "191", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "192", "messages": "193", "suppressedMessages": "194", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "195", "messages": "196", "suppressedMessages": "197", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "198", "messages": "199", "suppressedMessages": "200", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "201", "messages": "202", "suppressedMessages": "203", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "204", "messages": "205", "suppressedMessages": "206", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "207", "messages": "208", "suppressedMessages": "209", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "210", "messages": "211", "suppressedMessages": "212", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "213", "messages": "214", "suppressedMessages": "215", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "216", "messages": "217", "suppressedMessages": "218", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "219", "messages": "220", "suppressedMessages": "221", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "222", "messages": "223", "suppressedMessages": "224", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "225", "messages": "226", "suppressedMessages": "227", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "228", "messages": "229", "suppressedMessages": "230", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "231", "messages": "232", "suppressedMessages": "233", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "234", "messages": "235", "suppressedMessages": "236", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "237", "messages": "238", "suppressedMessages": "239", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "240", "messages": "241", "suppressedMessages": "242", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "243", "messages": "244", "suppressedMessages": "245", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "246", "messages": "247", "suppressedMessages": "248", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "249", "messages": "250", "suppressedMessages": "251", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "D:\\Via\\New folder\\v2\\src\\index.js", [], [], "D:\\Via\\New folder\\v2\\src\\App.js", [], [], "D:\\Via\\New folder\\v2\\src\\reportWebVitals.js", [], [], "D:\\Via\\New folder\\v2\\src\\pages\\Login.jsx", [], [], "D:\\Via\\New folder\\v2\\src\\pages\\Watches.jsx", [], [], "D:\\Via\\New folder\\v2\\src\\pages\\Bracelets.jsx", [], [], "D:\\Via\\New folder\\v2\\src\\pages\\HowItWorks.jsx", [], [], "D:\\Via\\New folder\\v2\\src\\pages\\Home.jsx", [], [], "D:\\Via\\New folder\\v2\\src\\pages\\VirtualTryOn.jsx", ["252", "253", "254", "255", "256", "257"], [], "D:\\Via\\New folder\\v2\\src\\pages\\WhyViaTryon.jsx", [], [], "D:\\Via\\New folder\\v2\\src\\pages\\SearchResults.jsx", [], [], "D:\\Via\\New folder\\v2\\src\\pages\\ProductDetails.jsx", [], [], "D:\\Via\\New folder\\v2\\src\\pages\\Contact.jsx", [], [], "D:\\Via\\New folder\\v2\\src\\pages\\Requirements.jsx", [], [], "D:\\Via\\New folder\\v2\\src\\components\\DemoForm.jsx", [], [], "D:\\Via\\New folder\\v2\\src\\components\\Footer.jsx", [], [], "D:\\Via\\New folder\\v2\\src\\components\\Navbar.jsx", ["258"], [], "D:\\Via\\New folder\\v2\\src\\pages\\admin\\Clients.jsx", ["259"], [], "D:\\Via\\New folder\\v2\\src\\pages\\admin\\TryOnAnalytics.jsx", [], [], "D:\\Via\\New folder\\v2\\src\\pages\\admin\\AdminDashboard.jsx", ["260", "261", "262", "263", "264", "265", "266"], [], "D:\\Via\\New folder\\v2\\src\\pages\\admin\\Settings.jsx", ["267", "268"], [], "D:\\Via\\New folder\\v2\\src\\pages\\client\\ClientDashboard.jsx", ["269", "270", "271"], [], "D:\\Via\\New folder\\v2\\src\\pages\\client\\analytics\\ClientAnalytics.jsx", ["272", "273"], [], "D:\\Via\\New folder\\v2\\src\\utils\\imageLoader.js", [], [], "D:\\Via\\New folder\\v2\\src\\services\\api.js", [], [], "D:\\Via\\New folder\\v2\\src\\data\\productCollections.js", [], [], "D:\\Via\\New folder\\v2\\src\\context\\CartContext.js", [], [], "D:\\Via\\New folder\\v2\\src\\pages\\client\\analytics\\UserEngagement.jsx", ["274", "275", "276"], [], "D:\\Via\\New folder\\v2\\src\\pages\\client\\analytics\\TimeAnalysis.jsx", ["277"], [], "D:\\Via\\New folder\\v2\\src\\pages\\client\\analytics\\Overview.jsx", ["278"], [], "D:\\Via\\New folder\\v2\\src\\pages\\client\\analytics\\ConversionRates.jsx", ["279"], [], "D:\\Via\\New folder\\v2\\src\\pages\\client\\analytics\\ProductPerformance.jsx", ["280"], [], "D:\\Via\\New folder\\v2\\src\\pages\\client\\analytics\\DeviceStats.jsx", [], [], "D:\\Via\\New folder\\v2\\src\\components\\admin\\AdminNavbar.jsx", [], [], "D:\\Via\\New folder\\v2\\src\\components\\admin\\AdminSidebar.jsx", [], [], "D:\\Via\\New folder\\v2\\src\\pages\\client\\analytics\\GeographicData.jsx", ["281"], [], "D:\\Via\\New folder\\v2\\src\\components\\client\\ClientSidebar.jsx", ["282"], [], "D:\\Via\\New folder\\v2\\src\\components\\client\\ClientNavbar.jsx", [], [], "D:\\Via\\New folder\\v2\\src\\utils\\backgroundRemover.js", [], [], "D:\\Via\\New folder\\v2\\src\\components\\EmbedCodeGenerator.jsx", ["283", "284", "285"], [], "D:\\Via\\New folder\\v2\\src\\pages\\client\\ClientSettings.jsx", ["286", "287"], [], "D:\\Via\\New folder\\v2\\src\\pages\\admin\\analytics\\AdminAnalytics.jsx", [], [], "D:\\Via\\New folder\\v2\\src\\pages\\admin\\analytics\\UserEngagement.jsx", ["288"], [], "D:\\Via\\New folder\\v2\\src\\pages\\admin\\analytics\\DeviceTechnical.jsx", ["289"], [], "D:\\Via\\New folder\\v2\\src\\pages\\admin\\analytics\\Overview.jsx", ["290", "291", "292", "293"], [], "D:\\Via\\New folder\\v2\\src\\pages\\admin\\analytics\\ProductAnalytics.jsx", ["294", "295", "296", "297", "298", "299"], [], "D:\\Via\\New folder\\v2\\src\\pages\\admin\\analytics\\TryOnAnalytics.jsx", ["300", "301", "302"], [], "D:\\Via\\New folder\\v2\\src\\pages\\admin\\analytics\\ClientPerformance.jsx", ["303", "304", "305"], [], "D:\\Via\\New folder\\v2\\src\\pages\\admin\\analytics\\GeographicAnalytics.jsx", ["306", "307"], [], "D:\\Via\\New folder\\v2\\src\\pages\\admin\\analytics\\BusinessMetrics.jsx", ["308", "309", "310"], [], {"ruleId": "311", "severity": 1, "message": "312", "line": 3, "column": 48, "nodeType": "313", "messageId": "314", "endLine": 3, "endColumn": 51}, {"ruleId": "311", "severity": 1, "message": "315", "line": 3, "column": 53, "nodeType": "313", "messageId": "314", "endLine": 3, "endColumn": 59}, {"ruleId": "311", "severity": 1, "message": "316", "line": 18, "column": 10, "nodeType": "313", "messageId": "314", "endLine": 18, "endColumn": 18}, {"ruleId": "311", "severity": 1, "message": "317", "line": 35, "column": 9, "nodeType": "313", "messageId": "314", "endLine": 35, "endColumn": 17}, {"ruleId": "311", "severity": 1, "message": "318", "line": 138, "column": 9, "nodeType": "313", "messageId": "314", "endLine": 138, "endColumn": 25}, {"ruleId": "319", "severity": 1, "message": "320", "line": 695, "column": 6, "nodeType": "321", "endLine": 695, "endColumn": 55, "suggestions": "322"}, {"ruleId": "311", "severity": 1, "message": "323", "line": 37, "column": 9, "nodeType": "313", "messageId": "314", "endLine": 37, "endColumn": 17}, {"ruleId": "311", "severity": 1, "message": "324", "line": 5, "column": 10, "nodeType": "313", "messageId": "314", "endLine": 5, "endColumn": 16}, {"ruleId": "311", "severity": 1, "message": "325", "line": 1, "column": 27, "nodeType": "313", "messageId": "314", "endLine": 1, "endColumn": 36}, {"ruleId": "311", "severity": 1, "message": "326", "line": 5, "column": 27, "nodeType": "313", "messageId": "314", "endLine": 5, "endColumn": 35}, {"ruleId": "311", "severity": 1, "message": "327", "line": 5, "column": 37, "nodeType": "313", "messageId": "314", "endLine": 5, "endColumn": 40}, {"ruleId": "311", "severity": 1, "message": "328", "line": 6, "column": 48, "nodeType": "313", "messageId": "314", "endLine": 6, "endColumn": 53}, {"ruleId": "311", "severity": 1, "message": "329", "line": 6, "column": 62, "nodeType": "313", "messageId": "314", "endLine": 6, "endColumn": 72}, {"ruleId": "311", "severity": 1, "message": "330", "line": 6, "column": 74, "nodeType": "313", "messageId": "314", "endLine": 6, "endColumn": 81}, {"ruleId": "311", "severity": 1, "message": "331", "line": 12, "column": 25, "nodeType": "313", "messageId": "314", "endLine": 12, "endColumn": 41}, {"ruleId": "311", "severity": 1, "message": "332", "line": 4, "column": 10, "nodeType": "313", "messageId": "314", "endLine": 4, "endColumn": 16}, {"ruleId": "311", "severity": 1, "message": "333", "line": 26, "column": 9, "nodeType": "313", "messageId": "314", "endLine": 26, "endColumn": 26}, {"ruleId": "311", "severity": 1, "message": "325", "line": 1, "column": 27, "nodeType": "313", "messageId": "314", "endLine": 1, "endColumn": 36}, {"ruleId": "311", "severity": 1, "message": "334", "line": 7, "column": 61, "nodeType": "313", "messageId": "314", "endLine": 7, "endColumn": 66}, {"ruleId": "311", "severity": 1, "message": "335", "line": 14, "column": 22, "nodeType": "313", "messageId": "314", "endLine": 14, "endColumn": 35}, {"ruleId": "311", "severity": 1, "message": "325", "line": 1, "column": 27, "nodeType": "313", "messageId": "314", "endLine": 1, "endColumn": 36}, {"ruleId": "311", "severity": 1, "message": "336", "line": 1, "column": 38, "nodeType": "313", "messageId": "314", "endLine": 1, "endColumn": 44}, {"ruleId": "311", "severity": 1, "message": "326", "line": 5, "column": 3, "nodeType": "313", "messageId": "314", "endLine": 5, "endColumn": 11}, {"ruleId": "311", "severity": 1, "message": "327", "line": 6, "column": 3, "nodeType": "313", "messageId": "314", "endLine": 6, "endColumn": 6}, {"ruleId": "311", "severity": 1, "message": "337", "line": 14, "column": 3, "nodeType": "313", "messageId": "314", "endLine": 14, "endColumn": 9}, {"ruleId": "311", "severity": 1, "message": "337", "line": 11, "column": 3, "nodeType": "313", "messageId": "314", "endLine": 11, "endColumn": 9}, {"ruleId": "311", "severity": 1, "message": "337", "line": 14, "column": 3, "nodeType": "313", "messageId": "314", "endLine": 14, "endColumn": 9}, {"ruleId": "311", "severity": 1, "message": "337", "line": 11, "column": 3, "nodeType": "313", "messageId": "314", "endLine": 11, "endColumn": 9}, {"ruleId": "311", "severity": 1, "message": "337", "line": 11, "column": 3, "nodeType": "313", "messageId": "314", "endLine": 11, "endColumn": 9}, {"ruleId": "311", "severity": 1, "message": "337", "line": 9, "column": 3, "nodeType": "313", "messageId": "314", "endLine": 9, "endColumn": 9}, {"ruleId": "311", "severity": 1, "message": "338", "line": 1, "column": 17, "nodeType": "313", "messageId": "314", "endLine": 1, "endColumn": 25}, {"ruleId": "311", "severity": 1, "message": "339", "line": 3, "column": 23, "nodeType": "313", "messageId": "314", "endLine": 3, "endColumn": 27}, {"ruleId": "340", "severity": 1, "message": "341", "line": 197, "column": 31, "nodeType": "342", "endLine": 197, "endColumn": 65}, {"ruleId": "340", "severity": 1, "message": "341", "line": 198, "column": 21, "nodeType": "342", "endLine": 198, "endColumn": 60}, {"ruleId": "311", "severity": 1, "message": "332", "line": 4, "column": 10, "nodeType": "313", "messageId": "314", "endLine": 4, "endColumn": 16}, {"ruleId": "311", "severity": 1, "message": "334", "line": 5, "column": 36, "nodeType": "313", "messageId": "314", "endLine": 5, "endColumn": 41}, {"ruleId": "311", "severity": 1, "message": "343", "line": 21, "column": 21, "nodeType": "313", "messageId": "314", "endLine": 21, "endColumn": 33}, {"ruleId": "311", "severity": 1, "message": "343", "line": 19, "column": 21, "nodeType": "313", "messageId": "314", "endLine": 19, "endColumn": 33}, {"ruleId": "311", "severity": 1, "message": "344", "line": 11, "column": 3, "nodeType": "313", "messageId": "314", "endLine": 11, "endColumn": 11}, {"ruleId": "311", "severity": 1, "message": "345", "line": 12, "column": 3, "nodeType": "313", "messageId": "314", "endLine": 12, "endColumn": 6}, {"ruleId": "311", "severity": 1, "message": "346", "line": 13, "column": 3, "nodeType": "313", "messageId": "314", "endLine": 13, "endColumn": 7}, {"ruleId": "311", "severity": 1, "message": "343", "line": 19, "column": 21, "nodeType": "313", "messageId": "314", "endLine": 19, "endColumn": 33}, {"ruleId": "311", "severity": 1, "message": "347", "line": 11, "column": 3, "nodeType": "313", "messageId": "314", "endLine": 11, "endColumn": 12}, {"ruleId": "311", "severity": 1, "message": "348", "line": 12, "column": 3, "nodeType": "313", "messageId": "314", "endLine": 12, "endColumn": 7}, {"ruleId": "311", "severity": 1, "message": "344", "line": 13, "column": 3, "nodeType": "313", "messageId": "314", "endLine": 13, "endColumn": 11}, {"ruleId": "311", "severity": 1, "message": "345", "line": 14, "column": 3, "nodeType": "313", "messageId": "314", "endLine": 14, "endColumn": 6}, {"ruleId": "311", "severity": 1, "message": "346", "line": 15, "column": 3, "nodeType": "313", "messageId": "314", "endLine": 15, "endColumn": 7}, {"ruleId": "311", "severity": 1, "message": "343", "line": 21, "column": 21, "nodeType": "313", "messageId": "314", "endLine": 21, "endColumn": 33}, {"ruleId": "311", "severity": 1, "message": "347", "line": 4, "column": 3, "nodeType": "313", "messageId": "314", "endLine": 4, "endColumn": 12}, {"ruleId": "311", "severity": 1, "message": "348", "line": 5, "column": 3, "nodeType": "313", "messageId": "314", "endLine": 5, "endColumn": 7}, {"ruleId": "311", "severity": 1, "message": "343", "line": 19, "column": 21, "nodeType": "313", "messageId": "314", "endLine": 19, "endColumn": 33}, {"ruleId": "311", "severity": 1, "message": "347", "line": 11, "column": 3, "nodeType": "313", "messageId": "314", "endLine": 11, "endColumn": 12}, {"ruleId": "311", "severity": 1, "message": "348", "line": 12, "column": 3, "nodeType": "313", "messageId": "314", "endLine": 12, "endColumn": 7}, {"ruleId": "311", "severity": 1, "message": "343", "line": 19, "column": 21, "nodeType": "313", "messageId": "314", "endLine": 19, "endColumn": 33}, {"ruleId": "311", "severity": 1, "message": "343", "line": 17, "column": 21, "nodeType": "313", "messageId": "314", "endLine": 17, "endColumn": 33}, {"ruleId": "311", "severity": 1, "message": "349", "line": 103, "column": 9, "nodeType": "313", "messageId": "314", "endLine": 103, "endColumn": 26}, {"ruleId": "311", "severity": 1, "message": "347", "line": 4, "column": 3, "nodeType": "313", "messageId": "314", "endLine": 4, "endColumn": 12}, {"ruleId": "311", "severity": 1, "message": "348", "line": 5, "column": 3, "nodeType": "313", "messageId": "314", "endLine": 5, "endColumn": 7}, {"ruleId": "311", "severity": 1, "message": "343", "line": 18, "column": 21, "nodeType": "313", "messageId": "314", "endLine": 18, "endColumn": 33}, "no-unused-vars", "'Eye' is defined but never used.", "Identifier", "unusedVar", "'Camera' is defined but never used.", "'isMobile' is assigned a value but never used.", "'panelRef' is assigned a value but never used.", "'getWatchPosition' is assigned a value but never used.", "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'handleCapture'. Either include it or remove the dependency array.", "ArrayExpression", ["350"], "'isClient' is assigned a value but never used.", "'Search' is defined but never used.", "'useEffect' is defined but never used.", "'BarChart' is defined but never used.", "'Bar' is defined but never used.", "'Clock' is defined but never used.", "'Smartphone' is defined but never used.", "'Monitor' is defined but never used.", "'setDashboardData' is assigned a value but never used.", "'motion' is defined but never used.", "'handleInputChange' is assigned a value but never used.", "'Globe' is defined but never used.", "'setClientData' is assigned a value but never used.", "'useRef' is defined but never used.", "'Legend' is defined but never used.", "'useState' is defined but never used.", "'Code' is defined but never used.", "jsx-a11y/anchor-is-valid", "The href attribute requires a valid value to be accessible. Provide a valid, navigable address as the href value. If you cannot provide a valid href, but still need the element to resemble a link, use a button and change it with appropriate styles. Learn more: https://github.com/jsx-eslint/eslint-plugin-jsx-a11y/blob/HEAD/docs/rules/anchor-is-valid.md", "JSXOpeningElement", "'setTimeRange' is assigned a value but never used.", "'PieChart' is defined but never used.", "'Pie' is defined but never used.", "'Cell' is defined but never used.", "'LineChart' is defined but never used.", "'Line' is defined but never used.", "'avgConversionRate' is assigned a value but never used.", {"desc": "351", "fix": "352"}, "Update the dependencies array to be: [isCountdownActive, isCaptured, isHandInPosition, handleCapture]", {"range": "353", "text": "354"}, [24262, 24311], "[isCountdownActive, isCaptured, isHandInPosition, handleCapture]"]