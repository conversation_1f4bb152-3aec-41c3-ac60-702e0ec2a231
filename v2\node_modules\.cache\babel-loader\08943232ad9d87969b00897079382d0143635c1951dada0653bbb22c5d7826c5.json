{"ast": null, "code": "var _jsxFileName = \"D:\\\\Via\\\\New folder\\\\v2\\\\src\\\\pages\\\\VirtualTryOn.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useRef } from 'react';\n\n// Add CSS for range slider styling\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst sliderCSS = `\n  input[type=\"range\"]::-webkit-slider-thumb {\n    -webkit-appearance: none;\n    appearance: none;\n    width: 20px;\n    height: 20px;\n    border-radius: 50%;\n    background: #2D8C88;\n    cursor: pointer;\n    box-shadow: 0 2px 6px rgba(45, 140, 136, 0.3);\n  }\n\n  input[type=\"range\"]::-moz-range-thumb {\n    width: 20px;\n    height: 20px;\n    border-radius: 50%;\n    background: #2D8C88;\n    cursor: pointer;\n    border: none;\n    box-shadow: 0 2px 6px rgba(45, 140, 136, 0.3);\n  }\n\n  input[type=\"range\"]::-webkit-slider-track {\n    background: #e0e0e0;\n    height: 6px;\n    border-radius: 3px;\n  }\n\n  input[type=\"range\"]::-moz-range-track {\n    background: #e0e0e0;\n    height: 6px;\n    border-radius: 3px;\n    border: none;\n  }\n\n  input[type=\"range\"]:focus {\n    outline: none;\n  }\n\n  input[type=\"range\"]:focus::-webkit-slider-thumb {\n    box-shadow: 0 0 0 3px rgba(45, 140, 136, 0.2);\n  }\n\n  input[type=\"range\"]:focus::-moz-range-thumb {\n    box-shadow: 0 0 0 3px rgba(45, 140, 136, 0.2);\n  }\n`;\n\n// Inject CSS\nif (typeof document !== 'undefined') {\n  const styleElement = document.createElement('style');\n  styleElement.textContent = sliderCSS;\n  document.head.appendChild(styleElement);\n}\nconst Tryon = ({\n  onBackToHome\n}) => {\n  _s();\n  // Refs for DOM elements\n  const videoRef = useRef(null);\n  const capturedImageRef = useRef(null);\n  const canvasRef = useRef(null);\n\n  // State variables\n  const [isCaptured, setIsCaptured] = useState(false);\n  const [selectedProduct, setSelectedProduct] = useState(null);\n  const [isRightHand, setIsRightHand] = useState(false);\n  const [showProductSelection, setShowProductSelection] = useState(false);\n  const [activeTab, setActiveTab] = useState('Watches');\n  const [showHandGuide, setShowHandGuide] = useState(true);\n  const [isMobile, setIsMobile] = useState(false);\n\n  // Watch size customization state\n  const [userGender, setUserGender] = useState('men'); // 'men' or 'women'\n  const [userWristSize, setUserWristSize] = useState(64); // Default men's wrist size in mm\n  const [showWristSizeInput, setShowWristSizeInput] = useState(false);\n\n  // Autocapture state variables\n  const [isAutoCaptureEnabled, setIsAutoCaptureEnabled] = useState(false);\n  const [countdown, setCountdown] = useState(0);\n  const [isHandInPosition, setIsHandInPosition] = useState(false);\n  const [isCountdownActive, setIsCountdownActive] = useState(false);\n\n  // Detect mobile device and set viewport height for Chrome mobile\n  useEffect(() => {\n    const checkMobile = () => {\n      setIsMobile(window.innerWidth <= 768);\n    };\n\n    // Fix viewport height for Chrome mobile\n    const setVH = () => {\n      const vh = window.innerHeight * 0.01;\n      document.documentElement.style.setProperty('--vh', `${vh}px`);\n    };\n    checkMobile();\n    setVH();\n    window.addEventListener('resize', () => {\n      checkMobile();\n      setVH();\n    });\n    window.addEventListener('orientationchange', () => {\n      setTimeout(() => {\n        setVH();\n      }, 100);\n    });\n    return () => {\n      window.removeEventListener('resize', checkMobile);\n      window.removeEventListener('orientationchange', setVH);\n    };\n  }, []);\n\n  // Realistic sizing configuration\n  // Average adult wrist circumference: 165mm (men), 155mm (women)\n  // Average wrist width when viewed from top: ~55-65mm\n  // SVG guide circle represents the wrist area (120px diameter in 800px viewBox)\n  // This means the circle represents approximately 60mm in real life\n\n  // Default wrist sizes by gender\n  const DEFAULT_WRIST_SIZES = {\n    men: 64,\n    // mm - default men's wrist size\n    women: 54 // mm - default women's wrist size\n  };\n  const SVG_WRIST_CIRCLE_DIAMETER = 120; // SVG circle diameter in viewBox units\n  const SVG_VIEWBOX_WIDTH = 800; // SVG viewBox width\n  const SVG_VIEWBOX_HEIGHT = 600; // SVG viewBox height\n\n  // Size configuration - Increased for better visibility\n  const WATCH_WIDTH = 25; // percentage of container width\n  const BRACELET_WIDTH = 15; // percentage of container width\n  const WATCH_HEIGHT = 38; // percentage of container height\n  const BRACELET_HEIGHT = 55; // percentage of container height - INCREASED for better visibility\n\n  // Test: Try changing WATCH_HEIGHT to 50 to see a much taller watch\n  // Test: Try changing WATCH_HEIGHT to 15 to see a shorter watch\n  // Test: Try changing BRACELET_HEIGHT to 70 to see a much taller bracelet\n  // Test: Try changing BRACELET_HEIGHT to 20 to see a shorter bracelet\n  // Note: Scale is now dynamic - smaller height = smaller scale, larger height = larger scale\n\n  // Calculate realistic watch sizing based on actual dimensions and user's wrist size\n  const calculateWatchDimensions = (watch, containerWidth, containerHeight) => {\n    // Get the default wrist size for the current gender\n    const defaultWristSize = DEFAULT_WRIST_SIZES[userGender];\n\n    // Calculate relative scaling factor based on user's wrist size vs default\n    const wristSizeRatio = userWristSize / defaultWristSize;\n\n    // Calculate the scale factor from real world to SVG coordinates using user's wrist size\n    const mmToSvgScale = SVG_WRIST_CIRCLE_DIAMETER / userWristSize;\n\n    // Convert watch dimensions to SVG units\n    const watchWidthSvg = watch.totalWidth * mmToSvgScale;\n    const watchHeightSvg = watch.totalHeight * mmToSvgScale;\n    const dialDiameterSvg = watch.dialDiameter * mmToSvgScale;\n\n    // Convert SVG units to container percentages\n    const watchWidthPercent = watchWidthSvg / SVG_VIEWBOX_WIDTH * 100;\n    const watchHeightPercent = watchHeightSvg / SVG_VIEWBOX_HEIGHT * 100;\n    const dialDiameterPercent = dialDiameterSvg / SVG_VIEWBOX_WIDTH * 100;\n\n    // Calculate positioning - watches should be positioned on the wrist circle\n    // SVG circle is at cx=\"400\" cy=\"300\" (center of the hand area)\n    const positionX = 50; // Center horizontally (400/800 = 50%)\n    const positionY = 50; // Center vertically (300/600 = 50%)\n\n    return {\n      width: Math.max(watchWidthPercent, 8),\n      // Minimum 8% for visibility\n      height: Math.max(watchHeightPercent, 10),\n      // Minimum 10% for visibility\n      dialDiameter: dialDiameterPercent,\n      positionX,\n      positionY,\n      scale: Math.min(watchWidthPercent / 15, watchHeightPercent / 20) * wristSizeRatio,\n      // Dynamic scale with wrist size adjustment\n      realWidth: watch.totalWidth,\n      realHeight: watch.totalHeight,\n      caseDiameter: watch.caseDiameter,\n      wristSizeRatio // Include ratio for additional scaling if needed\n    };\n  };\n\n  // Calculate watch position based on hand orientation and anatomy\n  const getWatchPosition = (watchData, isRightHand) => {\n    const baseDimensions = calculateWatchDimensions(watchData, 400, 600);\n\n    // Adjust position based on hand orientation\n    // Watches are typically worn on the top of the wrist\n    let adjustedX = baseDimensions.positionX;\n    let adjustedY = baseDimensions.positionY - 2; // Slightly higher on the wrist\n\n    // For right hand, watch might be positioned slightly differently\n    if (isRightHand) {\n      adjustedX = baseDimensions.positionX + 1; // Slight adjustment for right hand\n    }\n\n    // Adjust for different watch types\n    switch (watchData.type) {\n      case 'smartwatch':\n        // Smart watches are often worn higher on the wrist\n        adjustedY -= 1;\n        break;\n      case 'luxury':\n        // Luxury watches might be positioned more precisely\n        adjustedY -= 0.5;\n        break;\n      case 'sport':\n        // Sport watches might be worn slightly looser\n        adjustedY += 0.5;\n        break;\n      default:\n        break;\n    }\n    return {\n      ...baseDimensions,\n      positionX: adjustedX,\n      positionY: adjustedY\n    };\n  };\n  // Realistic watch data with actual dimensions (in millimeters)\n  const watches = [{\n    name: \"Classic Black\",\n    path: \"watches/watch_1.png\",\n    // Rolex Submariner style - 40mm case\n    caseDiameter: 41,\n    // mm\n    caseThickness: 12.5,\n    // mm\n    totalWidth: 42,\n    // mm (including crown)\n    totalHeight: 47,\n    // mm (lug to lug)\n    dialDiameter: 31,\n    // mm (visible dial)\n    type: \"dress\",\n    dialSize: 40\n  }, {\n    name: \"Silver Chrono\",\n    path: \"watches/watch_2.png\",\n    // Omega Speedmaster style - 42mm case\n    caseDiameter: 42,\n    // mm\n    caseThickness: 13.2,\n    // mm\n    totalWidth: 44,\n    // mm\n    totalHeight: 48.5,\n    // mm\n    dialDiameter: 33,\n    // mm\n    type: \"sport\",\n    dialSize: 42\n  }, {\n    name: \"Gold Luxury\",\n    path: \"watches/watch_3.png\",\n    // Patek Philippe Calatrava style - 38mm case\n    caseDiameter: 39,\n    // mm\n    caseThickness: 8.5,\n    // mm\n    totalWidth: 39,\n    // mm\n    totalHeight: 45,\n    // mm\n    dialDiameter: 30,\n    // mm\n    type: \"luxury\",\n    dialSize: 38\n  }, {\n    name: \"Sport Blue\",\n    path: \"watches/watch_6.png\",\n    // Apple Watch style - 44mm case\n    caseDiameter: 41,\n    // mm (width)\n    caseThickness: 10.7,\n    // mm\n    totalWidth: 44,\n    // mm\n    totalHeight: 38,\n    // mm (height - rectangular)\n    dialDiameter: 35,\n    // mm (screen diagonal)\n    type: \"smartwatch\",\n    dialSize: 44\n  }, {\n    name: \"Minimalist\",\n    path: \"watches/watch_5.png\",\n    // Daniel Wellington style - 36mm case\n    caseDiameter: 36,\n    // mm\n    caseThickness: 6,\n    // mm\n    totalWidth: 37,\n    // mm\n    totalHeight: 43,\n    // mm\n    dialDiameter: 28,\n    // mm\n    type: \"minimalist\",\n    dialSize: 36\n  }, {\n    name: \"Rose Gold\",\n    path: \"watches/watch_4.png\",\n    // Michael Kors style - 39mm case\n    caseDiameter: 44,\n    // mm\n    caseThickness: 11,\n    // mm\n    totalWidth: 41,\n    // mm\n    totalHeight: 46,\n    // mm\n    dialDiameter: 31,\n    // mm\n    type: \"fashion\",\n    dialSize: 41\n  }];\n  const bracelets = [{\n    name: \"Silver Chain\",\n    path: \"bracelets/bracelet_1.png\"\n  }, {\n    name: \"Gold Bangle\",\n    path: \"bracelets/bracelet_2.png\"\n  }, {\n    name: \"Leather Wrap\",\n    path: \"bracelets/bracelet_3.png\"\n  }, {\n    name: \"Diamond Tennis\",\n    path: \"bracelets/bracelet_4.png\"\n  }, {\n    name: \"Beaded Stone\",\n    path: \"bracelets/bracelet_5.png\"\n  }, {\n    name: \"Charm Bracelet\",\n    path: \"bracelets/bracelet_6.png\"\n  }];\n\n  // Initialize camera\n  const initCamera = async () => {\n    try {\n      const stream = await navigator.mediaDevices.getUserMedia({\n        video: {\n          facingMode: 'environment',\n          width: {\n            ideal: 1920\n          },\n          height: {\n            ideal: 1080\n          }\n        }\n      });\n      if (videoRef.current) {\n        videoRef.current.srcObject = stream;\n      }\n    } catch (err) {\n      console.error(\"Error accessing camera:\", err);\n      // Demo mode fallback\n      if (capturedImageRef.current) {\n        capturedImageRef.current.src = \"sample-hand.jpg\";\n        capturedImageRef.current.style.display = \"block\";\n      }\n      console.log(\"Camera not available - demo mode\");\n      setIsCaptured(true);\n      setShowProductSelection(true);\n    }\n  };\n\n  // Capture current frame\n  const captureFrame = () => {\n    if (!videoRef.current || !canvasRef.current) return null;\n    const canvas = canvasRef.current;\n    const video = videoRef.current;\n    canvas.width = video.videoWidth;\n    canvas.height = video.videoHeight;\n    const ctx = canvas.getContext('2d');\n    ctx.drawImage(video, 0, 0, canvas.width, canvas.height);\n    return canvas.toDataURL('image/png');\n  };\n\n  // Improved background remover that preserves light colors\n  const removeBackground = (imgElement, productType = 'watch') => {\n    const img = new Image();\n    img.crossOrigin = 'anonymous';\n    img.onload = function () {\n      const canvas = document.createElement('canvas');\n      const ctx = canvas.getContext('2d');\n      canvas.width = img.naturalWidth;\n      canvas.height = img.naturalHeight;\n      ctx.drawImage(img, 0, 0);\n      try {\n        const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);\n        const data = imageData.data;\n        const width = canvas.width;\n        const height = canvas.height;\n\n        // First pass: Identify edge pixels to preserve product boundaries\n        const edgePixels = new Set();\n        for (let y = 1; y < height - 1; y++) {\n          for (let x = 1; x < width - 1; x++) {\n            const idx = (y * width + x) * 4;\n            const r = data[idx];\n            const g = data[idx + 1];\n            const b = data[idx + 2];\n\n            // Check surrounding pixels for edge detection\n            let isEdge = false;\n            for (let dy = -1; dy <= 1; dy++) {\n              for (let dx = -1; dx <= 1; dx++) {\n                if (dx === 0 && dy === 0) continue;\n                const neighborIdx = ((y + dy) * width + (x + dx)) * 4;\n                const nr = data[neighborIdx];\n                const ng = data[neighborIdx + 1];\n                const nb = data[neighborIdx + 2];\n\n                // If there's a significant color difference, it's an edge\n                const colorDiff = Math.abs(r - nr) + Math.abs(g - ng) + Math.abs(b - nb);\n                if (colorDiff > 60) {\n                  isEdge = true;\n                  break;\n                }\n              }\n              if (isEdge) break;\n            }\n            if (isEdge) {\n              edgePixels.add(idx / 4);\n            }\n          }\n        }\n\n        // Second pass: Remove only white background while preserving all product colors\n        for (let i = 0; i < data.length; i += 4) {\n          const r = data[i];\n          const g = data[i + 1];\n          const b = data[i + 2];\n          const pixelIndex = i / 4;\n\n          // Calculate color properties\n          const brightness = (r + g + b) / 3;\n\n          // Check if pixel is near edges (preserve product boundaries)\n          const isNearEdge = edgePixels.has(pixelIndex);\n\n          // Only remove pure white or near-white pixels that aren't edges\n          const isPureWhite = r > 245 && g > 245 && b > 245 && Math.abs(r - g) < 10 && Math.abs(g - b) < 10 && !isNearEdge;\n\n          // Remove background if it's pure white and not an edge\n          if (isPureWhite) {\n            data[i + 3] = 0; // Make fully transparent\n          } else if (brightness > 240 && !isNearEdge) {\n            // For very bright but not pure white, reduce opacity slightly\n            data[i + 3] = Math.max(0, data[i + 3] - 50);\n          }\n        }\n        ctx.putImageData(imageData, 0, 0);\n        imgElement.src = canvas.toDataURL('image/png');\n\n        // Apply product-specific styling that preserves all colors\n        imgElement.style.filter = 'none';\n        imgElement.style.mixBlendMode = 'normal';\n        imgElement.style.opacity = '1';\n      } catch (e) {\n        console.warn('Canvas operation failed:', e);\n        // Fallback styling that preserves all colors\n        imgElement.style.filter = 'none';\n        imgElement.style.mixBlendMode = 'normal';\n        imgElement.style.opacity = '1';\n      }\n    };\n    img.onerror = function () {\n      console.warn('Image loading failed');\n      // Fallback styling\n      imgElement.style.filter = 'none';\n      imgElement.style.mixBlendMode = 'normal';\n      imgElement.style.opacity = '1';\n    };\n    img.src = imgElement.src;\n  };\n\n  // Placeholder for hand detection\n  const detectHandOrientation = imageData => {\n    // Simple heuristic for demo purposes\n    return Math.random() > 0.5;\n  };\n\n  // Detect if arm and wrist are within the SVG guide area\n  const detectHandInPosition = () => {\n    if (!videoRef.current || !canvasRef.current) return false;\n    const video = videoRef.current;\n    const canvas = canvasRef.current;\n    const ctx = canvas.getContext('2d');\n\n    // Set canvas size to match video\n    canvas.width = video.videoWidth;\n    canvas.height = video.videoHeight;\n\n    // Draw current video frame\n    ctx.drawImage(video, 0, 0, canvas.width, canvas.height);\n\n    // Get the video container dimensions to calculate the guide area\n    const videoContainer = video.parentElement;\n    const containerRect = videoContainer.getBoundingClientRect();\n\n    // Calculate the actual video display area (accounting for object-fit: cover)\n    const videoAspect = video.videoWidth / video.videoHeight;\n    const containerAspect = containerRect.width / containerRect.height;\n    let displayWidth, displayHeight, offsetX, offsetY;\n    if (videoAspect > containerAspect) {\n      // Video is wider - height fills container, width is cropped\n      displayHeight = containerRect.height;\n      displayWidth = displayHeight * videoAspect;\n      offsetX = (displayWidth - containerRect.width) / 2;\n      offsetY = 0;\n    } else {\n      // Video is taller - width fills container, height is cropped\n      displayWidth = containerRect.width;\n      displayHeight = displayWidth / videoAspect;\n      offsetX = 0;\n      offsetY = (displayHeight - containerRect.height) / 2;\n    }\n\n    // Calculate the guide areas in canvas coordinates\n    // SVG viewBox is 800x600\n    // Main rectangle: x=\"320\" y=\"150\" width=\"160\" height=\"300\" (wrist/forearm area)\n    // Circle: cx=\"400\" cy=\"300\" r=\"60\" (hand area)\n\n    const scaleX = canvas.width / displayWidth;\n    const scaleY = canvas.height / displayHeight;\n\n    // Wrist/forearm area (rectangle)\n    const rectX = Math.max(0, (320 / 800 * displayWidth - offsetX) * scaleX);\n    const rectY = Math.max(0, (150 / 600 * displayHeight - offsetY) * scaleY);\n    const rectWidth = Math.min(canvas.width - rectX, 160 / 800 * displayWidth * scaleX);\n    const rectHeight = Math.min(canvas.height - rectY, 300 / 600 * displayHeight * scaleY);\n\n    // Hand area (circle)\n    const circleX = Math.max(0, (340 / 800 * displayWidth - offsetX) * scaleX); // Adjusted for circle bounds\n    const circleY = Math.max(0, (240 / 600 * displayHeight - offsetY) * scaleY); // Adjusted for circle bounds\n    const circleWidth = Math.min(canvas.width - circleX, 120 / 800 * displayWidth * scaleX); // Circle diameter\n    const circleHeight = Math.min(canvas.height - circleY, 120 / 600 * displayHeight * scaleY); // Circle diameter\n\n    try {\n      // Check wrist/forearm area (rectangle)\n      const rectImageData = ctx.getImageData(rectX, rectY, rectWidth, rectHeight);\n      const rectData = rectImageData.data;\n\n      // Check hand area (circle approximation)\n      const circleImageData = ctx.getImageData(circleX, circleY, circleWidth, circleHeight);\n      const circleData = circleImageData.data;\n      let rectSkinPixels = 0;\n      let rectTotalPixels = 0;\n      let circleSkinPixels = 0;\n      let circleTotalPixels = 0;\n\n      // Enhanced skin tone detection\n      const isSkinTone = (r, g, b) => {\n        // Multiple skin tone ranges to cover different skin colors\n        const condition1 = r > 95 && g > 40 && b > 20 && r > g && r > b && Math.abs(r - g) > 15;\n        const condition2 = r > 220 && g > 210 && b > 170 && Math.abs(r - g) < 15 && Math.abs(g - b) < 15;\n        const condition3 = r > 120 && g > 80 && b > 50 && r > g && g > b;\n        const condition4 = r > 180 && g > 120 && b > 90 && r > g && g >= b;\n        return condition1 || condition2 || condition3 || condition4;\n      };\n\n      // Analyze rectangle area (wrist/forearm)\n      for (let i = 0; i < rectData.length; i += 4) {\n        const r = rectData[i];\n        const g = rectData[i + 1];\n        const b = rectData[i + 2];\n        if (isSkinTone(r, g, b)) {\n          rectSkinPixels++;\n        }\n        rectTotalPixels++;\n      }\n\n      // Analyze circle area (hand)\n      for (let i = 0; i < circleData.length; i += 4) {\n        const r = circleData[i];\n        const g = circleData[i + 1];\n        const b = circleData[i + 2];\n        if (isSkinTone(r, g, b)) {\n          circleSkinPixels++;\n        }\n        circleTotalPixels++;\n      }\n\n      // Calculate skin ratios\n      const rectSkinRatio = rectSkinPixels / rectTotalPixels;\n      const circleSkinRatio = circleSkinPixels / circleTotalPixels;\n\n      // Both wrist/forearm AND hand areas must have sufficient skin tone presence\n      // Wrist area needs at least 20% skin tone (forearm/wrist)\n      // Hand area needs at least 25% skin tone (hand/fingers)\n      const wristInPosition = rectSkinRatio > 0.20;\n      const handInPosition = circleSkinRatio > 0.25;\n      return wristInPosition && handInPosition;\n    } catch (error) {\n      console.warn('Hand detection error:', error);\n      return false;\n    }\n  };\n\n  // Apply product to watch position with dynamic scaling\n  const applyProductToWatchPosition = (productPath, productType) => {\n    // Clear any existing product first\n    setSelectedProduct(null);\n\n    // Find the watch data for dial size\n    const watchData = watches.find(w => w.path === productPath);\n\n    // Small delay to ensure the old product is removed before adding new one\n    setTimeout(() => {\n      setSelectedProduct({\n        path: productPath,\n        dialSize: (watchData === null || watchData === void 0 ? void 0 : watchData.dialSize) || 40 // Default to 40mm if not found\n      });\n    }, 50);\n  };\n\n  // Handle capture button click\n  const handleCapture = () => {\n    if (!isCaptured) {\n      const capturedDataUrl = captureFrame();\n      if (capturedImageRef.current && capturedDataUrl) {\n        capturedImageRef.current.src = capturedDataUrl;\n        capturedImageRef.current.style.display = 'block';\n      }\n      setIsCaptured(true);\n      setShowWristSizeInput(true); // Show wrist size input first\n      setShowHandGuide(false);\n\n      // Detect hand orientation\n      if (canvasRef.current && videoRef.current) {\n        const canvas = canvasRef.current;\n        const ctx = canvas.getContext('2d');\n        canvas.width = videoRef.current.videoWidth;\n        canvas.height = videoRef.current.videoHeight;\n        ctx.drawImage(videoRef.current, 0, 0);\n        const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);\n        setIsRightHand(detectHandOrientation(imageData));\n      }\n    } else {\n      setShowProductSelection(true);\n    }\n  };\n\n  // Handle back button click\n  const handleBack = () => {\n    if (capturedImageRef.current) {\n      capturedImageRef.current.style.display = 'none';\n    }\n    setIsCaptured(false);\n    setSelectedProduct(null);\n    setIsRightHand(false);\n    setShowProductSelection(false);\n    setShowWristSizeInput(false);\n    setShowHandGuide(true);\n  };\n\n  // Handle gender selection\n  const handleGenderChange = gender => {\n    setUserGender(gender);\n    setUserWristSize(DEFAULT_WRIST_SIZES[gender]);\n  };\n\n  // Handle wrist size change\n  const handleWristSizeChange = size => {\n    setUserWristSize(size);\n  };\n\n  // Handle continue to product selection\n  const handleContinueToProducts = () => {\n    setShowWristSizeInput(false);\n    setShowProductSelection(true);\n  };\n\n  // Handle tab change\n  const handleTabChange = tabName => {\n    setActiveTab(tabName);\n  };\n\n  // Handle product selection\n  const handleProductSelect = product => {\n    applyProductToWatchPosition(product.path, activeTab);\n  };\n\n  // Initialize camera on component mount\n  useEffect(() => {\n    initCamera();\n  }, []);\n\n  // Hand position monitoring effect (only when autocapture is enabled)\n  useEffect(() => {\n    if (!isAutoCaptureEnabled || isCaptured) return;\n    const interval = setInterval(() => {\n      const handInPosition = detectHandInPosition();\n      setIsHandInPosition(handInPosition);\n\n      // Only start countdown if hand is properly positioned\n      if (handInPosition && !isCountdownActive && countdown === 0) {\n        setIsCountdownActive(true);\n      }\n\n      // Stop countdown if hand moves out of position\n      if (!handInPosition && isCountdownActive) {\n        setIsCountdownActive(false);\n        setCountdown(0);\n      }\n    }, 150); // Check every 150ms for better performance\n\n    return () => clearInterval(interval);\n  }, [isAutoCaptureEnabled, isCaptured, isCountdownActive, countdown]);\n\n  // Countdown effect (starts automatically when hand is in position)\n  useEffect(() => {\n    if (!isCountdownActive || isCaptured) {\n      setCountdown(0);\n      return;\n    }\n\n    // Only start countdown if hand is still in position\n    if (!isHandInPosition) {\n      setIsCountdownActive(false);\n      setCountdown(0);\n      return;\n    }\n\n    // Start countdown\n    setCountdown(3);\n    const countdownInterval = setInterval(() => {\n      setCountdown(prev => {\n        // Double-check hand position before continuing countdown\n        if (!isHandInPosition) {\n          clearInterval(countdownInterval);\n          setIsCountdownActive(false);\n          return 0;\n        }\n        if (prev <= 1) {\n          // Countdown finished - trigger capture\n          clearInterval(countdownInterval);\n          setIsCountdownActive(false);\n          setIsAutoCaptureEnabled(false); // Turn off autocapture after capture\n          handleCapture();\n          return 0;\n        }\n        return prev - 1;\n      });\n    }, 1000);\n    return () => clearInterval(countdownInterval);\n  }, [isCountdownActive, isCaptured, isHandInPosition]);\n\n  // Handle autocapture switch toggle\n  const handleAutoCaptureToggle = () => {\n    const newState = !isAutoCaptureEnabled;\n    setIsAutoCaptureEnabled(newState);\n\n    // Reset states when turning off\n    if (!newState) {\n      setIsCountdownActive(false);\n      setCountdown(0);\n      setIsHandInPosition(false);\n    }\n  };\n\n  // Reset autocapture when going back\n  const handleBackWithReset = () => {\n    setIsAutoCaptureEnabled(false);\n    setIsCountdownActive(false);\n    setCountdown(0);\n    setIsHandInPosition(false);\n    setShowWristSizeInput(false);\n    setUserGender('men');\n    setUserWristSize(64);\n    handleBack();\n  };\n\n  // Get current products based on active tab\n  const getCurrentProducts = () => {\n    return activeTab === 'Watches' ? watches : bracelets;\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    style: styles.container,\n    className: isMobile ? 'mobile-container chrome-mobile-fix' : '',\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      style: styles.cameraContainer,\n      children: [/*#__PURE__*/_jsxDEV(\"video\", {\n        ref: videoRef,\n        style: styles.cameraFeed,\n        autoPlay: true,\n        playsInline: true,\n        muted: true\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 781,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"canvas\", {\n        ref: canvasRef,\n        style: {\n          display: 'none'\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 788,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"img\", {\n        ref: capturedImageRef,\n        style: styles.capturedImage,\n        alt: \"Captured hand\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 789,\n        columnNumber: 9\n      }, this), !isCaptured && /*#__PURE__*/_jsxDEV(\"button\", {\n        style: styles.homeBtn,\n        onClick: onBackToHome,\n        \"aria-label\": \"Home\",\n        children: \"\\u2190\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 797,\n        columnNumber: 11\n      }, this), !isCaptured && /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          position: 'absolute',\n          top: isMobile ? 10 : 20,\n          right: isMobile ? 10 : 20,\n          zIndex: 20,\n          display: 'flex',\n          flexDirection: 'column',\n          alignItems: 'center'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          style: {\n            color: '#2D8C88',\n            fontWeight: 700,\n            fontSize: isMobile ? 14 : 16,\n            marginBottom: 6,\n            letterSpacing: 0.5\n          },\n          children: \"Auto Capture\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 809,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n          className: \"switch\",\n          children: [/*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"checkbox\",\n            checked: isAutoCaptureEnabled,\n            onChange: handleAutoCaptureToggle,\n            disabled: isCountdownActive,\n            \"aria-label\": \"Toggle auto capture\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 811,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"slider\",\n            children: /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"circle\",\n              children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                className: \"checkmark\",\n                viewBox: \"0 0 12 10\",\n                children: /*#__PURE__*/_jsxDEV(\"polyline\", {\n                  points: \"1.5 6 5 9 10.5 1\",\n                  fill: \"none\",\n                  stroke: \"#2D8C88\",\n                  strokeWidth: \"2\",\n                  strokeLinecap: \"round\",\n                  strokeLinejoin: \"round\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 820,\n                  columnNumber: 66\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 820,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"svg\", {\n                className: \"cross\",\n                viewBox: \"0 0 10 10\",\n                children: [/*#__PURE__*/_jsxDEV(\"line\", {\n                  x1: \"1\",\n                  y1: \"1\",\n                  x2: \"9\",\n                  y2: \"9\",\n                  stroke: \"#838383\",\n                  strokeWidth: \"2\",\n                  strokeLinecap: \"round\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 821,\n                  columnNumber: 62\n                }, this), /*#__PURE__*/_jsxDEV(\"line\", {\n                  x1: \"9\",\n                  y1: \"1\",\n                  x2: \"1\",\n                  y2: \"9\",\n                  stroke: \"#838383\",\n                  strokeWidth: \"2\",\n                  strokeLinecap: \"round\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 821,\n                  columnNumber: 152\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 821,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 819,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 818,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 810,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 808,\n        columnNumber: 11\n      }, this), isCaptured && /*#__PURE__*/_jsxDEV(\"button\", {\n        style: styles.backBtn,\n        onClick: handleBackWithReset,\n        \"aria-label\": \"Back\",\n        children: \"\\u2190\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 830,\n        columnNumber: 11\n      }, this), isCountdownActive && /*#__PURE__*/_jsxDEV(\"div\", {\n        style: styles.countdownDisplay,\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: styles.countdownNumber,\n          children: countdown\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 842,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: styles.countdownText,\n          children: \"Auto capturing...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 843,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 841,\n        columnNumber: 11\n      }, this), isAutoCaptureEnabled && !isHandInPosition && !isCountdownActive && /*#__PURE__*/_jsxDEV(\"div\", {\n        style: styles.statusMessage,\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: styles.statusText,\n          children: \"Position your arm and wrist in the guide area\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 850,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: styles.statusSubtext,\n          children: \"Countdown will start automatically when detected\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 851,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 849,\n        columnNumber: 11\n      }, this), isAutoCaptureEnabled && isHandInPosition && !isCountdownActive && /*#__PURE__*/_jsxDEV(\"div\", {\n        style: styles.statusMessage,\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            ...styles.statusText,\n            backgroundColor: 'rgba(45, 140, 136, 0.9)'\n          },\n          children: \"Perfect! Starting countdown...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 857,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 856,\n        columnNumber: 11\n      }, this), showHandGuide && /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          ...styles.handGuide,\n          opacity: isAutoCaptureEnabled && isHandInPosition ? 0.9 : 0.8,\n          filter: isAutoCaptureEnabled && isHandInPosition ? 'drop-shadow(0 2px 8px rgba(45, 140, 136, 0.5))' : isAutoCaptureEnabled && !isHandInPosition ? 'drop-shadow(0 2px 8px rgba(255, 107, 107, 0.5))' : 'drop-shadow(0 2px 8px rgba(255, 255, 255, 0.3))'\n        },\n        className: isMobile ? 'mobile-hand-guide' : '',\n        \"aria-hidden\": \"true\",\n        children: /*#__PURE__*/_jsxDEV(\"svg\", {\n          viewBox: \"0 0 800 600\",\n          xmlns: \"http://www.w3.org/2000/svg\",\n          children: [/*#__PURE__*/_jsxDEV(\"path\", {\n            d: \"M100 480 C 150 460, 200 450, 250 450 L 550 450 C 600 450, 650 460, 700 480\",\n            stroke: isAutoCaptureEnabled && isHandInPosition ? \"#2D8C88\" : isAutoCaptureEnabled && !isHandInPosition ? \"#ff6b6b\" : \"white\",\n            strokeWidth: \"5\",\n            fill: \"none\",\n            strokeLinecap: \"round\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 880,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n            d: \"M100 120 C 150 140, 200 150, 250 150 L 550 150 C 600 150, 650 140, 700 120\",\n            stroke: isAutoCaptureEnabled && isHandInPosition ? \"#2D8C88\" : isAutoCaptureEnabled && !isHandInPosition ? \"#ff6b6b\" : \"white\",\n            strokeWidth: \"5\",\n            fill: \"none\",\n            strokeLinecap: \"round\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 893,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"rect\", {\n            x: \"320\",\n            y: \"150\",\n            width: \"160\",\n            height: \"300\",\n            fill: isAutoCaptureEnabled && isHandInPosition ? \"#2D8C88\" : isAutoCaptureEnabled && !isHandInPosition ? \"#ff6b6b\" : \"white\",\n            opacity: isAutoCaptureEnabled && isHandInPosition ? \"0.25\" : \"0.15\",\n            rx: \"15\",\n            stroke: isAutoCaptureEnabled && isHandInPosition ? \"#2D8C88\" : isAutoCaptureEnabled && !isHandInPosition ? \"#ff6b6b\" : \"white\",\n            strokeWidth: \"2\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 907,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"circle\", {\n            cx: \"400\",\n            cy: \"300\",\n            r: \"60\",\n            fill: isAutoCaptureEnabled && isHandInPosition ? \"#2D8C88\" : isAutoCaptureEnabled && !isHandInPosition ? \"#ff6b6b\" : \"white\",\n            opacity: isAutoCaptureEnabled && isHandInPosition ? \"0.3\" : \"0.2\",\n            stroke: isAutoCaptureEnabled && isHandInPosition ? \"#2D8C88\" : isAutoCaptureEnabled && !isHandInPosition ? \"#ff6b6b\" : \"white\",\n            strokeWidth: \"2\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 931,\n            columnNumber: 15\n          }, this), isAutoCaptureEnabled && /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: [/*#__PURE__*/_jsxDEV(\"text\", {\n              x: \"400\",\n              y: \"140\",\n              textAnchor: \"middle\",\n              fill: \"white\",\n              fontSize: \"14\",\n              fontWeight: \"bold\",\n              children: \"WRIST & FOREARM\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 955,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"text\", {\n              x: \"400\",\n              y: \"480\",\n              textAnchor: \"middle\",\n              fill: \"white\",\n              fontSize: \"14\",\n              fontWeight: \"bold\",\n              children: \"HAND\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 958,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 878,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 865,\n        columnNumber: 11\n      }, this), selectedProduct && /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          ...styles.productPosition,\n          width: activeTab === 'Watches' ? `${WATCH_WIDTH}%` : `${BRACELET_WIDTH}%`,\n          height: activeTab === 'Watches' ? `${WATCH_HEIGHT}%` : `${BRACELET_HEIGHT}%`\n        },\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            position: 'relative',\n            width: '100%',\n            height: '100%',\n            display: 'flex',\n            alignItems: 'center',\n            justifyContent: 'center'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"img\", {\n            src: typeof selectedProduct === 'object' ? selectedProduct.path : selectedProduct,\n            alt: \"Selected product\",\n            style: {\n              width: '100%',\n              height: '100%',\n              objectFit: 'contain',\n              transform: activeTab === 'Bracelets' ? `rotate(90deg) scale(${BRACELET_HEIGHT / 30})${isRightHand ? ' scaleX(-1)' : ''}` : `scale(${WATCH_HEIGHT / 25 * (userWristSize / DEFAULT_WRIST_SIZES[userGender])})`,\n              filter: 'drop-shadow(0 2px 8px rgba(0,0,0,0.3))'\n            },\n            onLoad: e => removeBackground(e.target, activeTab === 'Watches' ? 'watch' : 'bracelet')\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 982,\n            columnNumber: 15\n          }, this), activeTab === 'Watches' && typeof selectedProduct === 'object' && /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              position: 'absolute',\n              bottom: '-30px',\n              left: '50%',\n              transform: 'translateX(-50%)',\n              fontSize: '11px',\n              fontWeight: '600',\n              color: 'white',\n              backgroundColor: 'rgba(45, 140, 136, 0.9)',\n              padding: '3px 8px',\n              borderRadius: '12px',\n              whiteSpace: 'nowrap',\n              pointerEvents: 'none',\n              boxShadow: '0 2px 6px rgba(0,0,0,0.3)',\n              zIndex: 2\n            },\n            children: [selectedProduct.dialSize, \"mm\", userWristSize !== DEFAULT_WRIST_SIZES[userGender] && /*#__PURE__*/_jsxDEV(\"span\", {\n              style: {\n                fontSize: '10px',\n                opacity: 0.8,\n                marginLeft: '4px'\n              },\n              children: [\"(scaled \", ((userWristSize / DEFAULT_WRIST_SIZES[userGender] - 1) * 100).toFixed(0), \"%)\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1015,\n              columnNumber: 21\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 997,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 974,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 969,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        style: styles.captureBtn,\n        onClick: handleCapture,\n        \"aria-label\": isCaptured ? \"Select Products\" : \"Capture\",\n        children: !isCaptured ? /*#__PURE__*/_jsxDEV(\"div\", {\n          style: styles.captureInner\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1036,\n          columnNumber: 13\n        }, this) : /*#__PURE__*/_jsxDEV(\"svg\", {\n          width: \"24\",\n          height: \"24\",\n          viewBox: \"0 0 24 24\",\n          fill: \"white\",\n          children: /*#__PURE__*/_jsxDEV(\"path\", {\n            d: \"M8.59 16.59L13.17 12L8.59 7.41L10 6L16 12L10 18L8.59 16.59Z\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1039,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1038,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1030,\n        columnNumber: 9\n      }, this), isCaptured && /*#__PURE__*/_jsxDEV(\"button\", {\n        style: styles.resetBtn,\n        onClick: () => window.location.reload(),\n        \"aria-label\": \"Reset\",\n        children: /*#__PURE__*/_jsxDEV(\"svg\", {\n          width: \"20\",\n          height: \"20\",\n          viewBox: \"0 0 24 24\",\n          fill: \"white\",\n          children: /*#__PURE__*/_jsxDEV(\"path\", {\n            d: \"M17.65 6.35C16.2 4.9 14.21 4 12 4C7.58 4 4 7.58 4 12S7.58 20 12 20C15.73 20 18.84 17.45 19.73 14H17.65C16.83 16.33 14.61 18 12 18C8.69 18 6 15.31 6 12S8.69 6 12 6C13.66 6 15.14 6.69 16.22 7.78L13 11H20V4L17.65 6.35Z\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1052,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1051,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1046,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 780,\n      columnNumber: 7\n    }, this), showWristSizeInput && /*#__PURE__*/_jsxDEV(\"div\", {\n      style: styles.wristSizePanel,\n      className: isMobile ? 'mobile-wrist-panel' : '',\n      \"aria-modal\": \"true\",\n      role: \"dialog\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        style: styles.wristSizeContent,\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          style: styles.wristSizeTitle,\n          children: \"Customize Watch Size\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1067,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          style: styles.wristSizeSubtitle,\n          children: \"Select your gender and adjust wrist size for the perfect fit\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1068,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: styles.genderSelection,\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            style: {\n              ...styles.genderButton,\n              ...(userGender === 'men' ? styles.genderButtonActive : {})\n            },\n            onClick: () => handleGenderChange('men'),\n            children: \"Men (64mm)\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1072,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            style: {\n              ...styles.genderButton,\n              ...(userGender === 'women' ? styles.genderButtonActive : {})\n            },\n            onClick: () => handleGenderChange('women'),\n            children: \"Women (54mm)\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1081,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1071,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: styles.sliderContainer,\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            style: styles.sliderLabel,\n            children: [\"Wrist Size: \", userWristSize, \"mm\", userWristSize !== DEFAULT_WRIST_SIZES[userGender] && /*#__PURE__*/_jsxDEV(\"span\", {\n              style: styles.sizeChange,\n              children: [\"(\", userWristSize > DEFAULT_WRIST_SIZES[userGender] ? '+' : '', ((userWristSize / DEFAULT_WRIST_SIZES[userGender] - 1) * 100).toFixed(0), \"%)\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1097,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1094,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"range\",\n            min: \"45\",\n            max: \"80\",\n            value: userWristSize,\n            onChange: e => handleWristSizeChange(parseInt(e.target.value)),\n            style: styles.slider\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1103,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: styles.sliderLabels,\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              children: \"45mm\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1112,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              children: \"80mm\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1113,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1111,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1093,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          style: styles.continueButton,\n          onClick: handleContinueToProducts,\n          children: \"Continue to Products\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1118,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1066,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1060,\n      columnNumber: 9\n    }, this), showProductSelection && /*#__PURE__*/_jsxDEV(\"div\", {\n      style: styles.productSelection,\n      className: isMobile ? 'mobile-product-panel' : '',\n      \"aria-modal\": \"true\",\n      role: \"dialog\",\n      children: [/*#__PURE__*/_jsxDEV(\"button\", {\n        style: styles.closeBtn,\n        onClick: () => setShowProductSelection(false),\n        \"aria-label\": \"Close product selection\",\n        children: /*#__PURE__*/_jsxDEV(\"svg\", {\n          width: isMobile ? \"16\" : \"20\",\n          height: isMobile ? \"16\" : \"20\",\n          viewBox: \"0 0 24 24\",\n          fill: \"currentColor\",\n          children: /*#__PURE__*/_jsxDEV(\"path\", {\n            d: \"M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1142,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1141,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1136,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: styles.productTabs,\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          style: {\n            ...styles.tab,\n            ...(activeTab === 'Watches' ? styles.activeTab : {})\n          },\n          onClick: () => handleTabChange('Watches'),\n          children: \"Watches\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1146,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          style: {\n            ...styles.tab,\n            ...(activeTab === 'Bracelets' ? styles.activeTab : {})\n          },\n          onClick: () => handleTabChange('Bracelets'),\n          children: \"Bracelets\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1155,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1145,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: styles.productScroll,\n        className: \"product-scroll\",\n        children: getCurrentProducts().map((product, index) => {\n          // Simple null check only\n          if (!product) return null;\n          const isSelected = (typeof selectedProduct === 'object' ? selectedProduct === null || selectedProduct === void 0 ? void 0 : selectedProduct.path : selectedProduct) === product.path;\n          return /*#__PURE__*/_jsxDEV(\"button\", {\n            style: {\n              ...styles.productItem,\n              borderColor: isSelected ? '#2D8C88' : '#e9ecef',\n              backgroundColor: isSelected ? '#f0fffe' : '#ffffff'\n            },\n            title: `${product.name} - ${product.caseDiameter || 'N/A'}mm`,\n            onClick: () => handleProductSelect(product),\n            \"aria-label\": `Select ${product.name} ${product.caseDiameter || 'N/A'}mm`,\n            children: [/*#__PURE__*/_jsxDEV(\"img\", {\n              src: product.path,\n              alt: product.name,\n              style: styles.productImage,\n              onError: e => {\n                e.target.parentElement.style.display = 'none';\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1184,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: styles.productLabel,\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                style: styles.productName,\n                children: product.name\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1193,\n                columnNumber: 21\n              }, this), activeTab === 'Watches' && product.caseDiameter && /*#__PURE__*/_jsxDEV(\"div\", {\n                style: styles.productSize,\n                children: [product.caseDiameter, \"mm\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1195,\n                columnNumber: 23\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1192,\n              columnNumber: 19\n            }, this)]\n          }, index, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1173,\n            columnNumber: 17\n          }, this);\n        })\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1165,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1130,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 776,\n    columnNumber: 5\n  }, this);\n};\n\n// Styles object - Clean, modern design with improved mobile responsiveness\n_s(Tryon, \"PcbETJpqH60az3F+4Zwin4+qbB4=\");\n_c = Tryon;\nconst styles = {\n  container: {\n    position: 'relative',\n    height: 'calc(var(--vh, 1vh) * 100)',\n    display: 'flex',\n    flexDirection: 'column',\n    backgroundColor: '#f8f9fa',\n    color: '#333',\n    fontFamily: \"'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif\",\n    overflow: 'hidden',\n    touchAction: 'manipulation',\n    WebkitTapHighlightColor: 'transparent'\n  },\n  cameraContainer: {\n    flex: 1,\n    position: 'relative',\n    overflow: 'hidden',\n    backgroundColor: '#000',\n    display: 'flex',\n    alignItems: 'center',\n    justifyContent: 'center'\n  },\n  cameraFeed: {\n    width: '100%',\n    height: '100%',\n    objectFit: 'cover',\n    transform: 'scaleX(1)' // Fix for Safari mirroring\n  },\n  capturedImage: {\n    position: 'absolute',\n    top: 0,\n    left: 0,\n    width: '100%',\n    height: '100%',\n    objectFit: 'cover',\n    display: 'none',\n    WebkitTransform: 'scaleX(1)' // Fix for Safari mirroring\n  },\n  homeBtn: {\n    position: 'absolute',\n    top: '20px',\n    left: '20px',\n    backgroundColor: 'rgba(0, 0, 0, 0.6)',\n    color: 'white',\n    padding: '12px',\n    borderRadius: '50%',\n    fontSize: '20px',\n    fontWeight: '700',\n    cursor: 'pointer',\n    zIndex: 10,\n    border: 'none',\n    boxShadow: '0 2px 10px rgba(0, 0, 0, 0.3)',\n    transition: 'all 0.2s ease',\n    display: 'flex',\n    alignItems: 'center',\n    justifyContent: 'center',\n    width: '44px',\n    height: '44px',\n    outline: 'none'\n  },\n  backBtn: {\n    position: 'absolute',\n    top: '20px',\n    left: '20px',\n    backgroundColor: 'rgba(0, 0, 0, 0.6)',\n    color: 'white',\n    padding: '12px',\n    borderRadius: '50%',\n    fontSize: '20px',\n    fontWeight: '700',\n    cursor: 'pointer',\n    zIndex: 10,\n    border: 'none',\n    boxShadow: '0 2px 10px rgba(0, 0, 0, 0.3)',\n    transition: 'all 0.2s ease',\n    display: 'flex',\n    alignItems: 'center',\n    justifyContent: 'center',\n    width: '44px',\n    height: '44px',\n    outline: 'none'\n  },\n  switchContainer: {\n    position: 'absolute',\n    top: '20px',\n    right: '20px',\n    zIndex: 10,\n    display: 'flex',\n    flexDirection: 'column',\n    alignItems: 'center',\n    gap: '4px',\n    padding: '12px',\n    backgroundColor: 'rgba(0, 0, 0, 0.5)',\n    borderRadius: '20px',\n    backdropFilter: 'blur(10px)',\n    WebkitBackdropFilter: 'blur(10px)',\n    boxShadow: '0 4px 12px rgba(0, 0, 0, 0.2)',\n    border: '1px solid rgba(255, 255, 255, 0.1)'\n  },\n  switchTrack: {\n    position: 'absolute',\n    top: '12px',\n    left: '12px',\n    width: '60px',\n    height: '32px',\n    backgroundColor: 'rgba(0, 0, 0, 0.3)',\n    borderRadius: '16px',\n    border: '2px solid rgba(255, 255, 255, 0.2)',\n    zIndex: 9,\n    transition: 'all 0.3s ease'\n  },\n  switchButton: {\n    position: 'relative',\n    width: '28px',\n    height: '28px',\n    borderRadius: '50%',\n    border: 'none',\n    cursor: 'pointer',\n    transition: 'all 0.3s ease',\n    display: 'flex',\n    alignItems: 'center',\n    justifyContent: 'center',\n    zIndex: 11,\n    margin: '2px',\n    boxShadow: '0 2px 8px rgba(0, 0, 0, 0.3)',\n    outline: 'none',\n    '&:hover': {\n      transform: 'scale(1.1)'\n    }\n  },\n  switchLabel: {\n    fontSize: '12px',\n    fontWeight: '700',\n    color: 'white',\n    textShadow: '0 1px 2px rgba(0, 0, 0, 0.7)',\n    marginTop: '4px',\n    padding: '4px 12px',\n    borderRadius: '12px',\n    backgroundColor: 'rgba(0, 0, 0, 0.3)',\n    letterSpacing: '0.5px'\n  },\n  countdownDisplay: {\n    position: 'absolute',\n    top: '35%',\n    left: '50%',\n    transform: 'translate(-50%, -50%)',\n    zIndex: 15,\n    textAlign: 'center',\n    pointerEvents: 'none',\n    padding: '16px 24px',\n    backgroundColor: 'rgba(0, 0, 0, 0.4)',\n    borderRadius: '20px',\n    backdropFilter: 'blur(10px)',\n    WebkitBackdropFilter: 'blur(10px)',\n    boxShadow: '0 4px 20px rgba(0, 0, 0, 0.3)'\n  },\n  countdownNumber: {\n    fontSize: '72px',\n    fontWeight: '900',\n    color: '#2D8C88',\n    textShadow: '0 2px 8px rgba(0, 0, 0, 0.5)',\n    marginBottom: '8px',\n    animation: 'pulse 1s ease-in-out'\n  },\n  countdownText: {\n    fontSize: '16px',\n    fontWeight: '600',\n    color: 'white',\n    textShadow: '0 1px 4px rgba(0, 0, 0, 0.7)'\n  },\n  statusMessage: {\n    position: 'absolute',\n    top: '25%',\n    left: '50%',\n    transform: 'translate(-50%, -50%)',\n    zIndex: 15,\n    textAlign: 'center',\n    pointerEvents: 'none',\n    padding: '12px 20px',\n    borderRadius: '16px',\n    backgroundColor: 'rgba(0, 0, 0, 0.4)',\n    backdropFilter: 'blur(10px)',\n    WebkitBackdropFilter: 'blur(10px)',\n    boxShadow: '0 4px 20px rgba(0, 0, 0, 0.3)'\n  },\n  statusText: {\n    fontSize: '16px',\n    fontWeight: '600',\n    color: 'white',\n    textShadow: '0 1px 4px rgba(0, 0, 0, 0.7)',\n    backgroundColor: 'rgba(255, 107, 107, 0.9)',\n    padding: '12px 20px',\n    borderRadius: '25px',\n    marginBottom: '8px',\n    transition: 'all 0.3s ease'\n  },\n  statusSubtext: {\n    fontSize: '12px',\n    fontWeight: '500',\n    color: 'white',\n    textShadow: '0 1px 4px rgba(0, 0, 0, 0.7)',\n    backgroundColor: 'rgba(0, 0, 0, 0.6)',\n    padding: '6px 12px',\n    borderRadius: '15px'\n  },\n  handGuide: {\n    position: 'absolute',\n    top: '50%',\n    left: '50%',\n    transform: 'translate(-50%, -50%)',\n    width: '80%',\n    maxWidth: '400px',\n    height: 'auto',\n    opacity: 0.8,\n    pointerEvents: 'none',\n    zIndex: 5,\n    filter: 'drop-shadow(0 2px 8px rgba(255, 255, 255, 0.3))',\n    WebkitFilter: 'drop-shadow(0 2px 8px rgba(255, 255, 255, 0.3))',\n    transition: 'all 0.3s ease'\n  },\n  productPosition: {\n    position: 'absolute',\n    top: '50%',\n    left: '50%',\n    transform: 'translate(-50%, -50%)',\n    zIndex: 8,\n    display: 'flex',\n    alignItems: 'center',\n    justifyContent: 'center',\n    pointerEvents: 'none',\n    minWidth: '100px',\n    // Ensure minimum size\n    minHeight: '100px' // Ensure minimum size\n  },\n  captureBtn: {\n    position: 'absolute',\n    bottom: '30px',\n    left: '50%',\n    transform: 'translateX(-50%)',\n    width: '70px',\n    height: '70px',\n    backgroundColor: 'rgba(255, 255, 255, 0.9)',\n    borderRadius: '50%',\n    display: 'flex',\n    alignItems: 'center',\n    justifyContent: 'center',\n    cursor: 'pointer',\n    zIndex: 10,\n    transition: 'all 0.2s ease',\n    border: '4px solid rgba(255, 255, 255, 0.3)',\n    boxShadow: '0 4px 20px rgba(0, 0, 0, 0.3)',\n    outline: 'none',\n    padding: 0\n  },\n  captureInner: {\n    width: '50px',\n    height: '50px',\n    backgroundColor: '#2D8C88',\n    borderRadius: '50%',\n    transition: 'all 0.2s ease'\n  },\n  resetBtn: {\n    position: 'absolute',\n    bottom: '30px',\n    right: '30px',\n    width: '50px',\n    height: '50px',\n    backgroundColor: 'rgba(0, 0, 0, 0.6)',\n    borderRadius: '50%',\n    display: 'flex',\n    alignItems: 'center',\n    justifyContent: 'center',\n    cursor: 'pointer',\n    zIndex: 10,\n    transition: 'all 0.2s ease',\n    border: 'none',\n    boxShadow: '0 2px 10px rgba(0, 0, 0, 0.3)',\n    outline: 'none',\n    padding: 0\n  },\n  // Wrist Size Input Panel Styles\n  wristSizePanel: {\n    position: 'absolute',\n    bottom: 0,\n    left: 0,\n    right: 0,\n    backgroundColor: 'rgba(255, 255, 255, 0.95)',\n    backdropFilter: 'blur(20px)',\n    WebkitBackdropFilter: 'blur(20px)',\n    borderTopLeftRadius: '20px',\n    borderTopRightRadius: '20px',\n    padding: '24px',\n    maxHeight: '70vh',\n    display: 'flex',\n    flexDirection: 'column',\n    zIndex: 25,\n    boxShadow: '0 -4px 20px rgba(0, 0, 0, 0.15)',\n    border: 'none'\n  },\n  wristSizeContent: {\n    display: 'flex',\n    flexDirection: 'column',\n    alignItems: 'center',\n    gap: '20px'\n  },\n  wristSizeTitle: {\n    fontSize: '20px',\n    fontWeight: '700',\n    color: '#2D8C88',\n    margin: 0,\n    textAlign: 'center'\n  },\n  wristSizeSubtitle: {\n    fontSize: '14px',\n    color: '#666',\n    margin: 0,\n    textAlign: 'center',\n    lineHeight: '1.4'\n  },\n  genderSelection: {\n    display: 'flex',\n    gap: '12px',\n    width: '100%',\n    maxWidth: '300px'\n  },\n  genderButton: {\n    flex: 1,\n    padding: '12px 16px',\n    borderRadius: '10px',\n    fontSize: '14px',\n    fontWeight: '600',\n    cursor: 'pointer',\n    transition: 'all 0.2s ease',\n    border: '2px solid #e0e0e0',\n    backgroundColor: '#ffffff',\n    color: '#666',\n    outline: 'none'\n  },\n  genderButtonActive: {\n    backgroundColor: '#2D8C88',\n    color: '#ffffff',\n    borderColor: '#2D8C88',\n    boxShadow: '0 2px 8px rgba(45, 140, 136, 0.3)'\n  },\n  sliderContainer: {\n    width: '100%',\n    maxWidth: '300px',\n    display: 'flex',\n    flexDirection: 'column',\n    gap: '8px'\n  },\n  sliderLabel: {\n    fontSize: '16px',\n    fontWeight: '600',\n    color: '#333',\n    textAlign: 'center',\n    display: 'flex',\n    alignItems: 'center',\n    justifyContent: 'center',\n    gap: '8px'\n  },\n  sizeChange: {\n    fontSize: '14px',\n    fontWeight: '500',\n    color: '#2D8C88',\n    backgroundColor: 'rgba(45, 140, 136, 0.1)',\n    padding: '2px 8px',\n    borderRadius: '12px'\n  },\n  slider: {\n    width: '100%',\n    height: '6px',\n    borderRadius: '3px',\n    background: '#e0e0e0',\n    outline: 'none',\n    cursor: 'pointer',\n    WebkitAppearance: 'none',\n    appearance: 'none'\n  },\n  sliderLabels: {\n    display: 'flex',\n    justifyContent: 'space-between',\n    fontSize: '12px',\n    color: '#999',\n    marginTop: '4px'\n  },\n  continueButton: {\n    width: '100%',\n    maxWidth: '300px',\n    padding: '14px 24px',\n    backgroundColor: '#2D8C88',\n    color: '#ffffff',\n    borderRadius: '12px',\n    fontSize: '16px',\n    fontWeight: '600',\n    cursor: 'pointer',\n    transition: 'all 0.2s ease',\n    border: 'none',\n    outline: 'none',\n    boxShadow: '0 4px 12px rgba(45, 140, 136, 0.3)'\n  },\n  productSelection: {\n    position: 'absolute',\n    bottom: 0,\n    left: 0,\n    right: 0,\n    backgroundColor: 'rgba(255, 255, 255, 0.95)',\n    backdropFilter: 'blur(20px)',\n    WebkitBackdropFilter: 'blur(20px)',\n    borderTopLeftRadius: '20px',\n    borderTopRightRadius: '20px',\n    padding: '16px',\n    maxHeight: '70vh',\n    display: 'flex',\n    flexDirection: 'column',\n    zIndex: 20,\n    boxShadow: '0 -4px 20px rgba(0, 0, 0, 0.15)',\n    border: 'none'\n  },\n  closeBtn: {\n    position: 'absolute',\n    top: '12px',\n    right: '16px',\n    color: '#666',\n    cursor: 'pointer',\n    zIndex: 21,\n    width: '32px',\n    height: '32px',\n    display: 'flex',\n    alignItems: 'center',\n    justifyContent: 'center',\n    borderRadius: '50%',\n    backgroundColor: 'rgba(0, 0, 0, 0.1)',\n    transition: 'all 0.2s ease',\n    border: 'none',\n    outline: 'none',\n    padding: 0\n  },\n  productTabs: {\n    display: 'flex',\n    marginBottom: '16px',\n    marginTop: '8px',\n    backgroundColor: '#f0f0f0',\n    borderRadius: '10px',\n    padding: '3px'\n  },\n  tab: {\n    flex: 1,\n    textAlign: 'center',\n    padding: '10px 16px',\n    borderRadius: '7px',\n    fontSize: '15px',\n    fontWeight: '600',\n    cursor: 'pointer',\n    transition: 'all 0.2s ease',\n    color: '#666',\n    outline: 'none',\n    border: 'none',\n    backgroundColor: 'transparent'\n  },\n  activeTab: {\n    backgroundColor: '#2D8C88',\n    color: '#ffffff',\n    boxShadow: '0 1px 4px rgba(45, 140, 136, 0.3)'\n  },\n  productScroll: {\n    display: 'grid',\n    gridTemplateColumns: 'repeat(auto-fill, minmax(85px, 1fr))',\n    gap: '10px',\n    maxHeight: 'calc(70vh - 80px)',\n    overflowY: 'auto',\n    paddingBottom: '8px',\n    scrollbarWidth: 'thin',\n    scrollbarColor: '#ddd #f5f5f5'\n  },\n  productItem: {\n    position: 'relative',\n    width: '100%',\n    aspectRatio: '1/1',\n    backgroundColor: '#ffffff',\n    borderRadius: '12px',\n    display: 'flex',\n    alignItems: 'center',\n    justifyContent: 'center',\n    cursor: 'pointer',\n    transition: 'all 0.2s ease',\n    border: '1px solid #e0e0e0',\n    overflow: 'hidden',\n    boxShadow: '0 1px 4px rgba(0, 0, 0, 0.1)',\n    padding: '6px',\n    outline: 'none'\n  },\n  productImage: {\n    width: '100%',\n    height: '100%',\n    objectFit: 'contain',\n    borderRadius: '8px',\n    backgroundColor: '#ffffff'\n  },\n  productLabel: {\n    position: 'absolute',\n    bottom: '3px',\n    left: '3px',\n    right: '3px',\n    fontSize: '9px',\n    color: '#666',\n    textAlign: 'center',\n    backgroundColor: 'rgba(255, 255, 255, 0.95)',\n    borderRadius: '3px',\n    padding: '3px 2px',\n    overflow: 'hidden'\n  },\n  productName: {\n    fontSize: '9px',\n    fontWeight: '600',\n    whiteSpace: 'nowrap',\n    textOverflow: 'ellipsis',\n    overflow: 'hidden',\n    marginBottom: '1px'\n  },\n  productSize: {\n    fontSize: '8px',\n    fontWeight: '500',\n    color: '#2D8C88',\n    whiteSpace: 'nowrap'\n  }\n};\nexport default Tryon;\nvar _c;\n$RefreshReg$(_c, \"Tryon\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useRef", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "sliderCSS", "document", "styleElement", "createElement", "textContent", "head", "append<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "onBackToHome", "_s", "videoRef", "capturedImageRef", "canvasRef", "isCaptured", "setIsCaptured", "selectedProduct", "setSelectedProduct", "isRightHand", "setIsRightHand", "showProductSelection", "setShowProductSelection", "activeTab", "setActiveTab", "showHandGuide", "setShowHandGuide", "isMobile", "setIsMobile", "userGender", "setUserGender", "userWristSize", "setUserWristSize", "showWristSizeInput", "setShowWristSizeInput", "isAutoCaptureEnabled", "setIsAutoCaptureEnabled", "countdown", "setCountdown", "isHandInPosition", "setIsHandInPosition", "isCountdownActive", "setIsCountdownActive", "checkMobile", "window", "innerWidth", "setVH", "vh", "innerHeight", "documentElement", "style", "setProperty", "addEventListener", "setTimeout", "removeEventListener", "DEFAULT_WRIST_SIZES", "men", "women", "SVG_WRIST_CIRCLE_DIAMETER", "SVG_VIEWBOX_WIDTH", "SVG_VIEWBOX_HEIGHT", "WATCH_WIDTH", "BRACELET_WIDTH", "WATCH_HEIGHT", "BRACELET_HEIGHT", "calculateWatchDimensions", "watch", "containerWidth", "containerHeight", "defaultWristSize", "wristSizeRatio", "mmToSvgScale", "watchWidthSvg", "totalWidth", "watchHeightSvg", "totalHeight", "dialDiameterSvg", "dialDiameter", "watchWidthPercent", "watchHeightPercent", "dialDiameterPercent", "positionX", "positionY", "width", "Math", "max", "height", "scale", "min", "realWidth", "realHeight", "caseDiameter", "getWatchPosition", "watchData", "baseDimensions", "adjustedX", "adjustedY", "type", "watches", "name", "path", "caseThickness", "dialSize", "bracelets", "initCamera", "stream", "navigator", "mediaDevices", "getUserMedia", "video", "facingMode", "ideal", "current", "srcObject", "err", "console", "error", "src", "display", "log", "<PERSON><PERSON><PERSON><PERSON>", "canvas", "videoWidth", "videoHeight", "ctx", "getContext", "drawImage", "toDataURL", "removeBackground", "imgElement", "productType", "img", "Image", "crossOrigin", "onload", "naturalWidth", "naturalHeight", "imageData", "getImageData", "data", "edgePixels", "Set", "y", "x", "idx", "r", "g", "b", "isEdge", "dy", "dx", "neighborIdx", "nr", "ng", "nb", "colorDiff", "abs", "add", "i", "length", "pixelIndex", "brightness", "isNearEdge", "has", "isPureWhite", "putImageData", "filter", "mixBlendMode", "opacity", "e", "warn", "onerror", "detectHandOrientation", "random", "detectHandInPosition", "videoContainer", "parentElement", "containerRect", "getBoundingClientRect", "videoAspect", "containerAspect", "displayWidth", "displayHeight", "offsetX", "offsetY", "scaleX", "scaleY", "rectX", "rectY", "rectWidth", "rectHeight", "circleX", "circleY", "circleWidth", "circleHeight", "rectImageData", "rectData", "circleImageData", "circleData", "rectSkinPixels", "rectTotalPixels", "circleSkinPixels", "circleTotalPixels", "isSkinTone", "condition1", "condition2", "condition3", "condition4", "rectSkinRatio", "circleSkinRatio", "wristInPosition", "handInPosition", "applyProductToWatchPosition", "productPath", "find", "w", "handleCapture", "capturedDataUrl", "handleBack", "handleGenderChange", "gender", "handleWristSizeChange", "size", "handleContinueToProducts", "handleTabChange", "tabName", "handleProductSelect", "product", "interval", "setInterval", "clearInterval", "countdownInterval", "prev", "handleAutoCaptureToggle", "newState", "handleBackWithReset", "getCurrentProducts", "styles", "container", "className", "children", "cameraContainer", "ref", "cameraFeed", "autoPlay", "playsInline", "muted", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "capturedImage", "alt", "homeBtn", "onClick", "position", "top", "right", "zIndex", "flexDirection", "alignItems", "color", "fontWeight", "fontSize", "marginBottom", "letterSpacing", "checked", "onChange", "disabled", "viewBox", "points", "fill", "stroke", "strokeWidth", "strokeLinecap", "strokeLinejoin", "x1", "y1", "x2", "y2", "backBtn", "countdownDisplay", "countdownNumber", "countdownText", "statusMessage", "statusText", "statusSubtext", "backgroundColor", "handGuide", "xmlns", "d", "rx", "cx", "cy", "textAnchor", "productPosition", "justifyContent", "objectFit", "transform", "onLoad", "target", "bottom", "left", "padding", "borderRadius", "whiteSpace", "pointerEvents", "boxShadow", "marginLeft", "toFixed", "captureBtn", "captureInner", "resetBtn", "location", "reload", "wristSizePanel", "role", "wristSizeContent", "wristSizeTitle", "wristSizeSubtitle", "genderSelection", "genderButton", "genderButtonActive", "slide<PERSON><PERSON><PERSON><PERSON>", "slider<PERSON><PERSON><PERSON>", "sizeChange", "value", "parseInt", "slider", "slider<PERSON><PERSON><PERSON>", "continueButton", "productSelection", "closeBtn", "productTabs", "tab", "productScroll", "map", "index", "isSelected", "productItem", "borderColor", "title", "productImage", "onError", "productLabel", "productName", "productSize", "_c", "fontFamily", "overflow", "touchAction", "WebkitTapHighlightColor", "flex", "WebkitTransform", "cursor", "border", "transition", "outline", "switchContainer", "gap", "<PERSON><PERSON>ilter", "WebkitBackdropFilter", "switchTrack", "switchButton", "margin", "switchLabel", "textShadow", "marginTop", "textAlign", "animation", "max<PERSON><PERSON><PERSON>", "WebkitFilter", "min<PERSON><PERSON><PERSON>", "minHeight", "borderTopLeftRadius", "borderTopRightRadius", "maxHeight", "lineHeight", "background", "WebkitAppearance", "appearance", "gridTemplateColumns", "overflowY", "paddingBottom", "scrollbarWidth", "scrollbarColor", "aspectRatio", "textOverflow", "$RefreshReg$"], "sources": ["D:/Via/New folder/v2/src/pages/VirtualTryOn.jsx"], "sourcesContent": ["import React, { useState, useEffect, useRef } from 'react';\n\n// Add CSS for range slider styling\nconst sliderCSS = `\n  input[type=\"range\"]::-webkit-slider-thumb {\n    -webkit-appearance: none;\n    appearance: none;\n    width: 20px;\n    height: 20px;\n    border-radius: 50%;\n    background: #2D8C88;\n    cursor: pointer;\n    box-shadow: 0 2px 6px rgba(45, 140, 136, 0.3);\n  }\n\n  input[type=\"range\"]::-moz-range-thumb {\n    width: 20px;\n    height: 20px;\n    border-radius: 50%;\n    background: #2D8C88;\n    cursor: pointer;\n    border: none;\n    box-shadow: 0 2px 6px rgba(45, 140, 136, 0.3);\n  }\n\n  input[type=\"range\"]::-webkit-slider-track {\n    background: #e0e0e0;\n    height: 6px;\n    border-radius: 3px;\n  }\n\n  input[type=\"range\"]::-moz-range-track {\n    background: #e0e0e0;\n    height: 6px;\n    border-radius: 3px;\n    border: none;\n  }\n\n  input[type=\"range\"]:focus {\n    outline: none;\n  }\n\n  input[type=\"range\"]:focus::-webkit-slider-thumb {\n    box-shadow: 0 0 0 3px rgba(45, 140, 136, 0.2);\n  }\n\n  input[type=\"range\"]:focus::-moz-range-thumb {\n    box-shadow: 0 0 0 3px rgba(45, 140, 136, 0.2);\n  }\n`;\n\n// Inject CSS\nif (typeof document !== 'undefined') {\n  const styleElement = document.createElement('style');\n  styleElement.textContent = sliderCSS;\n  document.head.appendChild(styleElement);\n}\n\nconst Tryon = ({ onBackToHome }) => {\n  // Refs for DOM elements\n  const videoRef = useRef(null);\n  const capturedImageRef = useRef(null);\n  const canvasRef = useRef(null);\n\n  // State variables\n  const [isCaptured, setIsCaptured] = useState(false);\n  const [selectedProduct, setSelectedProduct] = useState(null);\n  const [isRightHand, setIsRightHand] = useState(false);\n  const [showProductSelection, setShowProductSelection] = useState(false);\n  const [activeTab, setActiveTab] = useState('Watches');\n  const [showHandGuide, setShowHandGuide] = useState(true);\n  const [isMobile, setIsMobile] = useState(false);\n\n  // Watch size customization state\n  const [userGender, setUserGender] = useState('men'); // 'men' or 'women'\n  const [userWristSize, setUserWristSize] = useState(64); // Default men's wrist size in mm\n  const [showWristSizeInput, setShowWristSizeInput] = useState(false);\n\n  // Autocapture state variables\n  const [isAutoCaptureEnabled, setIsAutoCaptureEnabled] = useState(false);\n  const [countdown, setCountdown] = useState(0);\n  const [isHandInPosition, setIsHandInPosition] = useState(false);\n  const [isCountdownActive, setIsCountdownActive] = useState(false);\n\n  // Detect mobile device and set viewport height for Chrome mobile\n  useEffect(() => {\n    const checkMobile = () => {\n      setIsMobile(window.innerWidth <= 768);\n    };\n\n    // Fix viewport height for Chrome mobile\n    const setVH = () => {\n      const vh = window.innerHeight * 0.01;\n      document.documentElement.style.setProperty('--vh', `${vh}px`);\n    };\n\n    checkMobile();\n    setVH();\n\n    window.addEventListener('resize', () => {\n      checkMobile();\n      setVH();\n    });\n\n    window.addEventListener('orientationchange', () => {\n      setTimeout(() => {\n        setVH();\n      }, 100);\n    });\n\n    return () => {\n      window.removeEventListener('resize', checkMobile);\n      window.removeEventListener('orientationchange', setVH);\n    };\n  }, []);\n\n  // Realistic sizing configuration\n  // Average adult wrist circumference: 165mm (men), 155mm (women)\n  // Average wrist width when viewed from top: ~55-65mm\n  // SVG guide circle represents the wrist area (120px diameter in 800px viewBox)\n  // This means the circle represents approximately 60mm in real life\n\n  // Default wrist sizes by gender\n  const DEFAULT_WRIST_SIZES = {\n    men: 64,    // mm - default men's wrist size\n    women: 54   // mm - default women's wrist size\n  };\n\n  const SVG_WRIST_CIRCLE_DIAMETER = 120; // SVG circle diameter in viewBox units\n  const SVG_VIEWBOX_WIDTH = 800; // SVG viewBox width\n  const SVG_VIEWBOX_HEIGHT = 600; // SVG viewBox height\n\n  // Size configuration - Increased for better visibility\n  const WATCH_WIDTH = 25; // percentage of container width\n  const BRACELET_WIDTH = 15; // percentage of container width\n  const WATCH_HEIGHT = 38; // percentage of container height\n  const BRACELET_HEIGHT = 55; // percentage of container height - INCREASED for better visibility\n\n  // Test: Try changing WATCH_HEIGHT to 50 to see a much taller watch\n  // Test: Try changing WATCH_HEIGHT to 15 to see a shorter watch\n  // Test: Try changing BRACELET_HEIGHT to 70 to see a much taller bracelet\n  // Test: Try changing BRACELET_HEIGHT to 20 to see a shorter bracelet\n  // Note: Scale is now dynamic - smaller height = smaller scale, larger height = larger scale\n\n  // Calculate realistic watch sizing based on actual dimensions and user's wrist size\n  const calculateWatchDimensions = (watch, containerWidth, containerHeight) => {\n    // Get the default wrist size for the current gender\n    const defaultWristSize = DEFAULT_WRIST_SIZES[userGender];\n\n    // Calculate relative scaling factor based on user's wrist size vs default\n    const wristSizeRatio = userWristSize / defaultWristSize;\n\n    // Calculate the scale factor from real world to SVG coordinates using user's wrist size\n    const mmToSvgScale = SVG_WRIST_CIRCLE_DIAMETER / userWristSize;\n\n    // Convert watch dimensions to SVG units\n    const watchWidthSvg = watch.totalWidth * mmToSvgScale;\n    const watchHeightSvg = watch.totalHeight * mmToSvgScale;\n    const dialDiameterSvg = watch.dialDiameter * mmToSvgScale;\n\n    // Convert SVG units to container percentages\n    const watchWidthPercent = (watchWidthSvg / SVG_VIEWBOX_WIDTH) * 100;\n    const watchHeightPercent = (watchHeightSvg / SVG_VIEWBOX_HEIGHT) * 100;\n    const dialDiameterPercent = (dialDiameterSvg / SVG_VIEWBOX_WIDTH) * 100;\n\n    // Calculate positioning - watches should be positioned on the wrist circle\n    // SVG circle is at cx=\"400\" cy=\"300\" (center of the hand area)\n    const positionX = 50; // Center horizontally (400/800 = 50%)\n    const positionY = 50; // Center vertically (300/600 = 50%)\n\n    return {\n      width: Math.max(watchWidthPercent, 8), // Minimum 8% for visibility\n      height: Math.max(watchHeightPercent, 10), // Minimum 10% for visibility\n      dialDiameter: dialDiameterPercent,\n      positionX,\n      positionY,\n      scale: Math.min(watchWidthPercent / 15, watchHeightPercent / 20) * wristSizeRatio, // Dynamic scale with wrist size adjustment\n      realWidth: watch.totalWidth,\n      realHeight: watch.totalHeight,\n      caseDiameter: watch.caseDiameter,\n      wristSizeRatio // Include ratio for additional scaling if needed\n    };\n  };\n\n  // Calculate watch position based on hand orientation and anatomy\n  const getWatchPosition = (watchData, isRightHand) => {\n    const baseDimensions = calculateWatchDimensions(watchData, 400, 600);\n\n    // Adjust position based on hand orientation\n    // Watches are typically worn on the top of the wrist\n    let adjustedX = baseDimensions.positionX;\n    let adjustedY = baseDimensions.positionY - 2; // Slightly higher on the wrist\n\n    // For right hand, watch might be positioned slightly differently\n    if (isRightHand) {\n      adjustedX = baseDimensions.positionX + 1; // Slight adjustment for right hand\n    }\n\n    // Adjust for different watch types\n    switch (watchData.type) {\n      case 'smartwatch':\n        // Smart watches are often worn higher on the wrist\n        adjustedY -= 1;\n        break;\n      case 'luxury':\n        // Luxury watches might be positioned more precisely\n        adjustedY -= 0.5;\n        break;\n      case 'sport':\n        // Sport watches might be worn slightly looser\n        adjustedY += 0.5;\n        break;\n      default:\n        break;\n    }\n\n    return {\n      ...baseDimensions,\n      positionX: adjustedX,\n      positionY: adjustedY\n    };\n  };\n  // Realistic watch data with actual dimensions (in millimeters)\n  const watches = [\n    {\n      name: \"Classic Black\",\n      path: \"watches/watch_1.png\",\n      // Rolex Submariner style - 40mm case\n      caseDiameter: 41, // mm\n      caseThickness: 12.5, // mm\n      totalWidth: 42, // mm (including crown)\n      totalHeight: 47, // mm (lug to lug)\n      dialDiameter: 31, // mm (visible dial)\n      type: \"dress\",\n      dialSize: 40\n    },\n    {\n      name: \"Silver Chrono\",\n      path: \"watches/watch_2.png\",\n      // Omega Speedmaster style - 42mm case\n      caseDiameter: 42, // mm\n      caseThickness: 13.2, // mm\n      totalWidth: 44, // mm\n      totalHeight: 48.5, // mm\n      dialDiameter: 33, // mm\n      type: \"sport\",\n      dialSize: 42\n    },\n    {\n      name: \"Gold Luxury\",\n      path: \"watches/watch_3.png\",\n      // Patek Philippe Calatrava style - 38mm case\n      caseDiameter: 39, // mm\n      caseThickness: 8.5, // mm\n      totalWidth: 39, // mm\n      totalHeight: 45, // mm\n      dialDiameter: 30, // mm\n      type: \"luxury\",\n      dialSize: 38\n    },\n    {\n      name: \"Sport Blue\",\n      path: \"watches/watch_6.png\",\n      // Apple Watch style - 44mm case\n      caseDiameter: 41, // mm (width)\n      caseThickness: 10.7, // mm\n      totalWidth: 44, // mm\n      totalHeight: 38, // mm (height - rectangular)\n      dialDiameter: 35, // mm (screen diagonal)\n      type: \"smartwatch\",\n      dialSize: 44\n    },\n    {\n      name: \"Minimalist\",\n      path: \"watches/watch_5.png\",\n      // Daniel Wellington style - 36mm case\n      caseDiameter: 36, // mm\n      caseThickness: 6, // mm\n      totalWidth: 37, // mm\n      totalHeight: 43, // mm\n      dialDiameter: 28, // mm\n      type: \"minimalist\",\n      dialSize: 36\n    },\n    {\n      name: \"Rose Gold\",\n      path: \"watches/watch_4.png\",\n      // Michael Kors style - 39mm case\n      caseDiameter: 44, // mm\n      caseThickness: 11, // mm\n      totalWidth: 41, // mm\n      totalHeight: 46, // mm\n      dialDiameter: 31, // mm\n      type: \"fashion\",\n      dialSize: 41\n    }\n  ];\n\n  const bracelets = [\n    { name: \"Silver Chain\", path: \"bracelets/bracelet_1.png\" },\n    { name: \"Gold Bangle\", path: \"bracelets/bracelet_2.png\" },\n    { name: \"Leather Wrap\", path: \"bracelets/bracelet_3.png\" },\n    { name: \"Diamond Tennis\", path: \"bracelets/bracelet_4.png\" },\n    { name: \"Beaded Stone\", path: \"bracelets/bracelet_5.png\" },\n    { name: \"Charm Bracelet\", path: \"bracelets/bracelet_6.png\" }\n  ];\n\n  // Initialize camera\n  const initCamera = async () => {\n    try {\n      const stream = await navigator.mediaDevices.getUserMedia({\n        video: {\n          facingMode: 'environment',\n          width: { ideal: 1920 },\n          height: { ideal: 1080 }\n        }\n      });\n      if (videoRef.current) {\n        videoRef.current.srcObject = stream;\n      }\n    } catch (err) {\n      console.error(\"Error accessing camera:\", err);\n      // Demo mode fallback\n      if (capturedImageRef.current) {\n        capturedImageRef.current.src = \"sample-hand.jpg\";\n        capturedImageRef.current.style.display = \"block\";\n      }\n      console.log(\"Camera not available - demo mode\");\n      setIsCaptured(true);\n      setShowProductSelection(true);\n    }\n  };\n\n  // Capture current frame\n  const captureFrame = () => {\n    if (!videoRef.current || !canvasRef.current) return null;\n\n    const canvas = canvasRef.current;\n    const video = videoRef.current;\n    canvas.width = video.videoWidth;\n    canvas.height = video.videoHeight;\n    const ctx = canvas.getContext('2d');\n    ctx.drawImage(video, 0, 0, canvas.width, canvas.height);\n    return canvas.toDataURL('image/png');\n  };\n\n  // Improved background remover that preserves light colors\n  const removeBackground = (imgElement, productType = 'watch') => {\n    const img = new Image();\n    img.crossOrigin = 'anonymous';\n\n    img.onload = function() {\n      const canvas = document.createElement('canvas');\n      const ctx = canvas.getContext('2d');\n      canvas.width = img.naturalWidth;\n      canvas.height = img.naturalHeight;\n      ctx.drawImage(img, 0, 0);\n\n      try {\n        const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);\n        const data = imageData.data;\n        const width = canvas.width;\n        const height = canvas.height;\n\n        // First pass: Identify edge pixels to preserve product boundaries\n        const edgePixels = new Set();\n        for (let y = 1; y < height - 1; y++) {\n          for (let x = 1; x < width - 1; x++) {\n            const idx = (y * width + x) * 4;\n            const r = data[idx];\n            const g = data[idx + 1];\n            const b = data[idx + 2];\n\n            // Check surrounding pixels for edge detection\n            let isEdge = false;\n            for (let dy = -1; dy <= 1; dy++) {\n              for (let dx = -1; dx <= 1; dx++) {\n                if (dx === 0 && dy === 0) continue;\n                const neighborIdx = ((y + dy) * width + (x + dx)) * 4;\n                const nr = data[neighborIdx];\n                const ng = data[neighborIdx + 1];\n                const nb = data[neighborIdx + 2];\n\n                // If there's a significant color difference, it's an edge\n                const colorDiff = Math.abs(r - nr) + Math.abs(g - ng) + Math.abs(b - nb);\n                if (colorDiff > 60) {\n                  isEdge = true;\n                  break;\n                }\n              }\n              if (isEdge) break;\n            }\n\n            if (isEdge) {\n              edgePixels.add(idx / 4);\n            }\n          }\n        }\n\n        // Second pass: Remove only white background while preserving all product colors\n        for (let i = 0; i < data.length; i += 4) {\n          const r = data[i];\n          const g = data[i + 1];\n          const b = data[i + 2];\n          const pixelIndex = i / 4;\n\n          // Calculate color properties\n          const brightness = (r + g + b) / 3;\n\n          // Check if pixel is near edges (preserve product boundaries)\n          const isNearEdge = edgePixels.has(pixelIndex);\n\n          // Only remove pure white or near-white pixels that aren't edges\n          const isPureWhite = (\n            r > 245 &&\n            g > 245 &&\n            b > 245 &&\n            Math.abs(r - g) < 10 &&\n            Math.abs(g - b) < 10 &&\n            !isNearEdge\n          );\n\n          // Remove background if it's pure white and not an edge\n          if (isPureWhite) {\n            data[i + 3] = 0; // Make fully transparent\n          } else if (brightness > 240 && !isNearEdge) {\n            // For very bright but not pure white, reduce opacity slightly\n            data[i + 3] = Math.max(0, data[i + 3] - 50);\n          }\n        }\n\n        ctx.putImageData(imageData, 0, 0);\n        imgElement.src = canvas.toDataURL('image/png');\n\n        // Apply product-specific styling that preserves all colors\n        imgElement.style.filter = 'none';\n        imgElement.style.mixBlendMode = 'normal';\n        imgElement.style.opacity = '1';\n\n      } catch (e) {\n        console.warn('Canvas operation failed:', e);\n        // Fallback styling that preserves all colors\n        imgElement.style.filter = 'none';\n        imgElement.style.mixBlendMode = 'normal';\n        imgElement.style.opacity = '1';\n      }\n    };\n\n    img.onerror = function() {\n      console.warn('Image loading failed');\n      // Fallback styling\n      imgElement.style.filter = 'none';\n      imgElement.style.mixBlendMode = 'normal';\n      imgElement.style.opacity = '1';\n    };\n\n    img.src = imgElement.src;\n  };\n\n  // Placeholder for hand detection\n  const detectHandOrientation = (imageData) => {\n    // Simple heuristic for demo purposes\n    return Math.random() > 0.5;\n  };\n\n  // Detect if arm and wrist are within the SVG guide area\n  const detectHandInPosition = () => {\n    if (!videoRef.current || !canvasRef.current) return false;\n\n    const video = videoRef.current;\n    const canvas = canvasRef.current;\n    const ctx = canvas.getContext('2d');\n\n    // Set canvas size to match video\n    canvas.width = video.videoWidth;\n    canvas.height = video.videoHeight;\n\n    // Draw current video frame\n    ctx.drawImage(video, 0, 0, canvas.width, canvas.height);\n\n    // Get the video container dimensions to calculate the guide area\n    const videoContainer = video.parentElement;\n    const containerRect = videoContainer.getBoundingClientRect();\n\n    // Calculate the actual video display area (accounting for object-fit: cover)\n    const videoAspect = video.videoWidth / video.videoHeight;\n    const containerAspect = containerRect.width / containerRect.height;\n\n    let displayWidth, displayHeight, offsetX, offsetY;\n\n    if (videoAspect > containerAspect) {\n      // Video is wider - height fills container, width is cropped\n      displayHeight = containerRect.height;\n      displayWidth = displayHeight * videoAspect;\n      offsetX = (displayWidth - containerRect.width) / 2;\n      offsetY = 0;\n    } else {\n      // Video is taller - width fills container, height is cropped\n      displayWidth = containerRect.width;\n      displayHeight = displayWidth / videoAspect;\n      offsetX = 0;\n      offsetY = (displayHeight - containerRect.height) / 2;\n    }\n\n    // Calculate the guide areas in canvas coordinates\n    // SVG viewBox is 800x600\n    // Main rectangle: x=\"320\" y=\"150\" width=\"160\" height=\"300\" (wrist/forearm area)\n    // Circle: cx=\"400\" cy=\"300\" r=\"60\" (hand area)\n\n    const scaleX = canvas.width / displayWidth;\n    const scaleY = canvas.height / displayHeight;\n\n    // Wrist/forearm area (rectangle)\n    const rectX = Math.max(0, ((320 / 800) * displayWidth - offsetX) * scaleX);\n    const rectY = Math.max(0, ((150 / 600) * displayHeight - offsetY) * scaleY);\n    const rectWidth = Math.min(canvas.width - rectX, ((160 / 800) * displayWidth) * scaleX);\n    const rectHeight = Math.min(canvas.height - rectY, ((300 / 600) * displayHeight) * scaleY);\n\n    // Hand area (circle)\n    const circleX = Math.max(0, ((340 / 800) * displayWidth - offsetX) * scaleX); // Adjusted for circle bounds\n    const circleY = Math.max(0, ((240 / 600) * displayHeight - offsetY) * scaleY); // Adjusted for circle bounds\n    const circleWidth = Math.min(canvas.width - circleX, ((120 / 800) * displayWidth) * scaleX); // Circle diameter\n    const circleHeight = Math.min(canvas.height - circleY, ((120 / 600) * displayHeight) * scaleY); // Circle diameter\n\n    try {\n      // Check wrist/forearm area (rectangle)\n      const rectImageData = ctx.getImageData(rectX, rectY, rectWidth, rectHeight);\n      const rectData = rectImageData.data;\n\n      // Check hand area (circle approximation)\n      const circleImageData = ctx.getImageData(circleX, circleY, circleWidth, circleHeight);\n      const circleData = circleImageData.data;\n\n      let rectSkinPixels = 0;\n      let rectTotalPixels = 0;\n      let circleSkinPixels = 0;\n      let circleTotalPixels = 0;\n\n      // Enhanced skin tone detection\n      const isSkinTone = (r, g, b) => {\n        // Multiple skin tone ranges to cover different skin colors\n        const condition1 = r > 95 && g > 40 && b > 20 && r > g && r > b && Math.abs(r - g) > 15;\n        const condition2 = r > 220 && g > 210 && b > 170 && Math.abs(r - g) < 15 && Math.abs(g - b) < 15;\n        const condition3 = r > 120 && g > 80 && b > 50 && r > g && g > b;\n        const condition4 = r > 180 && g > 120 && b > 90 && r > g && g >= b;\n\n        return condition1 || condition2 || condition3 || condition4;\n      };\n\n      // Analyze rectangle area (wrist/forearm)\n      for (let i = 0; i < rectData.length; i += 4) {\n        const r = rectData[i];\n        const g = rectData[i + 1];\n        const b = rectData[i + 2];\n\n        if (isSkinTone(r, g, b)) {\n          rectSkinPixels++;\n        }\n        rectTotalPixels++;\n      }\n\n      // Analyze circle area (hand)\n      for (let i = 0; i < circleData.length; i += 4) {\n        const r = circleData[i];\n        const g = circleData[i + 1];\n        const b = circleData[i + 2];\n\n        if (isSkinTone(r, g, b)) {\n          circleSkinPixels++;\n        }\n        circleTotalPixels++;\n      }\n\n      // Calculate skin ratios\n      const rectSkinRatio = rectSkinPixels / rectTotalPixels;\n      const circleSkinRatio = circleSkinPixels / circleTotalPixels;\n\n      // Both wrist/forearm AND hand areas must have sufficient skin tone presence\n      // Wrist area needs at least 20% skin tone (forearm/wrist)\n      // Hand area needs at least 25% skin tone (hand/fingers)\n      const wristInPosition = rectSkinRatio > 0.20;\n      const handInPosition = circleSkinRatio > 0.25;\n\n      return wristInPosition && handInPosition;\n\n    } catch (error) {\n      console.warn('Hand detection error:', error);\n      return false;\n    }\n  };\n\n  // Apply product to watch position with dynamic scaling\n  const applyProductToWatchPosition = (productPath, productType) => {\n    // Clear any existing product first\n    setSelectedProduct(null);\n\n    // Find the watch data for dial size\n    const watchData = watches.find(w => w.path === productPath);\n\n    // Small delay to ensure the old product is removed before adding new one\n    setTimeout(() => {\n      setSelectedProduct({\n        path: productPath,\n        dialSize: watchData?.dialSize || 40 // Default to 40mm if not found\n      });\n    }, 50);\n  };\n\n  // Handle capture button click\n  const handleCapture = () => {\n    if (!isCaptured) {\n      const capturedDataUrl = captureFrame();\n      if (capturedImageRef.current && capturedDataUrl) {\n        capturedImageRef.current.src = capturedDataUrl;\n        capturedImageRef.current.style.display = 'block';\n      }\n      setIsCaptured(true);\n      setShowWristSizeInput(true); // Show wrist size input first\n      setShowHandGuide(false);\n\n      // Detect hand orientation\n      if (canvasRef.current && videoRef.current) {\n        const canvas = canvasRef.current;\n        const ctx = canvas.getContext('2d');\n        canvas.width = videoRef.current.videoWidth;\n        canvas.height = videoRef.current.videoHeight;\n        ctx.drawImage(videoRef.current, 0, 0);\n        const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);\n        setIsRightHand(detectHandOrientation(imageData));\n      }\n    } else {\n      setShowProductSelection(true);\n    }\n  };\n\n  // Handle back button click\n  const handleBack = () => {\n    if (capturedImageRef.current) {\n      capturedImageRef.current.style.display = 'none';\n    }\n    setIsCaptured(false);\n    setSelectedProduct(null);\n    setIsRightHand(false);\n    setShowProductSelection(false);\n    setShowWristSizeInput(false);\n    setShowHandGuide(true);\n  };\n\n  // Handle gender selection\n  const handleGenderChange = (gender) => {\n    setUserGender(gender);\n    setUserWristSize(DEFAULT_WRIST_SIZES[gender]);\n  };\n\n  // Handle wrist size change\n  const handleWristSizeChange = (size) => {\n    setUserWristSize(size);\n  };\n\n  // Handle continue to product selection\n  const handleContinueToProducts = () => {\n    setShowWristSizeInput(false);\n    setShowProductSelection(true);\n  };\n\n  // Handle tab change\n  const handleTabChange = (tabName) => {\n    setActiveTab(tabName);\n  };\n\n  // Handle product selection\n  const handleProductSelect = (product) => {\n    applyProductToWatchPosition(product.path, activeTab);\n  };\n\n  // Initialize camera on component mount\n  useEffect(() => {\n    initCamera();\n  }, []);\n\n  // Hand position monitoring effect (only when autocapture is enabled)\n  useEffect(() => {\n    if (!isAutoCaptureEnabled || isCaptured) return;\n\n    const interval = setInterval(() => {\n      const handInPosition = detectHandInPosition();\n      setIsHandInPosition(handInPosition);\n\n      // Only start countdown if hand is properly positioned\n      if (handInPosition && !isCountdownActive && countdown === 0) {\n        setIsCountdownActive(true);\n      }\n\n      // Stop countdown if hand moves out of position\n      if (!handInPosition && isCountdownActive) {\n        setIsCountdownActive(false);\n        setCountdown(0);\n      }\n    }, 150); // Check every 150ms for better performance\n\n    return () => clearInterval(interval);\n  }, [isAutoCaptureEnabled, isCaptured, isCountdownActive, countdown]);\n\n  // Countdown effect (starts automatically when hand is in position)\n  useEffect(() => {\n    if (!isCountdownActive || isCaptured) {\n      setCountdown(0);\n      return;\n    }\n\n    // Only start countdown if hand is still in position\n    if (!isHandInPosition) {\n      setIsCountdownActive(false);\n      setCountdown(0);\n      return;\n    }\n\n    // Start countdown\n    setCountdown(3);\n\n    const countdownInterval = setInterval(() => {\n      setCountdown(prev => {\n        // Double-check hand position before continuing countdown\n        if (!isHandInPosition) {\n          clearInterval(countdownInterval);\n          setIsCountdownActive(false);\n          return 0;\n        }\n\n        if (prev <= 1) {\n          // Countdown finished - trigger capture\n          clearInterval(countdownInterval);\n          setIsCountdownActive(false);\n          setIsAutoCaptureEnabled(false); // Turn off autocapture after capture\n          handleCapture();\n          return 0;\n        }\n        return prev - 1;\n      });\n    }, 1000);\n\n    return () => clearInterval(countdownInterval);\n  }, [isCountdownActive, isCaptured, isHandInPosition]);\n\n  // Handle autocapture switch toggle\n  const handleAutoCaptureToggle = () => {\n    const newState = !isAutoCaptureEnabled;\n    setIsAutoCaptureEnabled(newState);\n\n    // Reset states when turning off\n    if (!newState) {\n      setIsCountdownActive(false);\n      setCountdown(0);\n      setIsHandInPosition(false);\n    }\n  };\n\n  // Reset autocapture when going back\n  const handleBackWithReset = () => {\n    setIsAutoCaptureEnabled(false);\n    setIsCountdownActive(false);\n    setCountdown(0);\n    setIsHandInPosition(false);\n    setShowWristSizeInput(false);\n    setUserGender('men');\n    setUserWristSize(64);\n    handleBack();\n  };\n\n  // Get current products based on active tab\n  const getCurrentProducts = () => {\n    return activeTab === 'Watches' ? watches : bracelets;\n  };\n\n  return (\n    <div\n      style={styles.container}\n      className={isMobile ? 'mobile-container chrome-mobile-fix' : ''}\n    >\n      <div style={styles.cameraContainer}>\n        <video\n          ref={videoRef}\n          style={styles.cameraFeed}\n          autoPlay\n          playsInline\n          muted\n        />\n        <canvas ref={canvasRef} style={{ display: 'none' }} />\n        <img\n          ref={capturedImageRef}\n          style={styles.capturedImage}\n          alt=\"Captured hand\"\n        />\n\n        {/* Simple Home Button - Only visible when not captured */}\n        {!isCaptured && (\n          <button\n            style={styles.homeBtn}\n            onClick={onBackToHome}\n            aria-label=\"Home\"\n          >\n            ←\n          </button>\n        )}\n\n        {/* Autocapture Switch Button - Only visible when not captured */}\n        {!isCaptured && (\n          <div style={{ position: 'absolute', top: isMobile ? 10 : 20, right: isMobile ? 10 : 20, zIndex: 20, display: 'flex', flexDirection: 'column', alignItems: 'center' }}>\n            <label style={{ color: '#2D8C88', fontWeight: 700, fontSize: isMobile ? 14 : 16, marginBottom: 6, letterSpacing: 0.5 }}>Auto Capture</label>\n            <label className=\"switch\">\n              <input\n                type=\"checkbox\"\n                checked={isAutoCaptureEnabled}\n                onChange={handleAutoCaptureToggle}\n                disabled={isCountdownActive}\n                aria-label=\"Toggle auto capture\"\n              />\n              <span className=\"slider\">\n                <span className=\"circle\">\n                  <svg className=\"checkmark\" viewBox=\"0 0 12 10\"><polyline points=\"1.5 6 5 9 10.5 1\" fill=\"none\" stroke=\"#2D8C88\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\"/></svg>\n                  <svg className=\"cross\" viewBox=\"0 0 10 10\"><line x1=\"1\" y1=\"1\" x2=\"9\" y2=\"9\" stroke=\"#838383\" strokeWidth=\"2\" strokeLinecap=\"round\"/><line x1=\"9\" y1=\"1\" x2=\"1\" y2=\"9\" stroke=\"#838383\" strokeWidth=\"2\" strokeLinecap=\"round\"/></svg>\n                </span>\n              </span>\n            </label>\n          </div>\n        )}\n\n        {/* Simple Back Button - Only visible when captured */}\n        {isCaptured && (\n          <button\n            style={styles.backBtn}\n            onClick={handleBackWithReset}\n            aria-label=\"Back\"\n          >\n            ←\n          </button>\n        )}\n\n        {/* Countdown Display - Only visible during active countdown */}\n        {isCountdownActive && (\n          <div style={styles.countdownDisplay}>\n            <div style={styles.countdownNumber}>{countdown}</div>\n            <div style={styles.countdownText}>Auto capturing...</div>\n          </div>\n        )}\n\n        {/* Status Messages */}\n        {isAutoCaptureEnabled && !isHandInPosition && !isCountdownActive && (\n          <div style={styles.statusMessage}>\n            <div style={styles.statusText}>Position your arm and wrist in the guide area</div>\n            <div style={styles.statusSubtext}>Countdown will start automatically when detected</div>\n          </div>\n        )}\n\n        {isAutoCaptureEnabled && isHandInPosition && !isCountdownActive && (\n          <div style={styles.statusMessage}>\n            <div style={{...styles.statusText, backgroundColor: 'rgba(45, 140, 136, 0.9)'}}>\n              Perfect! Starting countdown...\n            </div>\n          </div>\n        )}\n\n        {/* Hand Guide SVG */}\n        {showHandGuide && (\n          <div\n            style={{\n              ...styles.handGuide,\n              opacity: isAutoCaptureEnabled && isHandInPosition ? 0.9 : 0.8,\n              filter: isAutoCaptureEnabled && isHandInPosition\n                ? 'drop-shadow(0 2px 8px rgba(45, 140, 136, 0.5))'\n                : isAutoCaptureEnabled && !isHandInPosition\n                  ? 'drop-shadow(0 2px 8px rgba(255, 107, 107, 0.5))'\n                  : 'drop-shadow(0 2px 8px rgba(255, 255, 255, 0.3))'\n            }}\n            className={isMobile ? 'mobile-hand-guide' : ''}\n            aria-hidden=\"true\"\n          >\n            <svg viewBox=\"0 0 800 600\" xmlns=\"http://www.w3.org/2000/svg\">\n              {/* Wrist guide lines */}\n              <path\n                d=\"M100 480 C 150 460, 200 450, 250 450 L 550 450 C 600 450, 650 460, 700 480\"\n                stroke={\n                  isAutoCaptureEnabled && isHandInPosition\n                    ? \"#2D8C88\"\n                    : isAutoCaptureEnabled && !isHandInPosition\n                      ? \"#ff6b6b\"\n                      : \"white\"\n                }\n                strokeWidth=\"5\"\n                fill=\"none\"\n                strokeLinecap=\"round\"\n              />\n              <path\n                d=\"M100 120 C 150 140, 200 150, 250 150 L 550 150 C 600 150, 650 140, 700 120\"\n                stroke={\n                  isAutoCaptureEnabled && isHandInPosition\n                    ? \"#2D8C88\"\n                    : isAutoCaptureEnabled && !isHandInPosition\n                      ? \"#ff6b6b\"\n                      : \"white\"\n                }\n                strokeWidth=\"5\"\n                fill=\"none\"\n                strokeLinecap=\"round\"\n              />\n              {/* Wrist/Forearm area (rectangle) */}\n              <rect\n                x=\"320\"\n                y=\"150\"\n                width=\"160\"\n                height=\"300\"\n                fill={\n                  isAutoCaptureEnabled && isHandInPosition\n                    ? \"#2D8C88\"\n                    : isAutoCaptureEnabled && !isHandInPosition\n                      ? \"#ff6b6b\"\n                      : \"white\"\n                }\n                opacity={isAutoCaptureEnabled && isHandInPosition ? \"0.25\" : \"0.15\"}\n                rx=\"15\"\n                stroke={\n                  isAutoCaptureEnabled && isHandInPosition\n                    ? \"#2D8C88\"\n                    : isAutoCaptureEnabled && !isHandInPosition\n                      ? \"#ff6b6b\"\n                      : \"white\"\n                }\n                strokeWidth=\"2\"\n              />\n              {/* Hand area (circle) */}\n              <circle\n                cx=\"400\"\n                cy=\"300\"\n                r=\"60\"\n                fill={\n                  isAutoCaptureEnabled && isHandInPosition\n                    ? \"#2D8C88\"\n                    : isAutoCaptureEnabled && !isHandInPosition\n                      ? \"#ff6b6b\"\n                      : \"white\"\n                }\n                opacity={isAutoCaptureEnabled && isHandInPosition ? \"0.3\" : \"0.2\"}\n                stroke={\n                  isAutoCaptureEnabled && isHandInPosition\n                    ? \"#2D8C88\"\n                    : isAutoCaptureEnabled && !isHandInPosition\n                      ? \"#ff6b6b\"\n                      : \"white\"\n                }\n                strokeWidth=\"2\"\n              />\n              {/* Labels for clarity */}\n              {isAutoCaptureEnabled && (\n                <>\n                  <text x=\"400\" y=\"140\" textAnchor=\"middle\" fill=\"white\" fontSize=\"14\" fontWeight=\"bold\">\n                    WRIST & FOREARM\n                  </text>\n                  <text x=\"400\" y=\"480\" textAnchor=\"middle\" fill=\"white\" fontSize=\"14\" fontWeight=\"bold\">\n                    HAND\n                  </text>\n                </>\n              )}\n            </svg>\n          </div>\n        )}\n\n        {/* Product Display */}\n        {selectedProduct && (\n          <div style={{\n            ...styles.productPosition,\n            width: activeTab === 'Watches' ? `${WATCH_WIDTH}%` : `${BRACELET_WIDTH}%`,\n            height: activeTab === 'Watches' ? `${WATCH_HEIGHT}%` : `${BRACELET_HEIGHT}%`\n          }}>\n            <div style={{\n              position: 'relative',\n              width: '100%',\n              height: '100%',\n              display: 'flex',\n              alignItems: 'center',\n              justifyContent: 'center'\n            }}>\n              <img\n                src={typeof selectedProduct === 'object' ? selectedProduct.path : selectedProduct}\n                alt=\"Selected product\"\n                style={{\n                  width: '100%',\n                  height: '100%',\n                  objectFit: 'contain',\n                  transform: activeTab === 'Bracelets'\n                    ? `rotate(90deg) scale(${BRACELET_HEIGHT / 30})${isRightHand ? ' scaleX(-1)' : ''}`\n                    : `scale(${(WATCH_HEIGHT / 25) * (userWristSize / DEFAULT_WRIST_SIZES[userGender])})`,\n                  filter: 'drop-shadow(0 2px 8px rgba(0,0,0,0.3))'\n                }}\n                onLoad={(e) => removeBackground(e.target, activeTab === 'Watches' ? 'watch' : 'bracelet')}\n              />\n              {activeTab === 'Watches' && typeof selectedProduct === 'object' && (\n                <div style={{\n                  position: 'absolute',\n                  bottom: '-30px',\n                  left: '50%',\n                  transform: 'translateX(-50%)',\n                  fontSize: '11px',\n                  fontWeight: '600',\n                  color: 'white',\n                  backgroundColor: 'rgba(45, 140, 136, 0.9)',\n                  padding: '3px 8px',\n                  borderRadius: '12px',\n                  whiteSpace: 'nowrap',\n                  pointerEvents: 'none',\n                  boxShadow: '0 2px 6px rgba(0,0,0,0.3)',\n                  zIndex: 2\n                }}>\n                  {selectedProduct.dialSize}mm\n                  {userWristSize !== DEFAULT_WRIST_SIZES[userGender] && (\n                    <span style={{\n                      fontSize: '10px',\n                      opacity: 0.8,\n                      marginLeft: '4px'\n                    }}>\n                      (scaled {((userWristSize / DEFAULT_WRIST_SIZES[userGender] - 1) * 100).toFixed(0)}%)\n                    </span>\n                  )}\n                </div>\n              )}\n            </div>\n          </div>\n        )}\n\n        {/* Camera-style Capture Button */}\n        <button\n          style={styles.captureBtn}\n          onClick={handleCapture}\n          aria-label={isCaptured ? \"Select Products\" : \"Capture\"}\n        >\n          {!isCaptured ? (\n            <div style={styles.captureInner}></div>\n          ) : (\n            <svg width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"white\">\n              <path d=\"M8.59 16.59L13.17 12L8.59 7.41L10 6L16 12L10 18L8.59 16.59Z\"/>\n            </svg>\n          )}\n        </button>\n\n        {/* Reset Button - Only visible when captured */}\n        {isCaptured && (\n          <button\n            style={styles.resetBtn}\n            onClick={() => window.location.reload()}\n            aria-label=\"Reset\"\n          >\n            <svg width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"white\">\n              <path d=\"M17.65 6.35C16.2 4.9 14.21 4 12 4C7.58 4 4 7.58 4 12S7.58 20 12 20C15.73 20 18.84 17.45 19.73 14H17.65C16.83 16.33 14.61 18 12 18C8.69 18 6 15.31 6 12S8.69 6 12 6C13.66 6 15.14 6.69 16.22 7.78L13 11H20V4L17.65 6.35Z\"/>\n            </svg>\n          </button>\n        )}\n      </div>\n\n      {/* Wrist Size Input Panel */}\n      {showWristSizeInput && (\n        <div\n          style={styles.wristSizePanel}\n          className={isMobile ? 'mobile-wrist-panel' : ''}\n          aria-modal=\"true\"\n          role=\"dialog\"\n        >\n          <div style={styles.wristSizeContent}>\n            <h3 style={styles.wristSizeTitle}>Customize Watch Size</h3>\n            <p style={styles.wristSizeSubtitle}>Select your gender and adjust wrist size for the perfect fit</p>\n\n            {/* Gender Selection */}\n            <div style={styles.genderSelection}>\n              <button\n                style={{\n                  ...styles.genderButton,\n                  ...(userGender === 'men' ? styles.genderButtonActive : {})\n                }}\n                onClick={() => handleGenderChange('men')}\n              >\n                Men (64mm)\n              </button>\n              <button\n                style={{\n                  ...styles.genderButton,\n                  ...(userGender === 'women' ? styles.genderButtonActive : {})\n                }}\n                onClick={() => handleGenderChange('women')}\n              >\n                Women (54mm)\n              </button>\n            </div>\n\n            {/* Wrist Size Slider */}\n            <div style={styles.sliderContainer}>\n              <label style={styles.sliderLabel}>\n                Wrist Size: {userWristSize}mm\n                {userWristSize !== DEFAULT_WRIST_SIZES[userGender] && (\n                  <span style={styles.sizeChange}>\n                    ({userWristSize > DEFAULT_WRIST_SIZES[userGender] ? '+' : ''}\n                    {((userWristSize / DEFAULT_WRIST_SIZES[userGender] - 1) * 100).toFixed(0)}%)\n                  </span>\n                )}\n              </label>\n              <input\n                type=\"range\"\n                min=\"45\"\n                max=\"80\"\n                value={userWristSize}\n                onChange={(e) => handleWristSizeChange(parseInt(e.target.value))}\n                style={styles.slider}\n              />\n              <div style={styles.sliderLabels}>\n                <span>45mm</span>\n                <span>80mm</span>\n              </div>\n            </div>\n\n            {/* Continue Button */}\n            <button\n              style={styles.continueButton}\n              onClick={handleContinueToProducts}\n            >\n              Continue to Products\n            </button>\n          </div>\n        </div>\n      )}\n\n      {/* Product Selection Panel */}\n      {showProductSelection && (\n        <div\n          style={styles.productSelection}\n          className={isMobile ? 'mobile-product-panel' : ''}\n          aria-modal=\"true\"\n          role=\"dialog\"\n        >\n          <button\n            style={styles.closeBtn}\n            onClick={() => setShowProductSelection(false)}\n            aria-label=\"Close product selection\"\n          >\n            <svg width={isMobile ? \"16\" : \"20\"} height={isMobile ? \"16\" : \"20\"} viewBox=\"0 0 24 24\" fill=\"currentColor\">\n              <path d=\"M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z\"/>\n            </svg>\n          </button>\n          <div style={styles.productTabs}>\n            <button\n              style={{\n                ...styles.tab,\n                ...(activeTab === 'Watches' ? styles.activeTab : {})\n              }}\n              onClick={() => handleTabChange('Watches')}\n            >\n              Watches\n            </button>\n            <button\n              style={{\n                ...styles.tab,\n                ...(activeTab === 'Bracelets' ? styles.activeTab : {})\n              }}\n              onClick={() => handleTabChange('Bracelets')}\n            >\n              Bracelets\n            </button>\n          </div>\n          <div style={styles.productScroll} className=\"product-scroll\">\n            {getCurrentProducts().map((product, index) => {\n              // Simple null check only\n              if (!product) return null;\n\n              const isSelected = (typeof selectedProduct === 'object' ? selectedProduct?.path : selectedProduct) === product.path;\n\n              return (\n                <button\n                  key={index}\n                  style={{\n                    ...styles.productItem,\n                    borderColor: isSelected ? '#2D8C88' : '#e9ecef',\n                    backgroundColor: isSelected ? '#f0fffe' : '#ffffff'\n                  }}\n                  title={`${product.name} - ${product.caseDiameter || 'N/A'}mm`}\n                  onClick={() => handleProductSelect(product)}\n                  aria-label={`Select ${product.name} ${product.caseDiameter || 'N/A'}mm`}\n                >\n                  <img\n                    src={product.path}\n                    alt={product.name}\n                    style={styles.productImage}\n                    onError={(e) => {\n                      e.target.parentElement.style.display = 'none';\n                    }}\n                  />\n                  <div style={styles.productLabel}>\n                    <div style={styles.productName}>{product.name}</div>\n                    {activeTab === 'Watches' && product.caseDiameter && (\n                      <div style={styles.productSize}>{product.caseDiameter}mm</div>\n                    )}\n                  </div>\n                </button>\n              );\n            })}\n          </div>\n        </div>\n      )}\n    </div>\n  );\n};\n\n// Styles object - Clean, modern design with improved mobile responsiveness\nconst styles = {\n  container: {\n    position: 'relative',\n    height: 'calc(var(--vh, 1vh) * 100)',\n    display: 'flex',\n    flexDirection: 'column',\n    backgroundColor: '#f8f9fa',\n    color: '#333',\n    fontFamily: \"'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif\",\n    overflow: 'hidden',\n    touchAction: 'manipulation',\n    WebkitTapHighlightColor: 'transparent'\n  },\n  cameraContainer: {\n    flex: 1,\n    position: 'relative',\n    overflow: 'hidden',\n    backgroundColor: '#000',\n    display: 'flex',\n    alignItems: 'center',\n    justifyContent: 'center'\n  },\n  cameraFeed: {\n    width: '100%',\n    height: '100%',\n    objectFit: 'cover',\n    transform: 'scaleX(1)' // Fix for Safari mirroring\n  },\n  capturedImage: {\n    position: 'absolute',\n    top: 0,\n    left: 0,\n    width: '100%',\n    height: '100%',\n    objectFit: 'cover',\n    display: 'none',\n    WebkitTransform: 'scaleX(1)' // Fix for Safari mirroring\n  },\n\n  homeBtn: {\n    position: 'absolute',\n    top: '20px',\n    left: '20px',\n    backgroundColor: 'rgba(0, 0, 0, 0.6)',\n    color: 'white',\n    padding: '12px',\n    borderRadius: '50%',\n    fontSize: '20px',\n    fontWeight: '700',\n    cursor: 'pointer',\n    zIndex: 10,\n    border: 'none',\n    boxShadow: '0 2px 10px rgba(0, 0, 0, 0.3)',\n    transition: 'all 0.2s ease',\n    display: 'flex',\n    alignItems: 'center',\n    justifyContent: 'center',\n    width: '44px',\n    height: '44px',\n    outline: 'none'\n  },\n  backBtn: {\n    position: 'absolute',\n    top: '20px',\n    left: '20px',\n    backgroundColor: 'rgba(0, 0, 0, 0.6)',\n    color: 'white',\n    padding: '12px',\n    borderRadius: '50%',\n    fontSize: '20px',\n    fontWeight: '700',\n    cursor: 'pointer',\n    zIndex: 10,\n    border: 'none',\n    boxShadow: '0 2px 10px rgba(0, 0, 0, 0.3)',\n    transition: 'all 0.2s ease',\n    display: 'flex',\n    alignItems: 'center',\n    justifyContent: 'center',\n    width: '44px',\n    height: '44px',\n    outline: 'none'\n  },\n  switchContainer: {\n    position: 'absolute',\n    top: '20px',\n    right: '20px',\n    zIndex: 10,\n    display: 'flex',\n    flexDirection: 'column',\n    alignItems: 'center',\n    gap: '4px',\n    padding: '12px',\n    backgroundColor: 'rgba(0, 0, 0, 0.5)',\n    borderRadius: '20px',\n    backdropFilter: 'blur(10px)',\n    WebkitBackdropFilter: 'blur(10px)',\n    boxShadow: '0 4px 12px rgba(0, 0, 0, 0.2)',\n    border: '1px solid rgba(255, 255, 255, 0.1)'\n  },\n  switchTrack: {\n    position: 'absolute',\n    top: '12px',\n    left: '12px',\n    width: '60px',\n    height: '32px',\n    backgroundColor: 'rgba(0, 0, 0, 0.3)',\n    borderRadius: '16px',\n    border: '2px solid rgba(255, 255, 255, 0.2)',\n    zIndex: 9,\n    transition: 'all 0.3s ease'\n  },\n  switchButton: {\n    position: 'relative',\n    width: '28px',\n    height: '28px',\n    borderRadius: '50%',\n    border: 'none',\n    cursor: 'pointer',\n    transition: 'all 0.3s ease',\n    display: 'flex',\n    alignItems: 'center',\n    justifyContent: 'center',\n    zIndex: 11,\n    margin: '2px',\n    boxShadow: '0 2px 8px rgba(0, 0, 0, 0.3)',\n    outline: 'none',\n    '&:hover': {\n      transform: 'scale(1.1)'\n    }\n  },\n  switchLabel: {\n    fontSize: '12px',\n    fontWeight: '700',\n    color: 'white',\n    textShadow: '0 1px 2px rgba(0, 0, 0, 0.7)',\n    marginTop: '4px',\n    padding: '4px 12px',\n    borderRadius: '12px',\n    backgroundColor: 'rgba(0, 0, 0, 0.3)',\n    letterSpacing: '0.5px'\n  },\n  countdownDisplay: {\n    position: 'absolute',\n    top: '35%',\n    left: '50%',\n    transform: 'translate(-50%, -50%)',\n    zIndex: 15,\n    textAlign: 'center',\n    pointerEvents: 'none',\n    padding: '16px 24px',\n    backgroundColor: 'rgba(0, 0, 0, 0.4)',\n    borderRadius: '20px',\n    backdropFilter: 'blur(10px)',\n    WebkitBackdropFilter: 'blur(10px)',\n    boxShadow: '0 4px 20px rgba(0, 0, 0, 0.3)'\n  },\n  countdownNumber: {\n    fontSize: '72px',\n    fontWeight: '900',\n    color: '#2D8C88',\n    textShadow: '0 2px 8px rgba(0, 0, 0, 0.5)',\n    marginBottom: '8px',\n    animation: 'pulse 1s ease-in-out'\n  },\n  countdownText: {\n    fontSize: '16px',\n    fontWeight: '600',\n    color: 'white',\n    textShadow: '0 1px 4px rgba(0, 0, 0, 0.7)'\n  },\n  statusMessage: {\n    position: 'absolute',\n    top: '25%',\n    left: '50%',\n    transform: 'translate(-50%, -50%)',\n    zIndex: 15,\n    textAlign: 'center',\n    pointerEvents: 'none',\n    padding: '12px 20px',\n    borderRadius: '16px',\n    backgroundColor: 'rgba(0, 0, 0, 0.4)',\n    backdropFilter: 'blur(10px)',\n    WebkitBackdropFilter: 'blur(10px)',\n    boxShadow: '0 4px 20px rgba(0, 0, 0, 0.3)'\n  },\n  statusText: {\n    fontSize: '16px',\n    fontWeight: '600',\n    color: 'white',\n    textShadow: '0 1px 4px rgba(0, 0, 0, 0.7)',\n    backgroundColor: 'rgba(255, 107, 107, 0.9)',\n    padding: '12px 20px',\n    borderRadius: '25px',\n    marginBottom: '8px',\n    transition: 'all 0.3s ease'\n  },\n  statusSubtext: {\n    fontSize: '12px',\n    fontWeight: '500',\n    color: 'white',\n    textShadow: '0 1px 4px rgba(0, 0, 0, 0.7)',\n    backgroundColor: 'rgba(0, 0, 0, 0.6)',\n    padding: '6px 12px',\n    borderRadius: '15px'\n  },\n  handGuide: {\n    position: 'absolute',\n    top: '50%',\n    left: '50%',\n    transform: 'translate(-50%, -50%)',\n    width: '80%',\n    maxWidth: '400px',\n    height: 'auto',\n    opacity: 0.8,\n    pointerEvents: 'none',\n    zIndex: 5,\n    filter: 'drop-shadow(0 2px 8px rgba(255, 255, 255, 0.3))',\n    WebkitFilter: 'drop-shadow(0 2px 8px rgba(255, 255, 255, 0.3))',\n    transition: 'all 0.3s ease'\n  },\n  productPosition: {\n    position: 'absolute',\n    top: '50%',\n    left: '50%',\n    transform: 'translate(-50%, -50%)',\n    zIndex: 8,\n    display: 'flex',\n    alignItems: 'center',\n    justifyContent: 'center',\n    pointerEvents: 'none',\n    minWidth: '100px', // Ensure minimum size\n    minHeight: '100px' // Ensure minimum size\n  },\n  captureBtn: {\n    position: 'absolute',\n    bottom: '30px',\n    left: '50%',\n    transform: 'translateX(-50%)',\n    width: '70px',\n    height: '70px',\n    backgroundColor: 'rgba(255, 255, 255, 0.9)',\n    borderRadius: '50%',\n    display: 'flex',\n    alignItems: 'center',\n    justifyContent: 'center',\n    cursor: 'pointer',\n    zIndex: 10,\n    transition: 'all 0.2s ease',\n    border: '4px solid rgba(255, 255, 255, 0.3)',\n    boxShadow: '0 4px 20px rgba(0, 0, 0, 0.3)',\n    outline: 'none',\n    padding: 0\n  },\n  captureInner: {\n    width: '50px',\n    height: '50px',\n    backgroundColor: '#2D8C88',\n    borderRadius: '50%',\n    transition: 'all 0.2s ease'\n  },\n  resetBtn: {\n    position: 'absolute',\n    bottom: '30px',\n    right: '30px',\n    width: '50px',\n    height: '50px',\n    backgroundColor: 'rgba(0, 0, 0, 0.6)',\n    borderRadius: '50%',\n    display: 'flex',\n    alignItems: 'center',\n    justifyContent: 'center',\n    cursor: 'pointer',\n    zIndex: 10,\n    transition: 'all 0.2s ease',\n    border: 'none',\n    boxShadow: '0 2px 10px rgba(0, 0, 0, 0.3)',\n    outline: 'none',\n    padding: 0\n  },\n\n  // Wrist Size Input Panel Styles\n  wristSizePanel: {\n    position: 'absolute',\n    bottom: 0,\n    left: 0,\n    right: 0,\n    backgroundColor: 'rgba(255, 255, 255, 0.95)',\n    backdropFilter: 'blur(20px)',\n    WebkitBackdropFilter: 'blur(20px)',\n    borderTopLeftRadius: '20px',\n    borderTopRightRadius: '20px',\n    padding: '24px',\n    maxHeight: '70vh',\n    display: 'flex',\n    flexDirection: 'column',\n    zIndex: 25,\n    boxShadow: '0 -4px 20px rgba(0, 0, 0, 0.15)',\n    border: 'none'\n  },\n  wristSizeContent: {\n    display: 'flex',\n    flexDirection: 'column',\n    alignItems: 'center',\n    gap: '20px'\n  },\n  wristSizeTitle: {\n    fontSize: '20px',\n    fontWeight: '700',\n    color: '#2D8C88',\n    margin: 0,\n    textAlign: 'center'\n  },\n  wristSizeSubtitle: {\n    fontSize: '14px',\n    color: '#666',\n    margin: 0,\n    textAlign: 'center',\n    lineHeight: '1.4'\n  },\n  genderSelection: {\n    display: 'flex',\n    gap: '12px',\n    width: '100%',\n    maxWidth: '300px'\n  },\n  genderButton: {\n    flex: 1,\n    padding: '12px 16px',\n    borderRadius: '10px',\n    fontSize: '14px',\n    fontWeight: '600',\n    cursor: 'pointer',\n    transition: 'all 0.2s ease',\n    border: '2px solid #e0e0e0',\n    backgroundColor: '#ffffff',\n    color: '#666',\n    outline: 'none'\n  },\n  genderButtonActive: {\n    backgroundColor: '#2D8C88',\n    color: '#ffffff',\n    borderColor: '#2D8C88',\n    boxShadow: '0 2px 8px rgba(45, 140, 136, 0.3)'\n  },\n  sliderContainer: {\n    width: '100%',\n    maxWidth: '300px',\n    display: 'flex',\n    flexDirection: 'column',\n    gap: '8px'\n  },\n  sliderLabel: {\n    fontSize: '16px',\n    fontWeight: '600',\n    color: '#333',\n    textAlign: 'center',\n    display: 'flex',\n    alignItems: 'center',\n    justifyContent: 'center',\n    gap: '8px'\n  },\n  sizeChange: {\n    fontSize: '14px',\n    fontWeight: '500',\n    color: '#2D8C88',\n    backgroundColor: 'rgba(45, 140, 136, 0.1)',\n    padding: '2px 8px',\n    borderRadius: '12px'\n  },\n  slider: {\n    width: '100%',\n    height: '6px',\n    borderRadius: '3px',\n    background: '#e0e0e0',\n    outline: 'none',\n    cursor: 'pointer',\n    WebkitAppearance: 'none',\n    appearance: 'none'\n  },\n  sliderLabels: {\n    display: 'flex',\n    justifyContent: 'space-between',\n    fontSize: '12px',\n    color: '#999',\n    marginTop: '4px'\n  },\n  continueButton: {\n    width: '100%',\n    maxWidth: '300px',\n    padding: '14px 24px',\n    backgroundColor: '#2D8C88',\n    color: '#ffffff',\n    borderRadius: '12px',\n    fontSize: '16px',\n    fontWeight: '600',\n    cursor: 'pointer',\n    transition: 'all 0.2s ease',\n    border: 'none',\n    outline: 'none',\n    boxShadow: '0 4px 12px rgba(45, 140, 136, 0.3)'\n  },\n\n  productSelection: {\n    position: 'absolute',\n    bottom: 0,\n    left: 0,\n    right: 0,\n    backgroundColor: 'rgba(255, 255, 255, 0.95)',\n    backdropFilter: 'blur(20px)',\n    WebkitBackdropFilter: 'blur(20px)',\n    borderTopLeftRadius: '20px',\n    borderTopRightRadius: '20px',\n    padding: '16px',\n    maxHeight: '70vh',\n    display: 'flex',\n    flexDirection: 'column',\n    zIndex: 20,\n    boxShadow: '0 -4px 20px rgba(0, 0, 0, 0.15)',\n    border: 'none'\n  },\n  closeBtn: {\n    position: 'absolute',\n    top: '12px',\n    right: '16px',\n    color: '#666',\n    cursor: 'pointer',\n    zIndex: 21,\n    width: '32px',\n    height: '32px',\n    display: 'flex',\n    alignItems: 'center',\n    justifyContent: 'center',\n    borderRadius: '50%',\n    backgroundColor: 'rgba(0, 0, 0, 0.1)',\n    transition: 'all 0.2s ease',\n    border: 'none',\n    outline: 'none',\n    padding: 0\n  },\n  productTabs: {\n    display: 'flex',\n    marginBottom: '16px',\n    marginTop: '8px',\n    backgroundColor: '#f0f0f0',\n    borderRadius: '10px',\n    padding: '3px'\n  },\n  tab: {\n    flex: 1,\n    textAlign: 'center',\n    padding: '10px 16px',\n    borderRadius: '7px',\n    fontSize: '15px',\n    fontWeight: '600',\n    cursor: 'pointer',\n    transition: 'all 0.2s ease',\n    color: '#666',\n    outline: 'none',\n    border: 'none',\n    backgroundColor: 'transparent'\n  },\n  activeTab: {\n    backgroundColor: '#2D8C88',\n    color: '#ffffff',\n    boxShadow: '0 1px 4px rgba(45, 140, 136, 0.3)'\n  },\n  productScroll: {\n    display: 'grid',\n    gridTemplateColumns: 'repeat(auto-fill, minmax(85px, 1fr))',\n    gap: '10px',\n    maxHeight: 'calc(70vh - 80px)',\n    overflowY: 'auto',\n    paddingBottom: '8px',\n    scrollbarWidth: 'thin',\n    scrollbarColor: '#ddd #f5f5f5'\n  },\n  productItem: {\n    position: 'relative',\n    width: '100%',\n    aspectRatio: '1/1',\n    backgroundColor: '#ffffff',\n    borderRadius: '12px',\n    display: 'flex',\n    alignItems: 'center',\n    justifyContent: 'center',\n    cursor: 'pointer',\n    transition: 'all 0.2s ease',\n    border: '1px solid #e0e0e0',\n    overflow: 'hidden',\n    boxShadow: '0 1px 4px rgba(0, 0, 0, 0.1)',\n    padding: '6px',\n    outline: 'none'\n  },\n  productImage: {\n    width: '100%',\n    height: '100%',\n    objectFit: 'contain',\n    borderRadius: '8px',\n    backgroundColor: '#ffffff'\n  },\n  productLabel: {\n    position: 'absolute',\n    bottom: '3px',\n    left: '3px',\n    right: '3px',\n    fontSize: '9px',\n    color: '#666',\n    textAlign: 'center',\n    backgroundColor: 'rgba(255, 255, 255, 0.95)',\n    borderRadius: '3px',\n    padding: '3px 2px',\n    overflow: 'hidden'\n  },\n  productName: {\n    fontSize: '9px',\n    fontWeight: '600',\n    whiteSpace: 'nowrap',\n    textOverflow: 'ellipsis',\n    overflow: 'hidden',\n    marginBottom: '1px'\n  },\n  productSize: {\n    fontSize: '8px',\n    fontWeight: '500',\n    color: '#2D8C88',\n    whiteSpace: 'nowrap'\n  }\n};\n\nexport default Tryon;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,MAAM,QAAQ,OAAO;;AAE1D;AAAA,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AACA,MAAMC,SAAS,GAAG;AAClB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;;AAED;AACA,IAAI,OAAOC,QAAQ,KAAK,WAAW,EAAE;EACnC,MAAMC,YAAY,GAAGD,QAAQ,CAACE,aAAa,CAAC,OAAO,CAAC;EACpDD,YAAY,CAACE,WAAW,GAAGJ,SAAS;EACpCC,QAAQ,CAACI,IAAI,CAACC,WAAW,CAACJ,YAAY,CAAC;AACzC;AAEA,MAAMK,KAAK,GAAGA,CAAC;EAAEC;AAAa,CAAC,KAAK;EAAAC,EAAA;EAClC;EACA,MAAMC,QAAQ,GAAGf,MAAM,CAAC,IAAI,CAAC;EAC7B,MAAMgB,gBAAgB,GAAGhB,MAAM,CAAC,IAAI,CAAC;EACrC,MAAMiB,SAAS,GAAGjB,MAAM,CAAC,IAAI,CAAC;;EAE9B;EACA,MAAM,CAACkB,UAAU,EAAEC,aAAa,CAAC,GAAGrB,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAACsB,eAAe,EAAEC,kBAAkB,CAAC,GAAGvB,QAAQ,CAAC,IAAI,CAAC;EAC5D,MAAM,CAACwB,WAAW,EAAEC,cAAc,CAAC,GAAGzB,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAAC0B,oBAAoB,EAAEC,uBAAuB,CAAC,GAAG3B,QAAQ,CAAC,KAAK,CAAC;EACvE,MAAM,CAAC4B,SAAS,EAAEC,YAAY,CAAC,GAAG7B,QAAQ,CAAC,SAAS,CAAC;EACrD,MAAM,CAAC8B,aAAa,EAAEC,gBAAgB,CAAC,GAAG/B,QAAQ,CAAC,IAAI,CAAC;EACxD,MAAM,CAACgC,QAAQ,EAAEC,WAAW,CAAC,GAAGjC,QAAQ,CAAC,KAAK,CAAC;;EAE/C;EACA,MAAM,CAACkC,UAAU,EAAEC,aAAa,CAAC,GAAGnC,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC;EACrD,MAAM,CAACoC,aAAa,EAAEC,gBAAgB,CAAC,GAAGrC,QAAQ,CAAC,EAAE,CAAC,CAAC,CAAC;EACxD,MAAM,CAACsC,kBAAkB,EAAEC,qBAAqB,CAAC,GAAGvC,QAAQ,CAAC,KAAK,CAAC;;EAEnE;EACA,MAAM,CAACwC,oBAAoB,EAAEC,uBAAuB,CAAC,GAAGzC,QAAQ,CAAC,KAAK,CAAC;EACvE,MAAM,CAAC0C,SAAS,EAAEC,YAAY,CAAC,GAAG3C,QAAQ,CAAC,CAAC,CAAC;EAC7C,MAAM,CAAC4C,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG7C,QAAQ,CAAC,KAAK,CAAC;EAC/D,MAAM,CAAC8C,iBAAiB,EAAEC,oBAAoB,CAAC,GAAG/C,QAAQ,CAAC,KAAK,CAAC;;EAEjE;EACAC,SAAS,CAAC,MAAM;IACd,MAAM+C,WAAW,GAAGA,CAAA,KAAM;MACxBf,WAAW,CAACgB,MAAM,CAACC,UAAU,IAAI,GAAG,CAAC;IACvC,CAAC;;IAED;IACA,MAAMC,KAAK,GAAGA,CAAA,KAAM;MAClB,MAAMC,EAAE,GAAGH,MAAM,CAACI,WAAW,GAAG,IAAI;MACpC7C,QAAQ,CAAC8C,eAAe,CAACC,KAAK,CAACC,WAAW,CAAC,MAAM,EAAE,GAAGJ,EAAE,IAAI,CAAC;IAC/D,CAAC;IAEDJ,WAAW,CAAC,CAAC;IACbG,KAAK,CAAC,CAAC;IAEPF,MAAM,CAACQ,gBAAgB,CAAC,QAAQ,EAAE,MAAM;MACtCT,WAAW,CAAC,CAAC;MACbG,KAAK,CAAC,CAAC;IACT,CAAC,CAAC;IAEFF,MAAM,CAACQ,gBAAgB,CAAC,mBAAmB,EAAE,MAAM;MACjDC,UAAU,CAAC,MAAM;QACfP,KAAK,CAAC,CAAC;MACT,CAAC,EAAE,GAAG,CAAC;IACT,CAAC,CAAC;IAEF,OAAO,MAAM;MACXF,MAAM,CAACU,mBAAmB,CAAC,QAAQ,EAAEX,WAAW,CAAC;MACjDC,MAAM,CAACU,mBAAmB,CAAC,mBAAmB,EAAER,KAAK,CAAC;IACxD,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA;EACA;EACA;EACA;;EAEA;EACA,MAAMS,mBAAmB,GAAG;IAC1BC,GAAG,EAAE,EAAE;IAAK;IACZC,KAAK,EAAE,EAAE,CAAG;EACd,CAAC;EAED,MAAMC,yBAAyB,GAAG,GAAG,CAAC,CAAC;EACvC,MAAMC,iBAAiB,GAAG,GAAG,CAAC,CAAC;EAC/B,MAAMC,kBAAkB,GAAG,GAAG,CAAC,CAAC;;EAEhC;EACA,MAAMC,WAAW,GAAG,EAAE,CAAC,CAAC;EACxB,MAAMC,cAAc,GAAG,EAAE,CAAC,CAAC;EAC3B,MAAMC,YAAY,GAAG,EAAE,CAAC,CAAC;EACzB,MAAMC,eAAe,GAAG,EAAE,CAAC,CAAC;;EAE5B;EACA;EACA;EACA;EACA;;EAEA;EACA,MAAMC,wBAAwB,GAAGA,CAACC,KAAK,EAAEC,cAAc,EAAEC,eAAe,KAAK;IAC3E;IACA,MAAMC,gBAAgB,GAAGd,mBAAmB,CAAC1B,UAAU,CAAC;;IAExD;IACA,MAAMyC,cAAc,GAAGvC,aAAa,GAAGsC,gBAAgB;;IAEvD;IACA,MAAME,YAAY,GAAGb,yBAAyB,GAAG3B,aAAa;;IAE9D;IACA,MAAMyC,aAAa,GAAGN,KAAK,CAACO,UAAU,GAAGF,YAAY;IACrD,MAAMG,cAAc,GAAGR,KAAK,CAACS,WAAW,GAAGJ,YAAY;IACvD,MAAMK,eAAe,GAAGV,KAAK,CAACW,YAAY,GAAGN,YAAY;;IAEzD;IACA,MAAMO,iBAAiB,GAAIN,aAAa,GAAGb,iBAAiB,GAAI,GAAG;IACnE,MAAMoB,kBAAkB,GAAIL,cAAc,GAAGd,kBAAkB,GAAI,GAAG;IACtE,MAAMoB,mBAAmB,GAAIJ,eAAe,GAAGjB,iBAAiB,GAAI,GAAG;;IAEvE;IACA;IACA,MAAMsB,SAAS,GAAG,EAAE,CAAC,CAAC;IACtB,MAAMC,SAAS,GAAG,EAAE,CAAC,CAAC;;IAEtB,OAAO;MACLC,KAAK,EAAEC,IAAI,CAACC,GAAG,CAACP,iBAAiB,EAAE,CAAC,CAAC;MAAE;MACvCQ,MAAM,EAAEF,IAAI,CAACC,GAAG,CAACN,kBAAkB,EAAE,EAAE,CAAC;MAAE;MAC1CF,YAAY,EAAEG,mBAAmB;MACjCC,SAAS;MACTC,SAAS;MACTK,KAAK,EAAEH,IAAI,CAACI,GAAG,CAACV,iBAAiB,GAAG,EAAE,EAAEC,kBAAkB,GAAG,EAAE,CAAC,GAAGT,cAAc;MAAE;MACnFmB,SAAS,EAAEvB,KAAK,CAACO,UAAU;MAC3BiB,UAAU,EAAExB,KAAK,CAACS,WAAW;MAC7BgB,YAAY,EAAEzB,KAAK,CAACyB,YAAY;MAChCrB,cAAc,CAAC;IACjB,CAAC;EACH,CAAC;;EAED;EACA,MAAMsB,gBAAgB,GAAGA,CAACC,SAAS,EAAE1E,WAAW,KAAK;IACnD,MAAM2E,cAAc,GAAG7B,wBAAwB,CAAC4B,SAAS,EAAE,GAAG,EAAE,GAAG,CAAC;;IAEpE;IACA;IACA,IAAIE,SAAS,GAAGD,cAAc,CAACb,SAAS;IACxC,IAAIe,SAAS,GAAGF,cAAc,CAACZ,SAAS,GAAG,CAAC,CAAC,CAAC;;IAE9C;IACA,IAAI/D,WAAW,EAAE;MACf4E,SAAS,GAAGD,cAAc,CAACb,SAAS,GAAG,CAAC,CAAC,CAAC;IAC5C;;IAEA;IACA,QAAQY,SAAS,CAACI,IAAI;MACpB,KAAK,YAAY;QACf;QACAD,SAAS,IAAI,CAAC;QACd;MACF,KAAK,QAAQ;QACX;QACAA,SAAS,IAAI,GAAG;QAChB;MACF,KAAK,OAAO;QACV;QACAA,SAAS,IAAI,GAAG;QAChB;MACF;QACE;IACJ;IAEA,OAAO;MACL,GAAGF,cAAc;MACjBb,SAAS,EAAEc,SAAS;MACpBb,SAAS,EAAEc;IACb,CAAC;EACH,CAAC;EACD;EACA,MAAME,OAAO,GAAG,CACd;IACEC,IAAI,EAAE,eAAe;IACrBC,IAAI,EAAE,qBAAqB;IAC3B;IACAT,YAAY,EAAE,EAAE;IAAE;IAClBU,aAAa,EAAE,IAAI;IAAE;IACrB5B,UAAU,EAAE,EAAE;IAAE;IAChBE,WAAW,EAAE,EAAE;IAAE;IACjBE,YAAY,EAAE,EAAE;IAAE;IAClBoB,IAAI,EAAE,OAAO;IACbK,QAAQ,EAAE;EACZ,CAAC,EACD;IACEH,IAAI,EAAE,eAAe;IACrBC,IAAI,EAAE,qBAAqB;IAC3B;IACAT,YAAY,EAAE,EAAE;IAAE;IAClBU,aAAa,EAAE,IAAI;IAAE;IACrB5B,UAAU,EAAE,EAAE;IAAE;IAChBE,WAAW,EAAE,IAAI;IAAE;IACnBE,YAAY,EAAE,EAAE;IAAE;IAClBoB,IAAI,EAAE,OAAO;IACbK,QAAQ,EAAE;EACZ,CAAC,EACD;IACEH,IAAI,EAAE,aAAa;IACnBC,IAAI,EAAE,qBAAqB;IAC3B;IACAT,YAAY,EAAE,EAAE;IAAE;IAClBU,aAAa,EAAE,GAAG;IAAE;IACpB5B,UAAU,EAAE,EAAE;IAAE;IAChBE,WAAW,EAAE,EAAE;IAAE;IACjBE,YAAY,EAAE,EAAE;IAAE;IAClBoB,IAAI,EAAE,QAAQ;IACdK,QAAQ,EAAE;EACZ,CAAC,EACD;IACEH,IAAI,EAAE,YAAY;IAClBC,IAAI,EAAE,qBAAqB;IAC3B;IACAT,YAAY,EAAE,EAAE;IAAE;IAClBU,aAAa,EAAE,IAAI;IAAE;IACrB5B,UAAU,EAAE,EAAE;IAAE;IAChBE,WAAW,EAAE,EAAE;IAAE;IACjBE,YAAY,EAAE,EAAE;IAAE;IAClBoB,IAAI,EAAE,YAAY;IAClBK,QAAQ,EAAE;EACZ,CAAC,EACD;IACEH,IAAI,EAAE,YAAY;IAClBC,IAAI,EAAE,qBAAqB;IAC3B;IACAT,YAAY,EAAE,EAAE;IAAE;IAClBU,aAAa,EAAE,CAAC;IAAE;IAClB5B,UAAU,EAAE,EAAE;IAAE;IAChBE,WAAW,EAAE,EAAE;IAAE;IACjBE,YAAY,EAAE,EAAE;IAAE;IAClBoB,IAAI,EAAE,YAAY;IAClBK,QAAQ,EAAE;EACZ,CAAC,EACD;IACEH,IAAI,EAAE,WAAW;IACjBC,IAAI,EAAE,qBAAqB;IAC3B;IACAT,YAAY,EAAE,EAAE;IAAE;IAClBU,aAAa,EAAE,EAAE;IAAE;IACnB5B,UAAU,EAAE,EAAE;IAAE;IAChBE,WAAW,EAAE,EAAE;IAAE;IACjBE,YAAY,EAAE,EAAE;IAAE;IAClBoB,IAAI,EAAE,SAAS;IACfK,QAAQ,EAAE;EACZ,CAAC,CACF;EAED,MAAMC,SAAS,GAAG,CAChB;IAAEJ,IAAI,EAAE,cAAc;IAAEC,IAAI,EAAE;EAA2B,CAAC,EAC1D;IAAED,IAAI,EAAE,aAAa;IAAEC,IAAI,EAAE;EAA2B,CAAC,EACzD;IAAED,IAAI,EAAE,cAAc;IAAEC,IAAI,EAAE;EAA2B,CAAC,EAC1D;IAAED,IAAI,EAAE,gBAAgB;IAAEC,IAAI,EAAE;EAA2B,CAAC,EAC5D;IAAED,IAAI,EAAE,cAAc;IAAEC,IAAI,EAAE;EAA2B,CAAC,EAC1D;IAAED,IAAI,EAAE,gBAAgB;IAAEC,IAAI,EAAE;EAA2B,CAAC,CAC7D;;EAED;EACA,MAAMI,UAAU,GAAG,MAAAA,CAAA,KAAY;IAC7B,IAAI;MACF,MAAMC,MAAM,GAAG,MAAMC,SAAS,CAACC,YAAY,CAACC,YAAY,CAAC;QACvDC,KAAK,EAAE;UACLC,UAAU,EAAE,aAAa;UACzB3B,KAAK,EAAE;YAAE4B,KAAK,EAAE;UAAK,CAAC;UACtBzB,MAAM,EAAE;YAAEyB,KAAK,EAAE;UAAK;QACxB;MACF,CAAC,CAAC;MACF,IAAInG,QAAQ,CAACoG,OAAO,EAAE;QACpBpG,QAAQ,CAACoG,OAAO,CAACC,SAAS,GAAGR,MAAM;MACrC;IACF,CAAC,CAAC,OAAOS,GAAG,EAAE;MACZC,OAAO,CAACC,KAAK,CAAC,yBAAyB,EAAEF,GAAG,CAAC;MAC7C;MACA,IAAIrG,gBAAgB,CAACmG,OAAO,EAAE;QAC5BnG,gBAAgB,CAACmG,OAAO,CAACK,GAAG,GAAG,iBAAiB;QAChDxG,gBAAgB,CAACmG,OAAO,CAAC9D,KAAK,CAACoE,OAAO,GAAG,OAAO;MAClD;MACAH,OAAO,CAACI,GAAG,CAAC,kCAAkC,CAAC;MAC/CvG,aAAa,CAAC,IAAI,CAAC;MACnBM,uBAAuB,CAAC,IAAI,CAAC;IAC/B;EACF,CAAC;;EAED;EACA,MAAMkG,YAAY,GAAGA,CAAA,KAAM;IACzB,IAAI,CAAC5G,QAAQ,CAACoG,OAAO,IAAI,CAAClG,SAAS,CAACkG,OAAO,EAAE,OAAO,IAAI;IAExD,MAAMS,MAAM,GAAG3G,SAAS,CAACkG,OAAO;IAChC,MAAMH,KAAK,GAAGjG,QAAQ,CAACoG,OAAO;IAC9BS,MAAM,CAACtC,KAAK,GAAG0B,KAAK,CAACa,UAAU;IAC/BD,MAAM,CAACnC,MAAM,GAAGuB,KAAK,CAACc,WAAW;IACjC,MAAMC,GAAG,GAAGH,MAAM,CAACI,UAAU,CAAC,IAAI,CAAC;IACnCD,GAAG,CAACE,SAAS,CAACjB,KAAK,EAAE,CAAC,EAAE,CAAC,EAAEY,MAAM,CAACtC,KAAK,EAAEsC,MAAM,CAACnC,MAAM,CAAC;IACvD,OAAOmC,MAAM,CAACM,SAAS,CAAC,WAAW,CAAC;EACtC,CAAC;;EAED;EACA,MAAMC,gBAAgB,GAAGA,CAACC,UAAU,EAAEC,WAAW,GAAG,OAAO,KAAK;IAC9D,MAAMC,GAAG,GAAG,IAAIC,KAAK,CAAC,CAAC;IACvBD,GAAG,CAACE,WAAW,GAAG,WAAW;IAE7BF,GAAG,CAACG,MAAM,GAAG,YAAW;MACtB,MAAMb,MAAM,GAAGtH,QAAQ,CAACE,aAAa,CAAC,QAAQ,CAAC;MAC/C,MAAMuH,GAAG,GAAGH,MAAM,CAACI,UAAU,CAAC,IAAI,CAAC;MACnCJ,MAAM,CAACtC,KAAK,GAAGgD,GAAG,CAACI,YAAY;MAC/Bd,MAAM,CAACnC,MAAM,GAAG6C,GAAG,CAACK,aAAa;MACjCZ,GAAG,CAACE,SAAS,CAACK,GAAG,EAAE,CAAC,EAAE,CAAC,CAAC;MAExB,IAAI;QACF,MAAMM,SAAS,GAAGb,GAAG,CAACc,YAAY,CAAC,CAAC,EAAE,CAAC,EAAEjB,MAAM,CAACtC,KAAK,EAAEsC,MAAM,CAACnC,MAAM,CAAC;QACrE,MAAMqD,IAAI,GAAGF,SAAS,CAACE,IAAI;QAC3B,MAAMxD,KAAK,GAAGsC,MAAM,CAACtC,KAAK;QAC1B,MAAMG,MAAM,GAAGmC,MAAM,CAACnC,MAAM;;QAE5B;QACA,MAAMsD,UAAU,GAAG,IAAIC,GAAG,CAAC,CAAC;QAC5B,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGxD,MAAM,GAAG,CAAC,EAAEwD,CAAC,EAAE,EAAE;UACnC,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG5D,KAAK,GAAG,CAAC,EAAE4D,CAAC,EAAE,EAAE;YAClC,MAAMC,GAAG,GAAG,CAACF,CAAC,GAAG3D,KAAK,GAAG4D,CAAC,IAAI,CAAC;YAC/B,MAAME,CAAC,GAAGN,IAAI,CAACK,GAAG,CAAC;YACnB,MAAME,CAAC,GAAGP,IAAI,CAACK,GAAG,GAAG,CAAC,CAAC;YACvB,MAAMG,CAAC,GAAGR,IAAI,CAACK,GAAG,GAAG,CAAC,CAAC;;YAEvB;YACA,IAAII,MAAM,GAAG,KAAK;YAClB,KAAK,IAAIC,EAAE,GAAG,CAAC,CAAC,EAAEA,EAAE,IAAI,CAAC,EAAEA,EAAE,EAAE,EAAE;cAC/B,KAAK,IAAIC,EAAE,GAAG,CAAC,CAAC,EAAEA,EAAE,IAAI,CAAC,EAAEA,EAAE,EAAE,EAAE;gBAC/B,IAAIA,EAAE,KAAK,CAAC,IAAID,EAAE,KAAK,CAAC,EAAE;gBAC1B,MAAME,WAAW,GAAG,CAAC,CAACT,CAAC,GAAGO,EAAE,IAAIlE,KAAK,IAAI4D,CAAC,GAAGO,EAAE,CAAC,IAAI,CAAC;gBACrD,MAAME,EAAE,GAAGb,IAAI,CAACY,WAAW,CAAC;gBAC5B,MAAME,EAAE,GAAGd,IAAI,CAACY,WAAW,GAAG,CAAC,CAAC;gBAChC,MAAMG,EAAE,GAAGf,IAAI,CAACY,WAAW,GAAG,CAAC,CAAC;;gBAEhC;gBACA,MAAMI,SAAS,GAAGvE,IAAI,CAACwE,GAAG,CAACX,CAAC,GAAGO,EAAE,CAAC,GAAGpE,IAAI,CAACwE,GAAG,CAACV,CAAC,GAAGO,EAAE,CAAC,GAAGrE,IAAI,CAACwE,GAAG,CAACT,CAAC,GAAGO,EAAE,CAAC;gBACxE,IAAIC,SAAS,GAAG,EAAE,EAAE;kBAClBP,MAAM,GAAG,IAAI;kBACb;gBACF;cACF;cACA,IAAIA,MAAM,EAAE;YACd;YAEA,IAAIA,MAAM,EAAE;cACVR,UAAU,CAACiB,GAAG,CAACb,GAAG,GAAG,CAAC,CAAC;YACzB;UACF;QACF;;QAEA;QACA,KAAK,IAAIc,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGnB,IAAI,CAACoB,MAAM,EAAED,CAAC,IAAI,CAAC,EAAE;UACvC,MAAMb,CAAC,GAAGN,IAAI,CAACmB,CAAC,CAAC;UACjB,MAAMZ,CAAC,GAAGP,IAAI,CAACmB,CAAC,GAAG,CAAC,CAAC;UACrB,MAAMX,CAAC,GAAGR,IAAI,CAACmB,CAAC,GAAG,CAAC,CAAC;UACrB,MAAME,UAAU,GAAGF,CAAC,GAAG,CAAC;;UAExB;UACA,MAAMG,UAAU,GAAG,CAAChB,CAAC,GAAGC,CAAC,GAAGC,CAAC,IAAI,CAAC;;UAElC;UACA,MAAMe,UAAU,GAAGtB,UAAU,CAACuB,GAAG,CAACH,UAAU,CAAC;;UAE7C;UACA,MAAMI,WAAW,GACfnB,CAAC,GAAG,GAAG,IACPC,CAAC,GAAG,GAAG,IACPC,CAAC,GAAG,GAAG,IACP/D,IAAI,CAACwE,GAAG,CAACX,CAAC,GAAGC,CAAC,CAAC,GAAG,EAAE,IACpB9D,IAAI,CAACwE,GAAG,CAACV,CAAC,GAAGC,CAAC,CAAC,GAAG,EAAE,IACpB,CAACe,UACF;;UAED;UACA,IAAIE,WAAW,EAAE;YACfzB,IAAI,CAACmB,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;UACnB,CAAC,MAAM,IAAIG,UAAU,GAAG,GAAG,IAAI,CAACC,UAAU,EAAE;YAC1C;YACAvB,IAAI,CAACmB,CAAC,GAAG,CAAC,CAAC,GAAG1E,IAAI,CAACC,GAAG,CAAC,CAAC,EAAEsD,IAAI,CAACmB,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,CAAC;UAC7C;QACF;QAEAlC,GAAG,CAACyC,YAAY,CAAC5B,SAAS,EAAE,CAAC,EAAE,CAAC,CAAC;QACjCR,UAAU,CAACZ,GAAG,GAAGI,MAAM,CAACM,SAAS,CAAC,WAAW,CAAC;;QAE9C;QACAE,UAAU,CAAC/E,KAAK,CAACoH,MAAM,GAAG,MAAM;QAChCrC,UAAU,CAAC/E,KAAK,CAACqH,YAAY,GAAG,QAAQ;QACxCtC,UAAU,CAAC/E,KAAK,CAACsH,OAAO,GAAG,GAAG;MAEhC,CAAC,CAAC,OAAOC,CAAC,EAAE;QACVtD,OAAO,CAACuD,IAAI,CAAC,0BAA0B,EAAED,CAAC,CAAC;QAC3C;QACAxC,UAAU,CAAC/E,KAAK,CAACoH,MAAM,GAAG,MAAM;QAChCrC,UAAU,CAAC/E,KAAK,CAACqH,YAAY,GAAG,QAAQ;QACxCtC,UAAU,CAAC/E,KAAK,CAACsH,OAAO,GAAG,GAAG;MAChC;IACF,CAAC;IAEDrC,GAAG,CAACwC,OAAO,GAAG,YAAW;MACvBxD,OAAO,CAACuD,IAAI,CAAC,sBAAsB,CAAC;MACpC;MACAzC,UAAU,CAAC/E,KAAK,CAACoH,MAAM,GAAG,MAAM;MAChCrC,UAAU,CAAC/E,KAAK,CAACqH,YAAY,GAAG,QAAQ;MACxCtC,UAAU,CAAC/E,KAAK,CAACsH,OAAO,GAAG,GAAG;IAChC,CAAC;IAEDrC,GAAG,CAACd,GAAG,GAAGY,UAAU,CAACZ,GAAG;EAC1B,CAAC;;EAED;EACA,MAAMuD,qBAAqB,GAAInC,SAAS,IAAK;IAC3C;IACA,OAAOrD,IAAI,CAACyF,MAAM,CAAC,CAAC,GAAG,GAAG;EAC5B,CAAC;;EAED;EACA,MAAMC,oBAAoB,GAAGA,CAAA,KAAM;IACjC,IAAI,CAAClK,QAAQ,CAACoG,OAAO,IAAI,CAAClG,SAAS,CAACkG,OAAO,EAAE,OAAO,KAAK;IAEzD,MAAMH,KAAK,GAAGjG,QAAQ,CAACoG,OAAO;IAC9B,MAAMS,MAAM,GAAG3G,SAAS,CAACkG,OAAO;IAChC,MAAMY,GAAG,GAAGH,MAAM,CAACI,UAAU,CAAC,IAAI,CAAC;;IAEnC;IACAJ,MAAM,CAACtC,KAAK,GAAG0B,KAAK,CAACa,UAAU;IAC/BD,MAAM,CAACnC,MAAM,GAAGuB,KAAK,CAACc,WAAW;;IAEjC;IACAC,GAAG,CAACE,SAAS,CAACjB,KAAK,EAAE,CAAC,EAAE,CAAC,EAAEY,MAAM,CAACtC,KAAK,EAAEsC,MAAM,CAACnC,MAAM,CAAC;;IAEvD;IACA,MAAMyF,cAAc,GAAGlE,KAAK,CAACmE,aAAa;IAC1C,MAAMC,aAAa,GAAGF,cAAc,CAACG,qBAAqB,CAAC,CAAC;;IAE5D;IACA,MAAMC,WAAW,GAAGtE,KAAK,CAACa,UAAU,GAAGb,KAAK,CAACc,WAAW;IACxD,MAAMyD,eAAe,GAAGH,aAAa,CAAC9F,KAAK,GAAG8F,aAAa,CAAC3F,MAAM;IAElE,IAAI+F,YAAY,EAAEC,aAAa,EAAEC,OAAO,EAAEC,OAAO;IAEjD,IAAIL,WAAW,GAAGC,eAAe,EAAE;MACjC;MACAE,aAAa,GAAGL,aAAa,CAAC3F,MAAM;MACpC+F,YAAY,GAAGC,aAAa,GAAGH,WAAW;MAC1CI,OAAO,GAAG,CAACF,YAAY,GAAGJ,aAAa,CAAC9F,KAAK,IAAI,CAAC;MAClDqG,OAAO,GAAG,CAAC;IACb,CAAC,MAAM;MACL;MACAH,YAAY,GAAGJ,aAAa,CAAC9F,KAAK;MAClCmG,aAAa,GAAGD,YAAY,GAAGF,WAAW;MAC1CI,OAAO,GAAG,CAAC;MACXC,OAAO,GAAG,CAACF,aAAa,GAAGL,aAAa,CAAC3F,MAAM,IAAI,CAAC;IACtD;;IAEA;IACA;IACA;IACA;;IAEA,MAAMmG,MAAM,GAAGhE,MAAM,CAACtC,KAAK,GAAGkG,YAAY;IAC1C,MAAMK,MAAM,GAAGjE,MAAM,CAACnC,MAAM,GAAGgG,aAAa;;IAE5C;IACA,MAAMK,KAAK,GAAGvG,IAAI,CAACC,GAAG,CAAC,CAAC,EAAE,CAAE,GAAG,GAAG,GAAG,GAAIgG,YAAY,GAAGE,OAAO,IAAIE,MAAM,CAAC;IAC1E,MAAMG,KAAK,GAAGxG,IAAI,CAACC,GAAG,CAAC,CAAC,EAAE,CAAE,GAAG,GAAG,GAAG,GAAIiG,aAAa,GAAGE,OAAO,IAAIE,MAAM,CAAC;IAC3E,MAAMG,SAAS,GAAGzG,IAAI,CAACI,GAAG,CAACiC,MAAM,CAACtC,KAAK,GAAGwG,KAAK,EAAI,GAAG,GAAG,GAAG,GAAIN,YAAY,GAAII,MAAM,CAAC;IACvF,MAAMK,UAAU,GAAG1G,IAAI,CAACI,GAAG,CAACiC,MAAM,CAACnC,MAAM,GAAGsG,KAAK,EAAI,GAAG,GAAG,GAAG,GAAIN,aAAa,GAAII,MAAM,CAAC;;IAE1F;IACA,MAAMK,OAAO,GAAG3G,IAAI,CAACC,GAAG,CAAC,CAAC,EAAE,CAAE,GAAG,GAAG,GAAG,GAAIgG,YAAY,GAAGE,OAAO,IAAIE,MAAM,CAAC,CAAC,CAAC;IAC9E,MAAMO,OAAO,GAAG5G,IAAI,CAACC,GAAG,CAAC,CAAC,EAAE,CAAE,GAAG,GAAG,GAAG,GAAIiG,aAAa,GAAGE,OAAO,IAAIE,MAAM,CAAC,CAAC,CAAC;IAC/E,MAAMO,WAAW,GAAG7G,IAAI,CAACI,GAAG,CAACiC,MAAM,CAACtC,KAAK,GAAG4G,OAAO,EAAI,GAAG,GAAG,GAAG,GAAIV,YAAY,GAAII,MAAM,CAAC,CAAC,CAAC;IAC7F,MAAMS,YAAY,GAAG9G,IAAI,CAACI,GAAG,CAACiC,MAAM,CAACnC,MAAM,GAAG0G,OAAO,EAAI,GAAG,GAAG,GAAG,GAAIV,aAAa,GAAII,MAAM,CAAC,CAAC,CAAC;;IAEhG,IAAI;MACF;MACA,MAAMS,aAAa,GAAGvE,GAAG,CAACc,YAAY,CAACiD,KAAK,EAAEC,KAAK,EAAEC,SAAS,EAAEC,UAAU,CAAC;MAC3E,MAAMM,QAAQ,GAAGD,aAAa,CAACxD,IAAI;;MAEnC;MACA,MAAM0D,eAAe,GAAGzE,GAAG,CAACc,YAAY,CAACqD,OAAO,EAAEC,OAAO,EAAEC,WAAW,EAAEC,YAAY,CAAC;MACrF,MAAMI,UAAU,GAAGD,eAAe,CAAC1D,IAAI;MAEvC,IAAI4D,cAAc,GAAG,CAAC;MACtB,IAAIC,eAAe,GAAG,CAAC;MACvB,IAAIC,gBAAgB,GAAG,CAAC;MACxB,IAAIC,iBAAiB,GAAG,CAAC;;MAEzB;MACA,MAAMC,UAAU,GAAGA,CAAC1D,CAAC,EAAEC,CAAC,EAAEC,CAAC,KAAK;QAC9B;QACA,MAAMyD,UAAU,GAAG3D,CAAC,GAAG,EAAE,IAAIC,CAAC,GAAG,EAAE,IAAIC,CAAC,GAAG,EAAE,IAAIF,CAAC,GAAGC,CAAC,IAAID,CAAC,GAAGE,CAAC,IAAI/D,IAAI,CAACwE,GAAG,CAACX,CAAC,GAAGC,CAAC,CAAC,GAAG,EAAE;QACvF,MAAM2D,UAAU,GAAG5D,CAAC,GAAG,GAAG,IAAIC,CAAC,GAAG,GAAG,IAAIC,CAAC,GAAG,GAAG,IAAI/D,IAAI,CAACwE,GAAG,CAACX,CAAC,GAAGC,CAAC,CAAC,GAAG,EAAE,IAAI9D,IAAI,CAACwE,GAAG,CAACV,CAAC,GAAGC,CAAC,CAAC,GAAG,EAAE;QAChG,MAAM2D,UAAU,GAAG7D,CAAC,GAAG,GAAG,IAAIC,CAAC,GAAG,EAAE,IAAIC,CAAC,GAAG,EAAE,IAAIF,CAAC,GAAGC,CAAC,IAAIA,CAAC,GAAGC,CAAC;QAChE,MAAM4D,UAAU,GAAG9D,CAAC,GAAG,GAAG,IAAIC,CAAC,GAAG,GAAG,IAAIC,CAAC,GAAG,EAAE,IAAIF,CAAC,GAAGC,CAAC,IAAIA,CAAC,IAAIC,CAAC;QAElE,OAAOyD,UAAU,IAAIC,UAAU,IAAIC,UAAU,IAAIC,UAAU;MAC7D,CAAC;;MAED;MACA,KAAK,IAAIjD,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGsC,QAAQ,CAACrC,MAAM,EAAED,CAAC,IAAI,CAAC,EAAE;QAC3C,MAAMb,CAAC,GAAGmD,QAAQ,CAACtC,CAAC,CAAC;QACrB,MAAMZ,CAAC,GAAGkD,QAAQ,CAACtC,CAAC,GAAG,CAAC,CAAC;QACzB,MAAMX,CAAC,GAAGiD,QAAQ,CAACtC,CAAC,GAAG,CAAC,CAAC;QAEzB,IAAI6C,UAAU,CAAC1D,CAAC,EAAEC,CAAC,EAAEC,CAAC,CAAC,EAAE;UACvBoD,cAAc,EAAE;QAClB;QACAC,eAAe,EAAE;MACnB;;MAEA;MACA,KAAK,IAAI1C,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGwC,UAAU,CAACvC,MAAM,EAAED,CAAC,IAAI,CAAC,EAAE;QAC7C,MAAMb,CAAC,GAAGqD,UAAU,CAACxC,CAAC,CAAC;QACvB,MAAMZ,CAAC,GAAGoD,UAAU,CAACxC,CAAC,GAAG,CAAC,CAAC;QAC3B,MAAMX,CAAC,GAAGmD,UAAU,CAACxC,CAAC,GAAG,CAAC,CAAC;QAE3B,IAAI6C,UAAU,CAAC1D,CAAC,EAAEC,CAAC,EAAEC,CAAC,CAAC,EAAE;UACvBsD,gBAAgB,EAAE;QACpB;QACAC,iBAAiB,EAAE;MACrB;;MAEA;MACA,MAAMM,aAAa,GAAGT,cAAc,GAAGC,eAAe;MACtD,MAAMS,eAAe,GAAGR,gBAAgB,GAAGC,iBAAiB;;MAE5D;MACA;MACA;MACA,MAAMQ,eAAe,GAAGF,aAAa,GAAG,IAAI;MAC5C,MAAMG,cAAc,GAAGF,eAAe,GAAG,IAAI;MAE7C,OAAOC,eAAe,IAAIC,cAAc;IAE1C,CAAC,CAAC,OAAO/F,KAAK,EAAE;MACdD,OAAO,CAACuD,IAAI,CAAC,uBAAuB,EAAEtD,KAAK,CAAC;MAC5C,OAAO,KAAK;IACd;EACF,CAAC;;EAED;EACA,MAAMgG,2BAA2B,GAAGA,CAACC,WAAW,EAAEnF,WAAW,KAAK;IAChE;IACAhH,kBAAkB,CAAC,IAAI,CAAC;;IAExB;IACA,MAAM2E,SAAS,GAAGK,OAAO,CAACoH,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACnH,IAAI,KAAKiH,WAAW,CAAC;;IAE3D;IACAhK,UAAU,CAAC,MAAM;MACfnC,kBAAkB,CAAC;QACjBkF,IAAI,EAAEiH,WAAW;QACjB/G,QAAQ,EAAE,CAAAT,SAAS,aAATA,SAAS,uBAATA,SAAS,CAAES,QAAQ,KAAI,EAAE,CAAC;MACtC,CAAC,CAAC;IACJ,CAAC,EAAE,EAAE,CAAC;EACR,CAAC;;EAED;EACA,MAAMkH,aAAa,GAAGA,CAAA,KAAM;IAC1B,IAAI,CAACzM,UAAU,EAAE;MACf,MAAM0M,eAAe,GAAGjG,YAAY,CAAC,CAAC;MACtC,IAAI3G,gBAAgB,CAACmG,OAAO,IAAIyG,eAAe,EAAE;QAC/C5M,gBAAgB,CAACmG,OAAO,CAACK,GAAG,GAAGoG,eAAe;QAC9C5M,gBAAgB,CAACmG,OAAO,CAAC9D,KAAK,CAACoE,OAAO,GAAG,OAAO;MAClD;MACAtG,aAAa,CAAC,IAAI,CAAC;MACnBkB,qBAAqB,CAAC,IAAI,CAAC,CAAC,CAAC;MAC7BR,gBAAgB,CAAC,KAAK,CAAC;;MAEvB;MACA,IAAIZ,SAAS,CAACkG,OAAO,IAAIpG,QAAQ,CAACoG,OAAO,EAAE;QACzC,MAAMS,MAAM,GAAG3G,SAAS,CAACkG,OAAO;QAChC,MAAMY,GAAG,GAAGH,MAAM,CAACI,UAAU,CAAC,IAAI,CAAC;QACnCJ,MAAM,CAACtC,KAAK,GAAGvE,QAAQ,CAACoG,OAAO,CAACU,UAAU;QAC1CD,MAAM,CAACnC,MAAM,GAAG1E,QAAQ,CAACoG,OAAO,CAACW,WAAW;QAC5CC,GAAG,CAACE,SAAS,CAAClH,QAAQ,CAACoG,OAAO,EAAE,CAAC,EAAE,CAAC,CAAC;QACrC,MAAMyB,SAAS,GAAGb,GAAG,CAACc,YAAY,CAAC,CAAC,EAAE,CAAC,EAAEjB,MAAM,CAACtC,KAAK,EAAEsC,MAAM,CAACnC,MAAM,CAAC;QACrElE,cAAc,CAACwJ,qBAAqB,CAACnC,SAAS,CAAC,CAAC;MAClD;IACF,CAAC,MAAM;MACLnH,uBAAuB,CAAC,IAAI,CAAC;IAC/B;EACF,CAAC;;EAED;EACA,MAAMoM,UAAU,GAAGA,CAAA,KAAM;IACvB,IAAI7M,gBAAgB,CAACmG,OAAO,EAAE;MAC5BnG,gBAAgB,CAACmG,OAAO,CAAC9D,KAAK,CAACoE,OAAO,GAAG,MAAM;IACjD;IACAtG,aAAa,CAAC,KAAK,CAAC;IACpBE,kBAAkB,CAAC,IAAI,CAAC;IACxBE,cAAc,CAAC,KAAK,CAAC;IACrBE,uBAAuB,CAAC,KAAK,CAAC;IAC9BY,qBAAqB,CAAC,KAAK,CAAC;IAC5BR,gBAAgB,CAAC,IAAI,CAAC;EACxB,CAAC;;EAED;EACA,MAAMiM,kBAAkB,GAAIC,MAAM,IAAK;IACrC9L,aAAa,CAAC8L,MAAM,CAAC;IACrB5L,gBAAgB,CAACuB,mBAAmB,CAACqK,MAAM,CAAC,CAAC;EAC/C,CAAC;;EAED;EACA,MAAMC,qBAAqB,GAAIC,IAAI,IAAK;IACtC9L,gBAAgB,CAAC8L,IAAI,CAAC;EACxB,CAAC;;EAED;EACA,MAAMC,wBAAwB,GAAGA,CAAA,KAAM;IACrC7L,qBAAqB,CAAC,KAAK,CAAC;IAC5BZ,uBAAuB,CAAC,IAAI,CAAC;EAC/B,CAAC;;EAED;EACA,MAAM0M,eAAe,GAAIC,OAAO,IAAK;IACnCzM,YAAY,CAACyM,OAAO,CAAC;EACvB,CAAC;;EAED;EACA,MAAMC,mBAAmB,GAAIC,OAAO,IAAK;IACvCf,2BAA2B,CAACe,OAAO,CAAC/H,IAAI,EAAE7E,SAAS,CAAC;EACtD,CAAC;;EAED;EACA3B,SAAS,CAAC,MAAM;IACd4G,UAAU,CAAC,CAAC;EACd,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA5G,SAAS,CAAC,MAAM;IACd,IAAI,CAACuC,oBAAoB,IAAIpB,UAAU,EAAE;IAEzC,MAAMqN,QAAQ,GAAGC,WAAW,CAAC,MAAM;MACjC,MAAMlB,cAAc,GAAGrC,oBAAoB,CAAC,CAAC;MAC7CtI,mBAAmB,CAAC2K,cAAc,CAAC;;MAEnC;MACA,IAAIA,cAAc,IAAI,CAAC1K,iBAAiB,IAAIJ,SAAS,KAAK,CAAC,EAAE;QAC3DK,oBAAoB,CAAC,IAAI,CAAC;MAC5B;;MAEA;MACA,IAAI,CAACyK,cAAc,IAAI1K,iBAAiB,EAAE;QACxCC,oBAAoB,CAAC,KAAK,CAAC;QAC3BJ,YAAY,CAAC,CAAC,CAAC;MACjB;IACF,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC;;IAET,OAAO,MAAMgM,aAAa,CAACF,QAAQ,CAAC;EACtC,CAAC,EAAE,CAACjM,oBAAoB,EAAEpB,UAAU,EAAE0B,iBAAiB,EAAEJ,SAAS,CAAC,CAAC;;EAEpE;EACAzC,SAAS,CAAC,MAAM;IACd,IAAI,CAAC6C,iBAAiB,IAAI1B,UAAU,EAAE;MACpCuB,YAAY,CAAC,CAAC,CAAC;MACf;IACF;;IAEA;IACA,IAAI,CAACC,gBAAgB,EAAE;MACrBG,oBAAoB,CAAC,KAAK,CAAC;MAC3BJ,YAAY,CAAC,CAAC,CAAC;MACf;IACF;;IAEA;IACAA,YAAY,CAAC,CAAC,CAAC;IAEf,MAAMiM,iBAAiB,GAAGF,WAAW,CAAC,MAAM;MAC1C/L,YAAY,CAACkM,IAAI,IAAI;QACnB;QACA,IAAI,CAACjM,gBAAgB,EAAE;UACrB+L,aAAa,CAACC,iBAAiB,CAAC;UAChC7L,oBAAoB,CAAC,KAAK,CAAC;UAC3B,OAAO,CAAC;QACV;QAEA,IAAI8L,IAAI,IAAI,CAAC,EAAE;UACb;UACAF,aAAa,CAACC,iBAAiB,CAAC;UAChC7L,oBAAoB,CAAC,KAAK,CAAC;UAC3BN,uBAAuB,CAAC,KAAK,CAAC,CAAC,CAAC;UAChCoL,aAAa,CAAC,CAAC;UACf,OAAO,CAAC;QACV;QACA,OAAOgB,IAAI,GAAG,CAAC;MACjB,CAAC,CAAC;IACJ,CAAC,EAAE,IAAI,CAAC;IAER,OAAO,MAAMF,aAAa,CAACC,iBAAiB,CAAC;EAC/C,CAAC,EAAE,CAAC9L,iBAAiB,EAAE1B,UAAU,EAAEwB,gBAAgB,CAAC,CAAC;;EAErD;EACA,MAAMkM,uBAAuB,GAAGA,CAAA,KAAM;IACpC,MAAMC,QAAQ,GAAG,CAACvM,oBAAoB;IACtCC,uBAAuB,CAACsM,QAAQ,CAAC;;IAEjC;IACA,IAAI,CAACA,QAAQ,EAAE;MACbhM,oBAAoB,CAAC,KAAK,CAAC;MAC3BJ,YAAY,CAAC,CAAC,CAAC;MACfE,mBAAmB,CAAC,KAAK,CAAC;IAC5B;EACF,CAAC;;EAED;EACA,MAAMmM,mBAAmB,GAAGA,CAAA,KAAM;IAChCvM,uBAAuB,CAAC,KAAK,CAAC;IAC9BM,oBAAoB,CAAC,KAAK,CAAC;IAC3BJ,YAAY,CAAC,CAAC,CAAC;IACfE,mBAAmB,CAAC,KAAK,CAAC;IAC1BN,qBAAqB,CAAC,KAAK,CAAC;IAC5BJ,aAAa,CAAC,KAAK,CAAC;IACpBE,gBAAgB,CAAC,EAAE,CAAC;IACpB0L,UAAU,CAAC,CAAC;EACd,CAAC;;EAED;EACA,MAAMkB,kBAAkB,GAAGA,CAAA,KAAM;IAC/B,OAAOrN,SAAS,KAAK,SAAS,GAAG2E,OAAO,GAAGK,SAAS;EACtD,CAAC;EAED,oBACExG,OAAA;IACEmD,KAAK,EAAE2L,MAAM,CAACC,SAAU;IACxBC,SAAS,EAAEpN,QAAQ,GAAG,oCAAoC,GAAG,EAAG;IAAAqN,QAAA,gBAEhEjP,OAAA;MAAKmD,KAAK,EAAE2L,MAAM,CAACI,eAAgB;MAAAD,QAAA,gBACjCjP,OAAA;QACEmP,GAAG,EAAEtO,QAAS;QACdsC,KAAK,EAAE2L,MAAM,CAACM,UAAW;QACzBC,QAAQ;QACRC,WAAW;QACXC,KAAK;MAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,eACF3P,OAAA;QAAQmP,GAAG,EAAEpO,SAAU;QAACoC,KAAK,EAAE;UAAEoE,OAAO,EAAE;QAAO;MAAE;QAAAiI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACtD3P,OAAA;QACEmP,GAAG,EAAErO,gBAAiB;QACtBqC,KAAK,EAAE2L,MAAM,CAACc,aAAc;QAC5BC,GAAG,EAAC;MAAe;QAAAL,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACpB,CAAC,EAGD,CAAC3O,UAAU,iBACVhB,OAAA;QACEmD,KAAK,EAAE2L,MAAM,CAACgB,OAAQ;QACtBC,OAAO,EAAEpP,YAAa;QACtB,cAAW,MAAM;QAAAsO,QAAA,EAClB;MAED;QAAAO,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CACT,EAGA,CAAC3O,UAAU,iBACVhB,OAAA;QAAKmD,KAAK,EAAE;UAAE6M,QAAQ,EAAE,UAAU;UAAEC,GAAG,EAAErO,QAAQ,GAAG,EAAE,GAAG,EAAE;UAAEsO,KAAK,EAAEtO,QAAQ,GAAG,EAAE,GAAG,EAAE;UAAEuO,MAAM,EAAE,EAAE;UAAE5I,OAAO,EAAE,MAAM;UAAE6I,aAAa,EAAE,QAAQ;UAAEC,UAAU,EAAE;QAAS,CAAE;QAAApB,QAAA,gBACnKjP,OAAA;UAAOmD,KAAK,EAAE;YAAEmN,KAAK,EAAE,SAAS;YAAEC,UAAU,EAAE,GAAG;YAAEC,QAAQ,EAAE5O,QAAQ,GAAG,EAAE,GAAG,EAAE;YAAE6O,YAAY,EAAE,CAAC;YAAEC,aAAa,EAAE;UAAI,CAAE;UAAAzB,QAAA,EAAC;QAAY;UAAAO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eAC5I3P,OAAA;UAAOgP,SAAS,EAAC,QAAQ;UAAAC,QAAA,gBACvBjP,OAAA;YACEkG,IAAI,EAAC,UAAU;YACfyK,OAAO,EAAEvO,oBAAqB;YAC9BwO,QAAQ,EAAElC,uBAAwB;YAClCmC,QAAQ,EAAEnO,iBAAkB;YAC5B,cAAW;UAAqB;YAAA8M,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjC,CAAC,eACF3P,OAAA;YAAMgP,SAAS,EAAC,QAAQ;YAAAC,QAAA,eACtBjP,OAAA;cAAMgP,SAAS,EAAC,QAAQ;cAAAC,QAAA,gBACtBjP,OAAA;gBAAKgP,SAAS,EAAC,WAAW;gBAAC8B,OAAO,EAAC,WAAW;gBAAA7B,QAAA,eAACjP,OAAA;kBAAU+Q,MAAM,EAAC,kBAAkB;kBAACC,IAAI,EAAC,MAAM;kBAACC,MAAM,EAAC,SAAS;kBAACC,WAAW,EAAC,GAAG;kBAACC,aAAa,EAAC,OAAO;kBAACC,cAAc,EAAC;gBAAO;kBAAA5B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAC;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACpL3P,OAAA;gBAAKgP,SAAS,EAAC,OAAO;gBAAC8B,OAAO,EAAC,WAAW;gBAAA7B,QAAA,gBAACjP,OAAA;kBAAMqR,EAAE,EAAC,GAAG;kBAACC,EAAE,EAAC,GAAG;kBAACC,EAAE,EAAC,GAAG;kBAACC,EAAE,EAAC,GAAG;kBAACP,MAAM,EAAC,SAAS;kBAACC,WAAW,EAAC,GAAG;kBAACC,aAAa,EAAC;gBAAO;kBAAA3B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAC,CAAC,eAAA3P,OAAA;kBAAMqR,EAAE,EAAC,GAAG;kBAACC,EAAE,EAAC,GAAG;kBAACC,EAAE,EAAC,GAAG;kBAACC,EAAE,EAAC,GAAG;kBAACP,MAAM,EAAC,SAAS;kBAACC,WAAW,EAAC,GAAG;kBAACC,aAAa,EAAC;gBAAO;kBAAA3B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjO;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CACN,EAGA3O,UAAU,iBACThB,OAAA;QACEmD,KAAK,EAAE2L,MAAM,CAAC2C,OAAQ;QACtB1B,OAAO,EAAEnB,mBAAoB;QAC7B,cAAW,MAAM;QAAAK,QAAA,EAClB;MAED;QAAAO,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CACT,EAGAjN,iBAAiB,iBAChB1C,OAAA;QAAKmD,KAAK,EAAE2L,MAAM,CAAC4C,gBAAiB;QAAAzC,QAAA,gBAClCjP,OAAA;UAAKmD,KAAK,EAAE2L,MAAM,CAAC6C,eAAgB;UAAA1C,QAAA,EAAE3M;QAAS;UAAAkN,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACrD3P,OAAA;UAAKmD,KAAK,EAAE2L,MAAM,CAAC8C,aAAc;UAAA3C,QAAA,EAAC;QAAiB;UAAAO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACtD,CACN,EAGAvN,oBAAoB,IAAI,CAACI,gBAAgB,IAAI,CAACE,iBAAiB,iBAC9D1C,OAAA;QAAKmD,KAAK,EAAE2L,MAAM,CAAC+C,aAAc;QAAA5C,QAAA,gBAC/BjP,OAAA;UAAKmD,KAAK,EAAE2L,MAAM,CAACgD,UAAW;UAAA7C,QAAA,EAAC;QAA6C;UAAAO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eAClF3P,OAAA;UAAKmD,KAAK,EAAE2L,MAAM,CAACiD,aAAc;UAAA9C,QAAA,EAAC;QAAgD;UAAAO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACrF,CACN,EAEAvN,oBAAoB,IAAII,gBAAgB,IAAI,CAACE,iBAAiB,iBAC7D1C,OAAA;QAAKmD,KAAK,EAAE2L,MAAM,CAAC+C,aAAc;QAAA5C,QAAA,eAC/BjP,OAAA;UAAKmD,KAAK,EAAE;YAAC,GAAG2L,MAAM,CAACgD,UAAU;YAAEE,eAAe,EAAE;UAAyB,CAAE;UAAA/C,QAAA,EAAC;QAEhF;UAAAO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN,EAGAjO,aAAa,iBACZ1B,OAAA;QACEmD,KAAK,EAAE;UACL,GAAG2L,MAAM,CAACmD,SAAS;UACnBxH,OAAO,EAAErI,oBAAoB,IAAII,gBAAgB,GAAG,GAAG,GAAG,GAAG;UAC7D+H,MAAM,EAAEnI,oBAAoB,IAAII,gBAAgB,GAC5C,gDAAgD,GAChDJ,oBAAoB,IAAI,CAACI,gBAAgB,GACvC,iDAAiD,GACjD;QACR,CAAE;QACFwM,SAAS,EAAEpN,QAAQ,GAAG,mBAAmB,GAAG,EAAG;QAC/C,eAAY,MAAM;QAAAqN,QAAA,eAElBjP,OAAA;UAAK8Q,OAAO,EAAC,aAAa;UAACoB,KAAK,EAAC,4BAA4B;UAAAjD,QAAA,gBAE3DjP,OAAA;YACEmS,CAAC,EAAC,4EAA4E;YAC9ElB,MAAM,EACJ7O,oBAAoB,IAAII,gBAAgB,GACpC,SAAS,GACTJ,oBAAoB,IAAI,CAACI,gBAAgB,GACvC,SAAS,GACT,OACP;YACD0O,WAAW,EAAC,GAAG;YACfF,IAAI,EAAC,MAAM;YACXG,aAAa,EAAC;UAAO;YAAA3B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtB,CAAC,eACF3P,OAAA;YACEmS,CAAC,EAAC,4EAA4E;YAC9ElB,MAAM,EACJ7O,oBAAoB,IAAII,gBAAgB,GACpC,SAAS,GACTJ,oBAAoB,IAAI,CAACI,gBAAgB,GACvC,SAAS,GACT,OACP;YACD0O,WAAW,EAAC,GAAG;YACfF,IAAI,EAAC,MAAM;YACXG,aAAa,EAAC;UAAO;YAAA3B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtB,CAAC,eAEF3P,OAAA;YACEgJ,CAAC,EAAC,KAAK;YACPD,CAAC,EAAC,KAAK;YACP3D,KAAK,EAAC,KAAK;YACXG,MAAM,EAAC,KAAK;YACZyL,IAAI,EACF5O,oBAAoB,IAAII,gBAAgB,GACpC,SAAS,GACTJ,oBAAoB,IAAI,CAACI,gBAAgB,GACvC,SAAS,GACT,OACP;YACDiI,OAAO,EAAErI,oBAAoB,IAAII,gBAAgB,GAAG,MAAM,GAAG,MAAO;YACpE4P,EAAE,EAAC,IAAI;YACPnB,MAAM,EACJ7O,oBAAoB,IAAII,gBAAgB,GACpC,SAAS,GACTJ,oBAAoB,IAAI,CAACI,gBAAgB,GACvC,SAAS,GACT,OACP;YACD0O,WAAW,EAAC;UAAG;YAAA1B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChB,CAAC,eAEF3P,OAAA;YACEqS,EAAE,EAAC,KAAK;YACRC,EAAE,EAAC,KAAK;YACRpJ,CAAC,EAAC,IAAI;YACN8H,IAAI,EACF5O,oBAAoB,IAAII,gBAAgB,GACpC,SAAS,GACTJ,oBAAoB,IAAI,CAACI,gBAAgB,GACvC,SAAS,GACT,OACP;YACDiI,OAAO,EAAErI,oBAAoB,IAAII,gBAAgB,GAAG,KAAK,GAAG,KAAM;YAClEyO,MAAM,EACJ7O,oBAAoB,IAAII,gBAAgB,GACpC,SAAS,GACTJ,oBAAoB,IAAI,CAACI,gBAAgB,GACvC,SAAS,GACT,OACP;YACD0O,WAAW,EAAC;UAAG;YAAA1B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChB,CAAC,EAEDvN,oBAAoB,iBACnBpC,OAAA,CAAAE,SAAA;YAAA+O,QAAA,gBACEjP,OAAA;cAAMgJ,CAAC,EAAC,KAAK;cAACD,CAAC,EAAC,KAAK;cAACwJ,UAAU,EAAC,QAAQ;cAACvB,IAAI,EAAC,OAAO;cAACR,QAAQ,EAAC,IAAI;cAACD,UAAU,EAAC,MAAM;cAAAtB,QAAA,EAAC;YAEvF;cAAAO,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACP3P,OAAA;cAAMgJ,CAAC,EAAC,KAAK;cAACD,CAAC,EAAC,KAAK;cAACwJ,UAAU,EAAC,QAAQ;cAACvB,IAAI,EAAC,OAAO;cAACR,QAAQ,EAAC,IAAI;cAACD,UAAU,EAAC,MAAM;cAAAtB,QAAA,EAAC;YAEvF;cAAAO,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA,eACP,CACH;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN,EAGAzO,eAAe,iBACdlB,OAAA;QAAKmD,KAAK,EAAE;UACV,GAAG2L,MAAM,CAAC0D,eAAe;UACzBpN,KAAK,EAAE5D,SAAS,KAAK,SAAS,GAAG,GAAGsC,WAAW,GAAG,GAAG,GAAGC,cAAc,GAAG;UACzEwB,MAAM,EAAE/D,SAAS,KAAK,SAAS,GAAG,GAAGwC,YAAY,GAAG,GAAG,GAAGC,eAAe;QAC3E,CAAE;QAAAgL,QAAA,eACAjP,OAAA;UAAKmD,KAAK,EAAE;YACV6M,QAAQ,EAAE,UAAU;YACpB5K,KAAK,EAAE,MAAM;YACbG,MAAM,EAAE,MAAM;YACdgC,OAAO,EAAE,MAAM;YACf8I,UAAU,EAAE,QAAQ;YACpBoC,cAAc,EAAE;UAClB,CAAE;UAAAxD,QAAA,gBACAjP,OAAA;YACEsH,GAAG,EAAE,OAAOpG,eAAe,KAAK,QAAQ,GAAGA,eAAe,CAACmF,IAAI,GAAGnF,eAAgB;YAClF2O,GAAG,EAAC,kBAAkB;YACtB1M,KAAK,EAAE;cACLiC,KAAK,EAAE,MAAM;cACbG,MAAM,EAAE,MAAM;cACdmN,SAAS,EAAE,SAAS;cACpBC,SAAS,EAAEnR,SAAS,KAAK,WAAW,GAChC,uBAAuByC,eAAe,GAAG,EAAE,IAAI7C,WAAW,GAAG,aAAa,GAAG,EAAE,EAAE,GACjF,SAAU4C,YAAY,GAAG,EAAE,IAAKhC,aAAa,GAAGwB,mBAAmB,CAAC1B,UAAU,CAAC,CAAC,GAAG;cACvFyI,MAAM,EAAE;YACV,CAAE;YACFqI,MAAM,EAAGlI,CAAC,IAAKzC,gBAAgB,CAACyC,CAAC,CAACmI,MAAM,EAAErR,SAAS,KAAK,SAAS,GAAG,OAAO,GAAG,UAAU;UAAE;YAAAgO,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3F,CAAC,EACDnO,SAAS,KAAK,SAAS,IAAI,OAAON,eAAe,KAAK,QAAQ,iBAC7DlB,OAAA;YAAKmD,KAAK,EAAE;cACV6M,QAAQ,EAAE,UAAU;cACpB8C,MAAM,EAAE,OAAO;cACfC,IAAI,EAAE,KAAK;cACXJ,SAAS,EAAE,kBAAkB;cAC7BnC,QAAQ,EAAE,MAAM;cAChBD,UAAU,EAAE,KAAK;cACjBD,KAAK,EAAE,OAAO;cACd0B,eAAe,EAAE,yBAAyB;cAC1CgB,OAAO,EAAE,SAAS;cAClBC,YAAY,EAAE,MAAM;cACpBC,UAAU,EAAE,QAAQ;cACpBC,aAAa,EAAE,MAAM;cACrBC,SAAS,EAAE,2BAA2B;cACtCjD,MAAM,EAAE;YACV,CAAE;YAAAlB,QAAA,GACC/N,eAAe,CAACqF,QAAQ,EAAC,IAC1B,EAACvE,aAAa,KAAKwB,mBAAmB,CAAC1B,UAAU,CAAC,iBAChD9B,OAAA;cAAMmD,KAAK,EAAE;gBACXqN,QAAQ,EAAE,MAAM;gBAChB/F,OAAO,EAAE,GAAG;gBACZ4I,UAAU,EAAE;cACd,CAAE;cAAApE,QAAA,GAAC,UACO,EAAC,CAAC,CAACjN,aAAa,GAAGwB,mBAAmB,CAAC1B,UAAU,CAAC,GAAG,CAAC,IAAI,GAAG,EAAEwR,OAAO,CAAC,CAAC,CAAC,EAAC,IACpF;YAAA;cAAA9D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CACP;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CACN;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN,eAGD3P,OAAA;QACEmD,KAAK,EAAE2L,MAAM,CAACyE,UAAW;QACzBxD,OAAO,EAAEtC,aAAc;QACvB,cAAYzM,UAAU,GAAG,iBAAiB,GAAG,SAAU;QAAAiO,QAAA,EAEtD,CAACjO,UAAU,gBACVhB,OAAA;UAAKmD,KAAK,EAAE2L,MAAM,CAAC0E;QAAa;UAAAhE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,gBAEvC3P,OAAA;UAAKoF,KAAK,EAAC,IAAI;UAACG,MAAM,EAAC,IAAI;UAACuL,OAAO,EAAC,WAAW;UAACE,IAAI,EAAC,OAAO;UAAA/B,QAAA,eAC1DjP,OAAA;YAAMmS,CAAC,EAAC;UAA6D;YAAA3C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpE;MACN;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACK,CAAC,EAGR3O,UAAU,iBACThB,OAAA;QACEmD,KAAK,EAAE2L,MAAM,CAAC2E,QAAS;QACvB1D,OAAO,EAAEA,CAAA,KAAMlN,MAAM,CAAC6Q,QAAQ,CAACC,MAAM,CAAC,CAAE;QACxC,cAAW,OAAO;QAAA1E,QAAA,eAElBjP,OAAA;UAAKoF,KAAK,EAAC,IAAI;UAACG,MAAM,EAAC,IAAI;UAACuL,OAAO,EAAC,WAAW;UAACE,IAAI,EAAC,OAAO;UAAA/B,QAAA,eAC1DjP,OAAA;YAAMmS,CAAC,EAAC;UAAyN;YAAA3C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChO;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACA,CACT;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,EAGLzN,kBAAkB,iBACjBlC,OAAA;MACEmD,KAAK,EAAE2L,MAAM,CAAC8E,cAAe;MAC7B5E,SAAS,EAAEpN,QAAQ,GAAG,oBAAoB,GAAG,EAAG;MAChD,cAAW,MAAM;MACjBiS,IAAI,EAAC,QAAQ;MAAA5E,QAAA,eAEbjP,OAAA;QAAKmD,KAAK,EAAE2L,MAAM,CAACgF,gBAAiB;QAAA7E,QAAA,gBAClCjP,OAAA;UAAImD,KAAK,EAAE2L,MAAM,CAACiF,cAAe;UAAA9E,QAAA,EAAC;QAAoB;UAAAO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC3D3P,OAAA;UAAGmD,KAAK,EAAE2L,MAAM,CAACkF,iBAAkB;UAAA/E,QAAA,EAAC;QAA4D;UAAAO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eAGpG3P,OAAA;UAAKmD,KAAK,EAAE2L,MAAM,CAACmF,eAAgB;UAAAhF,QAAA,gBACjCjP,OAAA;YACEmD,KAAK,EAAE;cACL,GAAG2L,MAAM,CAACoF,YAAY;cACtB,IAAIpS,UAAU,KAAK,KAAK,GAAGgN,MAAM,CAACqF,kBAAkB,GAAG,CAAC,CAAC;YAC3D,CAAE;YACFpE,OAAO,EAAEA,CAAA,KAAMnC,kBAAkB,CAAC,KAAK,CAAE;YAAAqB,QAAA,EAC1C;UAED;YAAAO,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACT3P,OAAA;YACEmD,KAAK,EAAE;cACL,GAAG2L,MAAM,CAACoF,YAAY;cACtB,IAAIpS,UAAU,KAAK,OAAO,GAAGgN,MAAM,CAACqF,kBAAkB,GAAG,CAAC,CAAC;YAC7D,CAAE;YACFpE,OAAO,EAAEA,CAAA,KAAMnC,kBAAkB,CAAC,OAAO,CAAE;YAAAqB,QAAA,EAC5C;UAED;YAAAO,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,eAGN3P,OAAA;UAAKmD,KAAK,EAAE2L,MAAM,CAACsF,eAAgB;UAAAnF,QAAA,gBACjCjP,OAAA;YAAOmD,KAAK,EAAE2L,MAAM,CAACuF,WAAY;YAAApF,QAAA,GAAC,cACpB,EAACjN,aAAa,EAAC,IAC3B,EAACA,aAAa,KAAKwB,mBAAmB,CAAC1B,UAAU,CAAC,iBAChD9B,OAAA;cAAMmD,KAAK,EAAE2L,MAAM,CAACwF,UAAW;cAAArF,QAAA,GAAC,GAC7B,EAACjN,aAAa,GAAGwB,mBAAmB,CAAC1B,UAAU,CAAC,GAAG,GAAG,GAAG,EAAE,EAC3D,CAAC,CAACE,aAAa,GAAGwB,mBAAmB,CAAC1B,UAAU,CAAC,GAAG,CAAC,IAAI,GAAG,EAAEwR,OAAO,CAAC,CAAC,CAAC,EAAC,IAC5E;YAAA;cAAA9D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CACP;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI,CAAC,eACR3P,OAAA;YACEkG,IAAI,EAAC,OAAO;YACZT,GAAG,EAAC,IAAI;YACRH,GAAG,EAAC,IAAI;YACRiP,KAAK,EAAEvS,aAAc;YACrB4O,QAAQ,EAAGlG,CAAC,IAAKoD,qBAAqB,CAAC0G,QAAQ,CAAC9J,CAAC,CAACmI,MAAM,CAAC0B,KAAK,CAAC,CAAE;YACjEpR,KAAK,EAAE2L,MAAM,CAAC2F;UAAO;YAAAjF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtB,CAAC,eACF3P,OAAA;YAAKmD,KAAK,EAAE2L,MAAM,CAAC4F,YAAa;YAAAzF,QAAA,gBAC9BjP,OAAA;cAAAiP,QAAA,EAAM;YAAI;cAAAO,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACjB3P,OAAA;cAAAiP,QAAA,EAAM;YAAI;cAAAO,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACd,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGN3P,OAAA;UACEmD,KAAK,EAAE2L,MAAM,CAAC6F,cAAe;UAC7B5E,OAAO,EAAE/B,wBAAyB;UAAAiB,QAAA,EACnC;QAED;UAAAO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN,EAGArO,oBAAoB,iBACnBtB,OAAA;MACEmD,KAAK,EAAE2L,MAAM,CAAC8F,gBAAiB;MAC/B5F,SAAS,EAAEpN,QAAQ,GAAG,sBAAsB,GAAG,EAAG;MAClD,cAAW,MAAM;MACjBiS,IAAI,EAAC,QAAQ;MAAA5E,QAAA,gBAEbjP,OAAA;QACEmD,KAAK,EAAE2L,MAAM,CAAC+F,QAAS;QACvB9E,OAAO,EAAEA,CAAA,KAAMxO,uBAAuB,CAAC,KAAK,CAAE;QAC9C,cAAW,yBAAyB;QAAA0N,QAAA,eAEpCjP,OAAA;UAAKoF,KAAK,EAAExD,QAAQ,GAAG,IAAI,GAAG,IAAK;UAAC2D,MAAM,EAAE3D,QAAQ,GAAG,IAAI,GAAG,IAAK;UAACkP,OAAO,EAAC,WAAW;UAACE,IAAI,EAAC,cAAc;UAAA/B,QAAA,eACzGjP,OAAA;YAAMmS,CAAC,EAAC;UAAuG;YAAA3C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9G;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACA,CAAC,eACT3P,OAAA;QAAKmD,KAAK,EAAE2L,MAAM,CAACgG,WAAY;QAAA7F,QAAA,gBAC7BjP,OAAA;UACEmD,KAAK,EAAE;YACL,GAAG2L,MAAM,CAACiG,GAAG;YACb,IAAIvT,SAAS,KAAK,SAAS,GAAGsN,MAAM,CAACtN,SAAS,GAAG,CAAC,CAAC;UACrD,CAAE;UACFuO,OAAO,EAAEA,CAAA,KAAM9B,eAAe,CAAC,SAAS,CAAE;UAAAgB,QAAA,EAC3C;QAED;UAAAO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACT3P,OAAA;UACEmD,KAAK,EAAE;YACL,GAAG2L,MAAM,CAACiG,GAAG;YACb,IAAIvT,SAAS,KAAK,WAAW,GAAGsN,MAAM,CAACtN,SAAS,GAAG,CAAC,CAAC;UACvD,CAAE;UACFuO,OAAO,EAAEA,CAAA,KAAM9B,eAAe,CAAC,WAAW,CAAE;UAAAgB,QAAA,EAC7C;QAED;UAAAO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,eACN3P,OAAA;QAAKmD,KAAK,EAAE2L,MAAM,CAACkG,aAAc;QAAChG,SAAS,EAAC,gBAAgB;QAAAC,QAAA,EACzDJ,kBAAkB,CAAC,CAAC,CAACoG,GAAG,CAAC,CAAC7G,OAAO,EAAE8G,KAAK,KAAK;UAC5C;UACA,IAAI,CAAC9G,OAAO,EAAE,OAAO,IAAI;UAEzB,MAAM+G,UAAU,GAAG,CAAC,OAAOjU,eAAe,KAAK,QAAQ,GAAGA,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAEmF,IAAI,GAAGnF,eAAe,MAAMkN,OAAO,CAAC/H,IAAI;UAEnH,oBACErG,OAAA;YAEEmD,KAAK,EAAE;cACL,GAAG2L,MAAM,CAACsG,WAAW;cACrBC,WAAW,EAAEF,UAAU,GAAG,SAAS,GAAG,SAAS;cAC/CnD,eAAe,EAAEmD,UAAU,GAAG,SAAS,GAAG;YAC5C,CAAE;YACFG,KAAK,EAAE,GAAGlH,OAAO,CAAChI,IAAI,MAAMgI,OAAO,CAACxI,YAAY,IAAI,KAAK,IAAK;YAC9DmK,OAAO,EAAEA,CAAA,KAAM5B,mBAAmB,CAACC,OAAO,CAAE;YAC5C,cAAY,UAAUA,OAAO,CAAChI,IAAI,IAAIgI,OAAO,CAACxI,YAAY,IAAI,KAAK,IAAK;YAAAqJ,QAAA,gBAExEjP,OAAA;cACEsH,GAAG,EAAE8G,OAAO,CAAC/H,IAAK;cAClBwJ,GAAG,EAAEzB,OAAO,CAAChI,IAAK;cAClBjD,KAAK,EAAE2L,MAAM,CAACyG,YAAa;cAC3BC,OAAO,EAAG9K,CAAC,IAAK;gBACdA,CAAC,CAACmI,MAAM,CAAC5H,aAAa,CAAC9H,KAAK,CAACoE,OAAO,GAAG,MAAM;cAC/C;YAAE;cAAAiI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACF3P,OAAA;cAAKmD,KAAK,EAAE2L,MAAM,CAAC2G,YAAa;cAAAxG,QAAA,gBAC9BjP,OAAA;gBAAKmD,KAAK,EAAE2L,MAAM,CAAC4G,WAAY;gBAAAzG,QAAA,EAAEb,OAAO,CAAChI;cAAI;gBAAAoJ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,EACnDnO,SAAS,KAAK,SAAS,IAAI4M,OAAO,CAACxI,YAAY,iBAC9C5F,OAAA;gBAAKmD,KAAK,EAAE2L,MAAM,CAAC6G,WAAY;gBAAA1G,QAAA,GAAEb,OAAO,CAACxI,YAAY,EAAC,IAAE;cAAA;gBAAA4J,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAC9D;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC;UAAA,GAvBDuF,KAAK;YAAA1F,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAwBJ,CAAC;QAEb,CAAC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;;AAED;AAAA/O,EAAA,CA7nCMF,KAAK;AAAAkV,EAAA,GAALlV,KAAK;AA8nCX,MAAMoO,MAAM,GAAG;EACbC,SAAS,EAAE;IACTiB,QAAQ,EAAE,UAAU;IACpBzK,MAAM,EAAE,4BAA4B;IACpCgC,OAAO,EAAE,MAAM;IACf6I,aAAa,EAAE,QAAQ;IACvB4B,eAAe,EAAE,SAAS;IAC1B1B,KAAK,EAAE,MAAM;IACbuF,UAAU,EAAE,4EAA4E;IACxFC,QAAQ,EAAE,QAAQ;IAClBC,WAAW,EAAE,cAAc;IAC3BC,uBAAuB,EAAE;EAC3B,CAAC;EACD9G,eAAe,EAAE;IACf+G,IAAI,EAAE,CAAC;IACPjG,QAAQ,EAAE,UAAU;IACpB8F,QAAQ,EAAE,QAAQ;IAClB9D,eAAe,EAAE,MAAM;IACvBzK,OAAO,EAAE,MAAM;IACf8I,UAAU,EAAE,QAAQ;IACpBoC,cAAc,EAAE;EAClB,CAAC;EACDrD,UAAU,EAAE;IACVhK,KAAK,EAAE,MAAM;IACbG,MAAM,EAAE,MAAM;IACdmN,SAAS,EAAE,OAAO;IAClBC,SAAS,EAAE,WAAW,CAAC;EACzB,CAAC;EACD/C,aAAa,EAAE;IACbI,QAAQ,EAAE,UAAU;IACpBC,GAAG,EAAE,CAAC;IACN8C,IAAI,EAAE,CAAC;IACP3N,KAAK,EAAE,MAAM;IACbG,MAAM,EAAE,MAAM;IACdmN,SAAS,EAAE,OAAO;IAClBnL,OAAO,EAAE,MAAM;IACf2O,eAAe,EAAE,WAAW,CAAC;EAC/B,CAAC;EAEDpG,OAAO,EAAE;IACPE,QAAQ,EAAE,UAAU;IACpBC,GAAG,EAAE,MAAM;IACX8C,IAAI,EAAE,MAAM;IACZf,eAAe,EAAE,oBAAoB;IACrC1B,KAAK,EAAE,OAAO;IACd0C,OAAO,EAAE,MAAM;IACfC,YAAY,EAAE,KAAK;IACnBzC,QAAQ,EAAE,MAAM;IAChBD,UAAU,EAAE,KAAK;IACjB4F,MAAM,EAAE,SAAS;IACjBhG,MAAM,EAAE,EAAE;IACViG,MAAM,EAAE,MAAM;IACdhD,SAAS,EAAE,+BAA+B;IAC1CiD,UAAU,EAAE,eAAe;IAC3B9O,OAAO,EAAE,MAAM;IACf8I,UAAU,EAAE,QAAQ;IACpBoC,cAAc,EAAE,QAAQ;IACxBrN,KAAK,EAAE,MAAM;IACbG,MAAM,EAAE,MAAM;IACd+Q,OAAO,EAAE;EACX,CAAC;EACD7E,OAAO,EAAE;IACPzB,QAAQ,EAAE,UAAU;IACpBC,GAAG,EAAE,MAAM;IACX8C,IAAI,EAAE,MAAM;IACZf,eAAe,EAAE,oBAAoB;IACrC1B,KAAK,EAAE,OAAO;IACd0C,OAAO,EAAE,MAAM;IACfC,YAAY,EAAE,KAAK;IACnBzC,QAAQ,EAAE,MAAM;IAChBD,UAAU,EAAE,KAAK;IACjB4F,MAAM,EAAE,SAAS;IACjBhG,MAAM,EAAE,EAAE;IACViG,MAAM,EAAE,MAAM;IACdhD,SAAS,EAAE,+BAA+B;IAC1CiD,UAAU,EAAE,eAAe;IAC3B9O,OAAO,EAAE,MAAM;IACf8I,UAAU,EAAE,QAAQ;IACpBoC,cAAc,EAAE,QAAQ;IACxBrN,KAAK,EAAE,MAAM;IACbG,MAAM,EAAE,MAAM;IACd+Q,OAAO,EAAE;EACX,CAAC;EACDC,eAAe,EAAE;IACfvG,QAAQ,EAAE,UAAU;IACpBC,GAAG,EAAE,MAAM;IACXC,KAAK,EAAE,MAAM;IACbC,MAAM,EAAE,EAAE;IACV5I,OAAO,EAAE,MAAM;IACf6I,aAAa,EAAE,QAAQ;IACvBC,UAAU,EAAE,QAAQ;IACpBmG,GAAG,EAAE,KAAK;IACVxD,OAAO,EAAE,MAAM;IACfhB,eAAe,EAAE,oBAAoB;IACrCiB,YAAY,EAAE,MAAM;IACpBwD,cAAc,EAAE,YAAY;IAC5BC,oBAAoB,EAAE,YAAY;IAClCtD,SAAS,EAAE,+BAA+B;IAC1CgD,MAAM,EAAE;EACV,CAAC;EACDO,WAAW,EAAE;IACX3G,QAAQ,EAAE,UAAU;IACpBC,GAAG,EAAE,MAAM;IACX8C,IAAI,EAAE,MAAM;IACZ3N,KAAK,EAAE,MAAM;IACbG,MAAM,EAAE,MAAM;IACdyM,eAAe,EAAE,oBAAoB;IACrCiB,YAAY,EAAE,MAAM;IACpBmD,MAAM,EAAE,oCAAoC;IAC5CjG,MAAM,EAAE,CAAC;IACTkG,UAAU,EAAE;EACd,CAAC;EACDO,YAAY,EAAE;IACZ5G,QAAQ,EAAE,UAAU;IACpB5K,KAAK,EAAE,MAAM;IACbG,MAAM,EAAE,MAAM;IACd0N,YAAY,EAAE,KAAK;IACnBmD,MAAM,EAAE,MAAM;IACdD,MAAM,EAAE,SAAS;IACjBE,UAAU,EAAE,eAAe;IAC3B9O,OAAO,EAAE,MAAM;IACf8I,UAAU,EAAE,QAAQ;IACpBoC,cAAc,EAAE,QAAQ;IACxBtC,MAAM,EAAE,EAAE;IACV0G,MAAM,EAAE,KAAK;IACbzD,SAAS,EAAE,8BAA8B;IACzCkD,OAAO,EAAE,MAAM;IACf,SAAS,EAAE;MACT3D,SAAS,EAAE;IACb;EACF,CAAC;EACDmE,WAAW,EAAE;IACXtG,QAAQ,EAAE,MAAM;IAChBD,UAAU,EAAE,KAAK;IACjBD,KAAK,EAAE,OAAO;IACdyG,UAAU,EAAE,8BAA8B;IAC1CC,SAAS,EAAE,KAAK;IAChBhE,OAAO,EAAE,UAAU;IACnBC,YAAY,EAAE,MAAM;IACpBjB,eAAe,EAAE,oBAAoB;IACrCtB,aAAa,EAAE;EACjB,CAAC;EACDgB,gBAAgB,EAAE;IAChB1B,QAAQ,EAAE,UAAU;IACpBC,GAAG,EAAE,KAAK;IACV8C,IAAI,EAAE,KAAK;IACXJ,SAAS,EAAE,uBAAuB;IAClCxC,MAAM,EAAE,EAAE;IACV8G,SAAS,EAAE,QAAQ;IACnB9D,aAAa,EAAE,MAAM;IACrBH,OAAO,EAAE,WAAW;IACpBhB,eAAe,EAAE,oBAAoB;IACrCiB,YAAY,EAAE,MAAM;IACpBwD,cAAc,EAAE,YAAY;IAC5BC,oBAAoB,EAAE,YAAY;IAClCtD,SAAS,EAAE;EACb,CAAC;EACDzB,eAAe,EAAE;IACfnB,QAAQ,EAAE,MAAM;IAChBD,UAAU,EAAE,KAAK;IACjBD,KAAK,EAAE,SAAS;IAChByG,UAAU,EAAE,8BAA8B;IAC1CtG,YAAY,EAAE,KAAK;IACnByG,SAAS,EAAE;EACb,CAAC;EACDtF,aAAa,EAAE;IACbpB,QAAQ,EAAE,MAAM;IAChBD,UAAU,EAAE,KAAK;IACjBD,KAAK,EAAE,OAAO;IACdyG,UAAU,EAAE;EACd,CAAC;EACDlF,aAAa,EAAE;IACb7B,QAAQ,EAAE,UAAU;IACpBC,GAAG,EAAE,KAAK;IACV8C,IAAI,EAAE,KAAK;IACXJ,SAAS,EAAE,uBAAuB;IAClCxC,MAAM,EAAE,EAAE;IACV8G,SAAS,EAAE,QAAQ;IACnB9D,aAAa,EAAE,MAAM;IACrBH,OAAO,EAAE,WAAW;IACpBC,YAAY,EAAE,MAAM;IACpBjB,eAAe,EAAE,oBAAoB;IACrCyE,cAAc,EAAE,YAAY;IAC5BC,oBAAoB,EAAE,YAAY;IAClCtD,SAAS,EAAE;EACb,CAAC;EACDtB,UAAU,EAAE;IACVtB,QAAQ,EAAE,MAAM;IAChBD,UAAU,EAAE,KAAK;IACjBD,KAAK,EAAE,OAAO;IACdyG,UAAU,EAAE,8BAA8B;IAC1C/E,eAAe,EAAE,0BAA0B;IAC3CgB,OAAO,EAAE,WAAW;IACpBC,YAAY,EAAE,MAAM;IACpBxC,YAAY,EAAE,KAAK;IACnB4F,UAAU,EAAE;EACd,CAAC;EACDtE,aAAa,EAAE;IACbvB,QAAQ,EAAE,MAAM;IAChBD,UAAU,EAAE,KAAK;IACjBD,KAAK,EAAE,OAAO;IACdyG,UAAU,EAAE,8BAA8B;IAC1C/E,eAAe,EAAE,oBAAoB;IACrCgB,OAAO,EAAE,UAAU;IACnBC,YAAY,EAAE;EAChB,CAAC;EACDhB,SAAS,EAAE;IACTjC,QAAQ,EAAE,UAAU;IACpBC,GAAG,EAAE,KAAK;IACV8C,IAAI,EAAE,KAAK;IACXJ,SAAS,EAAE,uBAAuB;IAClCvN,KAAK,EAAE,KAAK;IACZ+R,QAAQ,EAAE,OAAO;IACjB5R,MAAM,EAAE,MAAM;IACdkF,OAAO,EAAE,GAAG;IACZ0I,aAAa,EAAE,MAAM;IACrBhD,MAAM,EAAE,CAAC;IACT5F,MAAM,EAAE,iDAAiD;IACzD6M,YAAY,EAAE,iDAAiD;IAC/Df,UAAU,EAAE;EACd,CAAC;EACD7D,eAAe,EAAE;IACfxC,QAAQ,EAAE,UAAU;IACpBC,GAAG,EAAE,KAAK;IACV8C,IAAI,EAAE,KAAK;IACXJ,SAAS,EAAE,uBAAuB;IAClCxC,MAAM,EAAE,CAAC;IACT5I,OAAO,EAAE,MAAM;IACf8I,UAAU,EAAE,QAAQ;IACpBoC,cAAc,EAAE,QAAQ;IACxBU,aAAa,EAAE,MAAM;IACrBkE,QAAQ,EAAE,OAAO;IAAE;IACnBC,SAAS,EAAE,OAAO,CAAC;EACrB,CAAC;EACD/D,UAAU,EAAE;IACVvD,QAAQ,EAAE,UAAU;IACpB8C,MAAM,EAAE,MAAM;IACdC,IAAI,EAAE,KAAK;IACXJ,SAAS,EAAE,kBAAkB;IAC7BvN,KAAK,EAAE,MAAM;IACbG,MAAM,EAAE,MAAM;IACdyM,eAAe,EAAE,0BAA0B;IAC3CiB,YAAY,EAAE,KAAK;IACnB1L,OAAO,EAAE,MAAM;IACf8I,UAAU,EAAE,QAAQ;IACpBoC,cAAc,EAAE,QAAQ;IACxB0D,MAAM,EAAE,SAAS;IACjBhG,MAAM,EAAE,EAAE;IACVkG,UAAU,EAAE,eAAe;IAC3BD,MAAM,EAAE,oCAAoC;IAC5ChD,SAAS,EAAE,+BAA+B;IAC1CkD,OAAO,EAAE,MAAM;IACftD,OAAO,EAAE;EACX,CAAC;EACDQ,YAAY,EAAE;IACZpO,KAAK,EAAE,MAAM;IACbG,MAAM,EAAE,MAAM;IACdyM,eAAe,EAAE,SAAS;IAC1BiB,YAAY,EAAE,KAAK;IACnBoD,UAAU,EAAE;EACd,CAAC;EACD5C,QAAQ,EAAE;IACRzD,QAAQ,EAAE,UAAU;IACpB8C,MAAM,EAAE,MAAM;IACd5C,KAAK,EAAE,MAAM;IACb9K,KAAK,EAAE,MAAM;IACbG,MAAM,EAAE,MAAM;IACdyM,eAAe,EAAE,oBAAoB;IACrCiB,YAAY,EAAE,KAAK;IACnB1L,OAAO,EAAE,MAAM;IACf8I,UAAU,EAAE,QAAQ;IACpBoC,cAAc,EAAE,QAAQ;IACxB0D,MAAM,EAAE,SAAS;IACjBhG,MAAM,EAAE,EAAE;IACVkG,UAAU,EAAE,eAAe;IAC3BD,MAAM,EAAE,MAAM;IACdhD,SAAS,EAAE,+BAA+B;IAC1CkD,OAAO,EAAE,MAAM;IACftD,OAAO,EAAE;EACX,CAAC;EAED;EACAY,cAAc,EAAE;IACd5D,QAAQ,EAAE,UAAU;IACpB8C,MAAM,EAAE,CAAC;IACTC,IAAI,EAAE,CAAC;IACP7C,KAAK,EAAE,CAAC;IACR8B,eAAe,EAAE,2BAA2B;IAC5CyE,cAAc,EAAE,YAAY;IAC5BC,oBAAoB,EAAE,YAAY;IAClCa,mBAAmB,EAAE,MAAM;IAC3BC,oBAAoB,EAAE,MAAM;IAC5BxE,OAAO,EAAE,MAAM;IACfyE,SAAS,EAAE,MAAM;IACjBlQ,OAAO,EAAE,MAAM;IACf6I,aAAa,EAAE,QAAQ;IACvBD,MAAM,EAAE,EAAE;IACViD,SAAS,EAAE,iCAAiC;IAC5CgD,MAAM,EAAE;EACV,CAAC;EACDtC,gBAAgB,EAAE;IAChBvM,OAAO,EAAE,MAAM;IACf6I,aAAa,EAAE,QAAQ;IACvBC,UAAU,EAAE,QAAQ;IACpBmG,GAAG,EAAE;EACP,CAAC;EACDzC,cAAc,EAAE;IACdvD,QAAQ,EAAE,MAAM;IAChBD,UAAU,EAAE,KAAK;IACjBD,KAAK,EAAE,SAAS;IAChBuG,MAAM,EAAE,CAAC;IACTI,SAAS,EAAE;EACb,CAAC;EACDjD,iBAAiB,EAAE;IACjBxD,QAAQ,EAAE,MAAM;IAChBF,KAAK,EAAE,MAAM;IACbuG,MAAM,EAAE,CAAC;IACTI,SAAS,EAAE,QAAQ;IACnBS,UAAU,EAAE;EACd,CAAC;EACDzD,eAAe,EAAE;IACf1M,OAAO,EAAE,MAAM;IACfiP,GAAG,EAAE,MAAM;IACXpR,KAAK,EAAE,MAAM;IACb+R,QAAQ,EAAE;EACZ,CAAC;EACDjD,YAAY,EAAE;IACZ+B,IAAI,EAAE,CAAC;IACPjD,OAAO,EAAE,WAAW;IACpBC,YAAY,EAAE,MAAM;IACpBzC,QAAQ,EAAE,MAAM;IAChBD,UAAU,EAAE,KAAK;IACjB4F,MAAM,EAAE,SAAS;IACjBE,UAAU,EAAE,eAAe;IAC3BD,MAAM,EAAE,mBAAmB;IAC3BpE,eAAe,EAAE,SAAS;IAC1B1B,KAAK,EAAE,MAAM;IACbgG,OAAO,EAAE;EACX,CAAC;EACDnC,kBAAkB,EAAE;IAClBnC,eAAe,EAAE,SAAS;IAC1B1B,KAAK,EAAE,SAAS;IAChB+E,WAAW,EAAE,SAAS;IACtBjC,SAAS,EAAE;EACb,CAAC;EACDgB,eAAe,EAAE;IACfhP,KAAK,EAAE,MAAM;IACb+R,QAAQ,EAAE,OAAO;IACjB5P,OAAO,EAAE,MAAM;IACf6I,aAAa,EAAE,QAAQ;IACvBoG,GAAG,EAAE;EACP,CAAC;EACDnC,WAAW,EAAE;IACX7D,QAAQ,EAAE,MAAM;IAChBD,UAAU,EAAE,KAAK;IACjBD,KAAK,EAAE,MAAM;IACb2G,SAAS,EAAE,QAAQ;IACnB1P,OAAO,EAAE,MAAM;IACf8I,UAAU,EAAE,QAAQ;IACpBoC,cAAc,EAAE,QAAQ;IACxB+D,GAAG,EAAE;EACP,CAAC;EACDlC,UAAU,EAAE;IACV9D,QAAQ,EAAE,MAAM;IAChBD,UAAU,EAAE,KAAK;IACjBD,KAAK,EAAE,SAAS;IAChB0B,eAAe,EAAE,yBAAyB;IAC1CgB,OAAO,EAAE,SAAS;IAClBC,YAAY,EAAE;EAChB,CAAC;EACDwB,MAAM,EAAE;IACNrP,KAAK,EAAE,MAAM;IACbG,MAAM,EAAE,KAAK;IACb0N,YAAY,EAAE,KAAK;IACnB0E,UAAU,EAAE,SAAS;IACrBrB,OAAO,EAAE,MAAM;IACfH,MAAM,EAAE,SAAS;IACjByB,gBAAgB,EAAE,MAAM;IACxBC,UAAU,EAAE;EACd,CAAC;EACDnD,YAAY,EAAE;IACZnN,OAAO,EAAE,MAAM;IACfkL,cAAc,EAAE,eAAe;IAC/BjC,QAAQ,EAAE,MAAM;IAChBF,KAAK,EAAE,MAAM;IACb0G,SAAS,EAAE;EACb,CAAC;EACDrC,cAAc,EAAE;IACdvP,KAAK,EAAE,MAAM;IACb+R,QAAQ,EAAE,OAAO;IACjBnE,OAAO,EAAE,WAAW;IACpBhB,eAAe,EAAE,SAAS;IAC1B1B,KAAK,EAAE,SAAS;IAChB2C,YAAY,EAAE,MAAM;IACpBzC,QAAQ,EAAE,MAAM;IAChBD,UAAU,EAAE,KAAK;IACjB4F,MAAM,EAAE,SAAS;IACjBE,UAAU,EAAE,eAAe;IAC3BD,MAAM,EAAE,MAAM;IACdE,OAAO,EAAE,MAAM;IACflD,SAAS,EAAE;EACb,CAAC;EAEDwB,gBAAgB,EAAE;IAChB5E,QAAQ,EAAE,UAAU;IACpB8C,MAAM,EAAE,CAAC;IACTC,IAAI,EAAE,CAAC;IACP7C,KAAK,EAAE,CAAC;IACR8B,eAAe,EAAE,2BAA2B;IAC5CyE,cAAc,EAAE,YAAY;IAC5BC,oBAAoB,EAAE,YAAY;IAClCa,mBAAmB,EAAE,MAAM;IAC3BC,oBAAoB,EAAE,MAAM;IAC5BxE,OAAO,EAAE,MAAM;IACfyE,SAAS,EAAE,MAAM;IACjBlQ,OAAO,EAAE,MAAM;IACf6I,aAAa,EAAE,QAAQ;IACvBD,MAAM,EAAE,EAAE;IACViD,SAAS,EAAE,iCAAiC;IAC5CgD,MAAM,EAAE;EACV,CAAC;EACDvB,QAAQ,EAAE;IACR7E,QAAQ,EAAE,UAAU;IACpBC,GAAG,EAAE,MAAM;IACXC,KAAK,EAAE,MAAM;IACbI,KAAK,EAAE,MAAM;IACb6F,MAAM,EAAE,SAAS;IACjBhG,MAAM,EAAE,EAAE;IACV/K,KAAK,EAAE,MAAM;IACbG,MAAM,EAAE,MAAM;IACdgC,OAAO,EAAE,MAAM;IACf8I,UAAU,EAAE,QAAQ;IACpBoC,cAAc,EAAE,QAAQ;IACxBQ,YAAY,EAAE,KAAK;IACnBjB,eAAe,EAAE,oBAAoB;IACrCqE,UAAU,EAAE,eAAe;IAC3BD,MAAM,EAAE,MAAM;IACdE,OAAO,EAAE,MAAM;IACftD,OAAO,EAAE;EACX,CAAC;EACD8B,WAAW,EAAE;IACXvN,OAAO,EAAE,MAAM;IACfkJ,YAAY,EAAE,MAAM;IACpBuG,SAAS,EAAE,KAAK;IAChBhF,eAAe,EAAE,SAAS;IAC1BiB,YAAY,EAAE,MAAM;IACpBD,OAAO,EAAE;EACX,CAAC;EACD+B,GAAG,EAAE;IACHkB,IAAI,EAAE,CAAC;IACPgB,SAAS,EAAE,QAAQ;IACnBjE,OAAO,EAAE,WAAW;IACpBC,YAAY,EAAE,KAAK;IACnBzC,QAAQ,EAAE,MAAM;IAChBD,UAAU,EAAE,KAAK;IACjB4F,MAAM,EAAE,SAAS;IACjBE,UAAU,EAAE,eAAe;IAC3B/F,KAAK,EAAE,MAAM;IACbgG,OAAO,EAAE,MAAM;IACfF,MAAM,EAAE,MAAM;IACdpE,eAAe,EAAE;EACnB,CAAC;EACDxQ,SAAS,EAAE;IACTwQ,eAAe,EAAE,SAAS;IAC1B1B,KAAK,EAAE,SAAS;IAChB8C,SAAS,EAAE;EACb,CAAC;EACD4B,aAAa,EAAE;IACbzN,OAAO,EAAE,MAAM;IACfuQ,mBAAmB,EAAE,sCAAsC;IAC3DtB,GAAG,EAAE,MAAM;IACXiB,SAAS,EAAE,mBAAmB;IAC9BM,SAAS,EAAE,MAAM;IACjBC,aAAa,EAAE,KAAK;IACpBC,cAAc,EAAE,MAAM;IACtBC,cAAc,EAAE;EAClB,CAAC;EACD9C,WAAW,EAAE;IACXpF,QAAQ,EAAE,UAAU;IACpB5K,KAAK,EAAE,MAAM;IACb+S,WAAW,EAAE,KAAK;IAClBnG,eAAe,EAAE,SAAS;IAC1BiB,YAAY,EAAE,MAAM;IACpB1L,OAAO,EAAE,MAAM;IACf8I,UAAU,EAAE,QAAQ;IACpBoC,cAAc,EAAE,QAAQ;IACxB0D,MAAM,EAAE,SAAS;IACjBE,UAAU,EAAE,eAAe;IAC3BD,MAAM,EAAE,mBAAmB;IAC3BN,QAAQ,EAAE,QAAQ;IAClB1C,SAAS,EAAE,8BAA8B;IACzCJ,OAAO,EAAE,KAAK;IACdsD,OAAO,EAAE;EACX,CAAC;EACDf,YAAY,EAAE;IACZnQ,KAAK,EAAE,MAAM;IACbG,MAAM,EAAE,MAAM;IACdmN,SAAS,EAAE,SAAS;IACpBO,YAAY,EAAE,KAAK;IACnBjB,eAAe,EAAE;EACnB,CAAC;EACDyD,YAAY,EAAE;IACZzF,QAAQ,EAAE,UAAU;IACpB8C,MAAM,EAAE,KAAK;IACbC,IAAI,EAAE,KAAK;IACX7C,KAAK,EAAE,KAAK;IACZM,QAAQ,EAAE,KAAK;IACfF,KAAK,EAAE,MAAM;IACb2G,SAAS,EAAE,QAAQ;IACnBjF,eAAe,EAAE,2BAA2B;IAC5CiB,YAAY,EAAE,KAAK;IACnBD,OAAO,EAAE,SAAS;IAClB8C,QAAQ,EAAE;EACZ,CAAC;EACDJ,WAAW,EAAE;IACXlF,QAAQ,EAAE,KAAK;IACfD,UAAU,EAAE,KAAK;IACjB2C,UAAU,EAAE,QAAQ;IACpBkF,YAAY,EAAE,UAAU;IACxBtC,QAAQ,EAAE,QAAQ;IAClBrF,YAAY,EAAE;EAChB,CAAC;EACDkF,WAAW,EAAE;IACXnF,QAAQ,EAAE,KAAK;IACfD,UAAU,EAAE,KAAK;IACjBD,KAAK,EAAE,SAAS;IAChB4C,UAAU,EAAE;EACd;AACF,CAAC;AAED,eAAexS,KAAK;AAAC,IAAAkV,EAAA;AAAAyC,YAAA,CAAAzC,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}