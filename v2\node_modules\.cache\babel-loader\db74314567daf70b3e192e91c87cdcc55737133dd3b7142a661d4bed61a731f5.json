{"ast": null, "code": "var _jsxFileName = \"D:\\\\Via\\\\New folder\\\\v2\\\\src\\\\pages\\\\VirtualTryOn.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useRef } from 'react';\nimport { motion } from 'framer-motion';\nimport { ArrowLeft, Settings, RotateCcw, Home, Eye, Camera } from 'lucide-react';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst Tryon = ({\n  onBackToHome\n}) => {\n  _s();\n  // Refs for DOM elements\n  const videoRef = useRef(null);\n  const capturedImageRef = useRef(null);\n  const canvasRef = useRef(null);\n\n  // State variables\n  const [isCaptured, setIsCaptured] = useState(false);\n  const [selectedProduct, setSelectedProduct] = useState(null);\n  const [isRightHand, setIsRightHand] = useState(false);\n  const [showProductSelection, setShowProductSelection] = useState(false);\n  const [activeTab, setActiveTab] = useState('Watches');\n  const [showHandGuide, setShowHandGuide] = useState(true);\n  const [isMobile, setIsMobile] = useState(false);\n\n  // Watch size customization state\n  const [userGender, setUserGender] = useState('men'); // 'men' or 'women'\n  const [userWristSize, setUserWristSize] = useState(64); // Default men's wrist size in mm\n  const [showWristSizeModal, setShowWristSizeModal] = useState(false); // Mobile-friendly modal\n\n  // Autocapture state variables\n  const [isAutoCaptureEnabled, setIsAutoCaptureEnabled] = useState(false);\n  const [countdown, setCountdown] = useState(0);\n  const [isHandInPosition, setIsHandInPosition] = useState(false);\n  const [isCountdownActive, setIsCountdownActive] = useState(false);\n\n  // Add new state for panel position\n  const [panelPosition, setPanelPosition] = useState(0);\n  const [isDragging, setIsDragging] = useState(false);\n  const [startY, setStartY] = useState(0);\n  const panelRef = useRef(null);\n\n  // Detect mobile device and set viewport height for Chrome mobile\n  useEffect(() => {\n    const checkMobile = () => {\n      setIsMobile(window.innerWidth <= 768);\n    };\n\n    // Fix viewport height for Chrome mobile\n    const setVH = () => {\n      const vh = window.innerHeight * 0.01;\n      document.documentElement.style.setProperty('--vh', `${vh}px`);\n    };\n    checkMobile();\n    setVH();\n    window.addEventListener('resize', () => {\n      checkMobile();\n      setVH();\n    });\n    window.addEventListener('orientationchange', () => {\n      setTimeout(() => {\n        setVH();\n      }, 100);\n    });\n    return () => {\n      window.removeEventListener('resize', checkMobile);\n      window.removeEventListener('orientationchange', setVH);\n    };\n  }, []);\n\n  // Realistic sizing configuration\n  // Average adult wrist circumference: 165mm (men), 155mm (women)\n  // Average wrist width when viewed from top: ~55-65mm\n  // SVG guide circle represents the wrist area (120px diameter in 800px viewBox)\n  // This means the circle represents approximately 60mm in real life\n\n  // Default wrist sizes by gender\n  const DEFAULT_WRIST_SIZES = {\n    men: 64,\n    // mm - default men's wrist size\n    women: 54 // mm - default women's wrist size\n  };\n  const SVG_WRIST_CIRCLE_DIAMETER = 120; // SVG circle diameter in viewBox units\n  const SVG_VIEWBOX_WIDTH = 800; // SVG viewBox width\n  const SVG_VIEWBOX_HEIGHT = 600; // SVG viewBox height\n\n  // Size configuration - Increased for better visibility\n  const WATCH_WIDTH = 25; // percentage of container width\n  const BRACELET_WIDTH = 15; // percentage of container width\n  const WATCH_HEIGHT = 38; // percentage of container height\n  const BRACELET_HEIGHT = 55; // percentage of container height - INCREASED for better visibility\n\n  // Test: Try changing WATCH_HEIGHT to 50 to see a much taller watch\n  // Test: Try changing WATCH_HEIGHT to 15 to see a shorter watch\n  // Test: Try changing BRACELET_HEIGHT to 70 to see a much taller bracelet\n  // Test: Try changing BRACELET_HEIGHT to 20 to see a shorter bracelet\n  // Note: Scale is now dynamic - smaller height = smaller scale, larger height = larger scale\n\n  // Calculate realistic watch sizing based on actual dimensions and user's wrist size\n  const calculateWatchDimensions = (watch, containerWidth, containerHeight) => {\n    // Get the default wrist size for the current gender\n    const defaultWristSize = DEFAULT_WRIST_SIZES[userGender];\n\n    // Calculate relative scaling factor based on user's wrist size vs default\n    const wristSizeRatio = userWristSize / defaultWristSize;\n\n    // Calculate the scale factor from real world to SVG coordinates using user's wrist size\n    const mmToSvgScale = SVG_WRIST_CIRCLE_DIAMETER / userWristSize;\n\n    // Convert watch dimensions to SVG units\n    const watchWidthSvg = watch.totalWidth * mmToSvgScale;\n    const watchHeightSvg = watch.totalHeight * mmToSvgScale;\n    const dialDiameterSvg = watch.dialDiameter * mmToSvgScale;\n\n    // Convert SVG units to container percentages\n    const watchWidthPercent = watchWidthSvg / SVG_VIEWBOX_WIDTH * 100;\n    const watchHeightPercent = watchHeightSvg / SVG_VIEWBOX_HEIGHT * 100;\n    const dialDiameterPercent = dialDiameterSvg / SVG_VIEWBOX_WIDTH * 100;\n\n    // Calculate positioning - watches should be positioned on the wrist circle\n    // SVG circle is at cx=\"400\" cy=\"300\" (center of the hand area)\n    const positionX = 50; // Center horizontally (400/800 = 50%)\n    const positionY = 50; // Center vertically (300/600 = 50%)\n\n    return {\n      width: Math.max(watchWidthPercent, 8),\n      // Minimum 8% for visibility\n      height: Math.max(watchHeightPercent, 10),\n      // Minimum 10% for visibility\n      dialDiameter: dialDiameterPercent,\n      positionX,\n      positionY,\n      scale: Math.min(watchWidthPercent / 15, watchHeightPercent / 20) * wristSizeRatio,\n      // Dynamic scale with wrist size adjustment\n      realWidth: watch.totalWidth,\n      realHeight: watch.totalHeight,\n      caseDiameter: watch.caseDiameter,\n      wristSizeRatio // Include ratio for additional scaling if needed\n    };\n  };\n\n  // Calculate watch position based on hand orientation and anatomy\n  const getWatchPosition = (watchData, isRightHand) => {\n    const baseDimensions = calculateWatchDimensions(watchData, 400, 600);\n\n    // Adjust position based on hand orientation\n    // Watches are typically worn on the top of the wrist\n    let adjustedX = baseDimensions.positionX;\n    let adjustedY = baseDimensions.positionY - 2; // Slightly higher on the wrist\n\n    // For right hand, watch might be positioned slightly differently\n    if (isRightHand) {\n      adjustedX = baseDimensions.positionX + 1; // Slight adjustment for right hand\n    }\n\n    // Adjust for different watch types\n    switch (watchData.type) {\n      case 'smartwatch':\n        // Smart watches are often worn higher on the wrist\n        adjustedY -= 1;\n        break;\n      case 'luxury':\n        // Luxury watches might be positioned more precisely\n        adjustedY -= 0.5;\n        break;\n      case 'sport':\n        // Sport watches might be worn slightly looser\n        adjustedY += 0.5;\n        break;\n      default:\n        break;\n    }\n    return {\n      ...baseDimensions,\n      positionX: adjustedX,\n      positionY: adjustedY\n    };\n  };\n  // Realistic watch data with actual dimensions (in millimeters)\n  const watches = [{\n    name: \"Classic Black\",\n    path: \"imgs/watches/watch_1.png\",\n    // Rolex Submariner style - 40mm case\n    caseDiameter: 41,\n    // mm\n    caseThickness: 12.5,\n    // mm\n    totalWidth: 42,\n    // mm (including crown)\n    totalHeight: 47,\n    // mm (lug to lug)\n    dialDiameter: 31,\n    // mm (visible dial)\n    type: \"dress\",\n    dialSize: 40\n  }, {\n    name: \"Silver Chrono\",\n    path: \"imgs/watches/watch_2.png\",\n    // Omega Speedmaster style - 42mm case\n    caseDiameter: 42,\n    // mm\n    caseThickness: 13.2,\n    // mm\n    totalWidth: 44,\n    // mm\n    totalHeight: 48.5,\n    // mm\n    dialDiameter: 33,\n    // mm\n    type: \"sport\",\n    dialSize: 42\n  }, {\n    name: \"Gold Luxury\",\n    path: \"imgs/watches/watch_3.png\",\n    // Patek Philippe Calatrava style - 38mm case\n    caseDiameter: 39,\n    // mm\n    caseThickness: 8.5,\n    // mm\n    totalWidth: 39,\n    // mm\n    totalHeight: 45,\n    // mm\n    dialDiameter: 30,\n    // mm\n    type: \"luxury\",\n    dialSize: 38\n  }, {\n    name: \"Sport Blue\",\n    path: \"imgs/watches/watch_6.png\",\n    // Apple Watch style - 44mm case\n    caseDiameter: 41,\n    // mm (width)\n    caseThickness: 10.7,\n    // mm\n    totalWidth: 44,\n    // mm\n    totalHeight: 38,\n    // mm (height - rectangular)\n    dialDiameter: 35,\n    // mm (screen diagonal)\n    type: \"smartwatch\",\n    dialSize: 44\n  }, {\n    name: \"Minimalist\",\n    path: \"imgs/watches/watch_5.png\",\n    // Daniel Wellington style - 36mm case\n    caseDiameter: 36,\n    // mm\n    caseThickness: 6,\n    // mm\n    totalWidth: 37,\n    // mm\n    totalHeight: 43,\n    // mm\n    dialDiameter: 28,\n    // mm\n    type: \"minimalist\",\n    dialSize: 36\n  }, {\n    name: \"Rose Gold\",\n    path: \"imgs/watches/watch_4.png\",\n    // Michael Kors style - 39mm case\n    caseDiameter: 44,\n    // mm\n    caseThickness: 11,\n    // mm\n    totalWidth: 41,\n    // mm\n    totalHeight: 46,\n    // mm\n    dialDiameter: 31,\n    // mm\n    type: \"fashion\",\n    dialSize: 41\n  }];\n  const bracelets = [{\n    name: \"Silver Chain\",\n    path: \"bracelets/bracelet_1.png\"\n  }, {\n    name: \"Gold Bangle\",\n    path: \"bracelets/bracelet_2.png\"\n  }, {\n    name: \"Leather Wrap\",\n    path: \"bracelets/bracelet_3.png\"\n  }, {\n    name: \"Diamond Tennis\",\n    path: \"bracelets/bracelet_4.png\"\n  }, {\n    name: \"Beaded Stone\",\n    path: \"bracelets/bracelet_5.png\"\n  }, {\n    name: \"Charm Bracelet\",\n    path: \"bracelets/bracelet_6.png\"\n  }];\n\n  // Initialize camera\n  const initCamera = async () => {\n    try {\n      const stream = await navigator.mediaDevices.getUserMedia({\n        video: {\n          facingMode: 'environment',\n          width: {\n            ideal: 1920\n          },\n          height: {\n            ideal: 1080\n          }\n        }\n      });\n      if (videoRef.current) {\n        videoRef.current.srcObject = stream;\n      }\n    } catch (err) {\n      console.error(\"Error accessing camera:\", err);\n      // Demo mode fallback\n      if (capturedImageRef.current) {\n        capturedImageRef.current.src = \"sample-hand.jpg\";\n        capturedImageRef.current.style.display = \"block\";\n      }\n      console.log(\"Camera not available - demo mode\");\n      setIsCaptured(true);\n      setShowProductSelection(true);\n    }\n  };\n\n  // Capture current frame\n  const captureFrame = () => {\n    if (!videoRef.current || !canvasRef.current) return null;\n    const canvas = canvasRef.current;\n    const video = videoRef.current;\n    canvas.width = video.videoWidth;\n    canvas.height = video.videoHeight;\n    const ctx = canvas.getContext('2d');\n    ctx.drawImage(video, 0, 0, canvas.width, canvas.height);\n    return canvas.toDataURL('image/png');\n  };\n\n  // Improved background remover that preserves light colors\n  const removeBackground = (imgElement, productType = 'watch') => {\n    const img = new Image();\n    img.crossOrigin = 'anonymous';\n    img.onload = function () {\n      const canvas = document.createElement('canvas');\n      const ctx = canvas.getContext('2d');\n      canvas.width = img.naturalWidth;\n      canvas.height = img.naturalHeight;\n      ctx.drawImage(img, 0, 0);\n      try {\n        const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);\n        const data = imageData.data;\n        const width = canvas.width;\n        const height = canvas.height;\n\n        // First pass: Identify edge pixels to preserve product boundaries\n        const edgePixels = new Set();\n        for (let y = 1; y < height - 1; y++) {\n          for (let x = 1; x < width - 1; x++) {\n            const idx = (y * width + x) * 4;\n            const r = data[idx];\n            const g = data[idx + 1];\n            const b = data[idx + 2];\n\n            // Check surrounding pixels for edge detection\n            let isEdge = false;\n            for (let dy = -1; dy <= 1; dy++) {\n              for (let dx = -1; dx <= 1; dx++) {\n                if (dx === 0 && dy === 0) continue;\n                const neighborIdx = ((y + dy) * width + (x + dx)) * 4;\n                const nr = data[neighborIdx];\n                const ng = data[neighborIdx + 1];\n                const nb = data[neighborIdx + 2];\n\n                // If there's a significant color difference, it's an edge\n                const colorDiff = Math.abs(r - nr) + Math.abs(g - ng) + Math.abs(b - nb);\n                if (colorDiff > 60) {\n                  isEdge = true;\n                  break;\n                }\n              }\n              if (isEdge) break;\n            }\n            if (isEdge) {\n              edgePixels.add(idx / 4);\n            }\n          }\n        }\n\n        // Second pass: Remove only white background while preserving all product colors\n        for (let i = 0; i < data.length; i += 4) {\n          const r = data[i];\n          const g = data[i + 1];\n          const b = data[i + 2];\n          const pixelIndex = i / 4;\n\n          // Calculate color properties\n          const brightness = (r + g + b) / 3;\n\n          // Check if pixel is near edges (preserve product boundaries)\n          const isNearEdge = edgePixels.has(pixelIndex);\n\n          // Only remove pure white or near-white pixels that aren't edges\n          const isPureWhite = r > 245 && g > 245 && b > 245 && Math.abs(r - g) < 10 && Math.abs(g - b) < 10 && !isNearEdge;\n\n          // Remove background if it's pure white and not an edge\n          if (isPureWhite) {\n            data[i + 3] = 0; // Make fully transparent\n          } else if (brightness > 240 && !isNearEdge) {\n            // For very bright but not pure white, reduce opacity slightly\n            data[i + 3] = Math.max(0, data[i + 3] - 50);\n          }\n        }\n        ctx.putImageData(imageData, 0, 0);\n        imgElement.src = canvas.toDataURL('image/png');\n\n        // Apply product-specific styling that preserves all colors\n        imgElement.style.filter = 'none';\n        imgElement.style.mixBlendMode = 'normal';\n        imgElement.style.opacity = '1';\n      } catch (e) {\n        console.warn('Canvas operation failed:', e);\n        // Fallback styling that preserves all colors\n        imgElement.style.filter = 'none';\n        imgElement.style.mixBlendMode = 'normal';\n        imgElement.style.opacity = '1';\n      }\n    };\n    img.onerror = function () {\n      console.warn('Image loading failed');\n      // Fallback styling\n      imgElement.style.filter = 'none';\n      imgElement.style.mixBlendMode = 'normal';\n      imgElement.style.opacity = '1';\n    };\n    img.src = imgElement.src;\n  };\n\n  // Placeholder for hand detection\n  const detectHandOrientation = imageData => {\n    // Simple heuristic for demo purposes\n    return Math.random() > 0.5;\n  };\n\n  // Detect if arm and wrist are within the SVG guide area\n  const detectHandInPosition = () => {\n    if (!videoRef.current || !canvasRef.current) return false;\n    const video = videoRef.current;\n    const canvas = canvasRef.current;\n    const ctx = canvas.getContext('2d');\n\n    // Set canvas size to match video\n    canvas.width = video.videoWidth;\n    canvas.height = video.videoHeight;\n\n    // Draw current video frame\n    ctx.drawImage(video, 0, 0, canvas.width, canvas.height);\n\n    // Get the video container dimensions to calculate the guide area\n    const videoContainer = video.parentElement;\n    const containerRect = videoContainer.getBoundingClientRect();\n\n    // Calculate the actual video display area (accounting for object-fit: cover)\n    const videoAspect = video.videoWidth / video.videoHeight;\n    const containerAspect = containerRect.width / containerRect.height;\n    let displayWidth, displayHeight, offsetX, offsetY;\n    if (videoAspect > containerAspect) {\n      // Video is wider - height fills container, width is cropped\n      displayHeight = containerRect.height;\n      displayWidth = displayHeight * videoAspect;\n      offsetX = (displayWidth - containerRect.width) / 2;\n      offsetY = 0;\n    } else {\n      // Video is taller - width fills container, height is cropped\n      displayWidth = containerRect.width;\n      displayHeight = displayWidth / videoAspect;\n      offsetX = 0;\n      offsetY = (displayHeight - containerRect.height) / 2;\n    }\n\n    // Calculate the guide areas in canvas coordinates\n    // SVG viewBox is 800x600\n    // Main rectangle: x=\"320\" y=\"150\" width=\"160\" height=\"300\" (wrist/forearm area)\n    // Circle: cx=\"400\" cy=\"300\" r=\"60\" (hand area)\n\n    const scaleX = canvas.width / displayWidth;\n    const scaleY = canvas.height / displayHeight;\n\n    // Wrist/forearm area (rectangle)\n    const rectX = Math.max(0, (320 / 800 * displayWidth - offsetX) * scaleX);\n    const rectY = Math.max(0, (150 / 600 * displayHeight - offsetY) * scaleY);\n    const rectWidth = Math.min(canvas.width - rectX, 160 / 800 * displayWidth * scaleX);\n    const rectHeight = Math.min(canvas.height - rectY, 300 / 600 * displayHeight * scaleY);\n\n    // Hand area (circle)\n    const circleX = Math.max(0, (340 / 800 * displayWidth - offsetX) * scaleX); // Adjusted for circle bounds\n    const circleY = Math.max(0, (240 / 600 * displayHeight - offsetY) * scaleY); // Adjusted for circle bounds\n    const circleWidth = Math.min(canvas.width - circleX, 120 / 800 * displayWidth * scaleX); // Circle diameter\n    const circleHeight = Math.min(canvas.height - circleY, 120 / 600 * displayHeight * scaleY); // Circle diameter\n\n    try {\n      // Check wrist/forearm area (rectangle)\n      const rectImageData = ctx.getImageData(rectX, rectY, rectWidth, rectHeight);\n      const rectData = rectImageData.data;\n\n      // Check hand area (circle approximation)\n      const circleImageData = ctx.getImageData(circleX, circleY, circleWidth, circleHeight);\n      const circleData = circleImageData.data;\n      let rectSkinPixels = 0;\n      let rectTotalPixels = 0;\n      let circleSkinPixels = 0;\n      let circleTotalPixels = 0;\n\n      // Enhanced skin tone detection\n      const isSkinTone = (r, g, b) => {\n        // Multiple skin tone ranges to cover different skin colors\n        const condition1 = r > 95 && g > 40 && b > 20 && r > g && r > b && Math.abs(r - g) > 15;\n        const condition2 = r > 220 && g > 210 && b > 170 && Math.abs(r - g) < 15 && Math.abs(g - b) < 15;\n        const condition3 = r > 120 && g > 80 && b > 50 && r > g && g > b;\n        const condition4 = r > 180 && g > 120 && b > 90 && r > g && g >= b;\n        return condition1 || condition2 || condition3 || condition4;\n      };\n\n      // Analyze rectangle area (wrist/forearm)\n      for (let i = 0; i < rectData.length; i += 4) {\n        const r = rectData[i];\n        const g = rectData[i + 1];\n        const b = rectData[i + 2];\n        if (isSkinTone(r, g, b)) {\n          rectSkinPixels++;\n        }\n        rectTotalPixels++;\n      }\n\n      // Analyze circle area (hand)\n      for (let i = 0; i < circleData.length; i += 4) {\n        const r = circleData[i];\n        const g = circleData[i + 1];\n        const b = circleData[i + 2];\n        if (isSkinTone(r, g, b)) {\n          circleSkinPixels++;\n        }\n        circleTotalPixels++;\n      }\n\n      // Calculate skin ratios\n      const rectSkinRatio = rectSkinPixels / rectTotalPixels;\n      const circleSkinRatio = circleSkinPixels / circleTotalPixels;\n\n      // Both wrist/forearm AND hand areas must have sufficient skin tone presence\n      // Wrist area needs at least 20% skin tone (forearm/wrist)\n      // Hand area needs at least 25% skin tone (hand/fingers)\n      const wristInPosition = rectSkinRatio > 0.20;\n      const handInPosition = circleSkinRatio > 0.25;\n      return wristInPosition && handInPosition;\n    } catch (error) {\n      console.warn('Hand detection error:', error);\n      return false;\n    }\n  };\n\n  // Apply product to watch position with dynamic scaling\n  const applyProductToWatchPosition = (productPath, productType) => {\n    // Clear any existing product first\n    setSelectedProduct(null);\n\n    // Find the watch data for dial size\n    const watchData = watches.find(w => w.path === productPath);\n\n    // Small delay to ensure the old product is removed before adding new one\n    setTimeout(() => {\n      setSelectedProduct({\n        path: productPath,\n        dialSize: (watchData === null || watchData === void 0 ? void 0 : watchData.dialSize) || 40 // Default to 40mm if not found\n      });\n    }, 50);\n  };\n\n  // Handle capture button click\n  const handleCapture = () => {\n    if (!isCaptured) {\n      const capturedDataUrl = captureFrame();\n      if (capturedImageRef.current && capturedDataUrl) {\n        capturedImageRef.current.src = capturedDataUrl;\n        capturedImageRef.current.style.display = 'block';\n      }\n      setIsCaptured(true);\n      setShowProductSelection(true); // Show products immediately\n      setShowHandGuide(false);\n\n      // Detect hand orientation\n      if (canvasRef.current && videoRef.current) {\n        const canvas = canvasRef.current;\n        const ctx = canvas.getContext('2d');\n        canvas.width = videoRef.current.videoWidth;\n        canvas.height = videoRef.current.videoHeight;\n        ctx.drawImage(videoRef.current, 0, 0);\n        const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);\n        setIsRightHand(detectHandOrientation(imageData));\n      }\n    } else {\n      setShowProductSelection(true);\n    }\n  };\n\n  // Handle back button click\n  const handleBack = () => {\n    if (capturedImageRef.current) {\n      capturedImageRef.current.style.display = 'none';\n    }\n    setIsCaptured(false);\n    setSelectedProduct(null);\n    setIsRightHand(false);\n    setShowProductSelection(false);\n    setShowWristSizeModal(false);\n    setShowHandGuide(true);\n  };\n\n  // Handle gender selection\n  const handleGenderChange = gender => {\n    setUserGender(gender);\n    setUserWristSize(DEFAULT_WRIST_SIZES[gender]);\n  };\n\n  // Handle wrist size change\n  const handleWristSizeChange = size => {\n    setUserWristSize(size);\n  };\n\n  // Handle continue to product selection - No longer needed since both panels show together\n  // const handleContinueToProducts = () => {\n  //   setShowWristSizeInput(false);\n  //   setShowProductSelection(true);\n  // };\n\n  // Handle tab change\n  const handleTabChange = tabName => {\n    setActiveTab(tabName);\n  };\n\n  // Handle product selection\n  const handleProductSelect = product => {\n    applyProductToWatchPosition(product.path, activeTab);\n  };\n\n  // Initialize camera on component mount\n  useEffect(() => {\n    initCamera();\n  }, []);\n\n  // Hand position monitoring effect (only when autocapture is enabled)\n  useEffect(() => {\n    if (!isAutoCaptureEnabled || isCaptured) return;\n    const interval = setInterval(() => {\n      const handInPosition = detectHandInPosition();\n      setIsHandInPosition(handInPosition);\n\n      // Only start countdown if hand is properly positioned\n      if (handInPosition && !isCountdownActive && countdown === 0) {\n        setIsCountdownActive(true);\n      }\n\n      // Stop countdown if hand moves out of position\n      if (!handInPosition && isCountdownActive) {\n        setIsCountdownActive(false);\n        setCountdown(0);\n      }\n    }, 150); // Check every 150ms for better performance\n\n    return () => clearInterval(interval);\n  }, [isAutoCaptureEnabled, isCaptured, isCountdownActive, countdown]);\n\n  // Countdown effect (starts automatically when hand is in position)\n  useEffect(() => {\n    if (!isCountdownActive || isCaptured) {\n      setCountdown(0);\n      return;\n    }\n\n    // Only start countdown if hand is still in position\n    if (!isHandInPosition) {\n      setIsCountdownActive(false);\n      setCountdown(0);\n      return;\n    }\n\n    // Start countdown\n    setCountdown(3);\n    const countdownInterval = setInterval(() => {\n      setCountdown(prev => {\n        // Double-check hand position before continuing countdown\n        if (!isHandInPosition) {\n          clearInterval(countdownInterval);\n          setIsCountdownActive(false);\n          return 0;\n        }\n        if (prev <= 1) {\n          // Countdown finished - trigger capture\n          clearInterval(countdownInterval);\n          setIsCountdownActive(false);\n          setIsAutoCaptureEnabled(false); // Turn off autocapture after capture\n          handleCapture();\n          return 0;\n        }\n        return prev - 1;\n      });\n    }, 1000);\n    return () => clearInterval(countdownInterval);\n  }, [isCountdownActive, isCaptured, isHandInPosition]);\n\n  // Handle autocapture switch toggle\n  const handleAutoCaptureToggle = () => {\n    const newState = !isAutoCaptureEnabled;\n    setIsAutoCaptureEnabled(newState);\n\n    // Reset states when turning off\n    if (!newState) {\n      setIsCountdownActive(false);\n      setCountdown(0);\n      setIsHandInPosition(false);\n    }\n  };\n\n  // Reset autocapture when going back\n  const handleBackWithReset = () => {\n    setIsAutoCaptureEnabled(false);\n    setIsCountdownActive(false);\n    setCountdown(0);\n    setIsHandInPosition(false);\n    setShowWristSizeModal(false);\n    setUserGender('men');\n    setUserWristSize(64);\n    handleBack();\n  };\n\n  // Get current products based on active tab\n  const getCurrentProducts = () => {\n    return activeTab === 'Watches' ? watches : bracelets;\n  };\n\n  // Add touch gesture handlers\n  const handleTouchStart = e => {\n    setIsDragging(true);\n    setStartY(e.touches[0].clientY);\n  };\n  const handleTouchMove = e => {\n    if (!isDragging) return;\n    const currentY = e.touches[0].clientY;\n    const diff = currentY - startY;\n\n    // Only allow dragging down\n    if (diff > 0) {\n      setPanelPosition(diff);\n    }\n  };\n  const handleTouchEnd = () => {\n    if (!isDragging) return;\n    setIsDragging(false);\n\n    // If dragged more than 100px down, close the panel\n    if (panelPosition > 100) {\n      setShowProductSelection(false);\n    }\n\n    // Reset position\n    setPanelPosition(0);\n  };\n\n  // Add click outside handler for modals\n  useEffect(() => {\n    const handleClickOutside = e => {\n      if (showWristSizeModal && !e.target.closest('.modal-content')) {\n        setShowWristSizeModal(false);\n      }\n    };\n    document.addEventListener('mousedown', handleClickOutside);\n    document.addEventListener('touchstart', handleClickOutside);\n    return () => {\n      document.removeEventListener('mousedown', handleClickOutside);\n      document.removeEventListener('touchstart', handleClickOutside);\n    };\n  }, [showWristSizeModal]);\n\n  // Update product selection panel JSX\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"relative h-screen flex flex-col bg-gray-50 font-sans overflow-hidden\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex-1 relative overflow-hidden bg-black flex items-center justify-center\",\n      children: [/*#__PURE__*/_jsxDEV(\"video\", {\n        ref: videoRef,\n        className: \"w-full h-full object-cover\",\n        autoPlay: true,\n        playsInline: true,\n        muted: true\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 780,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"canvas\", {\n        ref: canvasRef,\n        className: \"hidden\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 787,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"img\", {\n        ref: capturedImageRef,\n        className: \"absolute top-0 left-0 w-full h-full object-cover hidden\",\n        alt: \"Captured hand\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 788,\n        columnNumber: 9\n      }, this), !isCaptured && /*#__PURE__*/_jsxDEV(\"button\", {\n        className: \"absolute top-4 left-4 bg-black/60 text-white p-3 rounded-full text-xl font-bold cursor-pointer z-10 border-none shadow-lg transition-all duration-200 flex items-center justify-center w-11 h-11 hover:bg-black/80 focus:outline-none focus:ring-2 focus:ring-primary-green focus:ring-offset-2\",\n        onClick: onBackToHome,\n        \"aria-label\": \"Home\",\n        children: /*#__PURE__*/_jsxDEV(Home, {\n          className: \"w-5 h-5\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 801,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 796,\n        columnNumber: 11\n      }, this), !isCaptured && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"absolute top-4 right-4 z-20 flex flex-col items-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          className: \"text-primary-green font-bold text-sm md:text-base mb-1.5 tracking-wide\",\n          children: \"Auto Capture\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 808,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n          className: \"relative inline-flex items-center cursor-pointer\",\n          children: [/*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"checkbox\",\n            checked: isAutoCaptureEnabled,\n            onChange: handleAutoCaptureToggle,\n            disabled: isCountdownActive,\n            \"aria-label\": \"Toggle auto capture\",\n            className: \"sr-only\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 810,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: `relative w-14 h-8 rounded-full transition-colors duration-200 ${isAutoCaptureEnabled ? 'bg-primary-green' : 'bg-gray-300'}`,\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: `absolute top-1 left-1 w-6 h-6 bg-white rounded-full transition-transform duration-200 ${isAutoCaptureEnabled ? 'translate-x-6' : 'translate-x-0'}`,\n              children: isAutoCaptureEnabled ? /*#__PURE__*/_jsxDEV(\"svg\", {\n                className: \"w-4 h-4 text-primary-green absolute top-1 left-1\",\n                viewBox: \"0 0 12 10\",\n                children: /*#__PURE__*/_jsxDEV(\"polyline\", {\n                  points: \"1.5 6 5 9 10.5 1\",\n                  fill: \"none\",\n                  stroke: \"currentColor\",\n                  strokeWidth: \"2\",\n                  strokeLinecap: \"round\",\n                  strokeLinejoin: \"round\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 826,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 825,\n                columnNumber: 21\n              }, this) : /*#__PURE__*/_jsxDEV(\"svg\", {\n                className: \"w-4 h-4 text-gray-400 absolute top-1 left-1\",\n                viewBox: \"0 0 10 10\",\n                children: [/*#__PURE__*/_jsxDEV(\"line\", {\n                  x1: \"1\",\n                  y1: \"1\",\n                  x2: \"9\",\n                  y2: \"9\",\n                  stroke: \"currentColor\",\n                  strokeWidth: \"2\",\n                  strokeLinecap: \"round\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 830,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"line\", {\n                  x1: \"9\",\n                  y1: \"1\",\n                  x2: \"1\",\n                  y2: \"9\",\n                  stroke: \"currentColor\",\n                  strokeWidth: \"2\",\n                  strokeLinecap: \"round\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 831,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 829,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 821,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 818,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 809,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 807,\n        columnNumber: 11\n      }, this), isCaptured && /*#__PURE__*/_jsxDEV(\"button\", {\n        className: \"absolute top-4 left-4 bg-black/60 text-white p-3 rounded-full text-xl font-bold cursor-pointer z-10 border-none shadow-lg transition-all duration-200 flex items-center justify-center w-11 h-11 hover:bg-black/80 focus:outline-none focus:ring-2 focus:ring-primary-green focus:ring-offset-2\",\n        onClick: handleBackWithReset,\n        \"aria-label\": \"Back\",\n        children: /*#__PURE__*/_jsxDEV(ArrowLeft, {\n          className: \"w-5 h-5\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 847,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 842,\n        columnNumber: 11\n      }, this), isCountdownActive && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 z-30 text-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-6xl md:text-8xl font-bold text-white mb-2 drop-shadow-lg\",\n          children: countdown\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 854,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-lg md:text-xl text-white font-medium bg-black/50 px-4 py-2 rounded-full\",\n          children: \"Auto capturing...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 855,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 853,\n        columnNumber: 11\n      }, this), isAutoCaptureEnabled && !isHandInPosition && !isCountdownActive && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"absolute bottom-32 left-1/2 transform -translate-x-1/2 z-20 text-center max-w-sm mx-auto\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-black/70 text-white px-4 py-3 rounded-xl mb-2 font-medium\",\n          children: \"Position your arm and wrist in the guide area\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 862,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-black/50 text-white/80 px-3 py-2 rounded-lg text-sm\",\n          children: \"Countdown will start automatically when detected\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 865,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 861,\n        columnNumber: 11\n      }, this), isAutoCaptureEnabled && isHandInPosition && !isCountdownActive && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"absolute bottom-32 left-1/2 transform -translate-x-1/2 z-20 text-center\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-primary-green/90 text-white px-6 py-3 rounded-xl font-medium\",\n          children: \"Perfect! Starting countdown...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 873,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 872,\n        columnNumber: 11\n      }, this), showHandGuide && /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          ...styles.handGuide,\n          opacity: isAutoCaptureEnabled && isHandInPosition ? 0.9 : 0.8,\n          filter: isAutoCaptureEnabled && isHandInPosition ? 'drop-shadow(0 2px 8px rgba(45, 140, 136, 0.5))' : isAutoCaptureEnabled && !isHandInPosition ? 'drop-shadow(0 2px 8px rgba(255, 107, 107, 0.5))' : 'drop-shadow(0 2px 8px rgba(255, 255, 255, 0.3))'\n        },\n        className: isMobile ? 'mobile-hand-guide' : '',\n        \"aria-hidden\": \"true\",\n        children: /*#__PURE__*/_jsxDEV(\"svg\", {\n          viewBox: \"0 0 800 600\",\n          xmlns: \"http://www.w3.org/2000/svg\",\n          children: [/*#__PURE__*/_jsxDEV(\"path\", {\n            d: \"M100 480 C 150 460, 200 450, 250 450 L 550 450 C 600 450, 650 460, 700 480\",\n            stroke: isAutoCaptureEnabled && isHandInPosition ? \"#2D8C88\" : isAutoCaptureEnabled && !isHandInPosition ? \"#ff6b6b\" : \"white\",\n            strokeWidth: \"5\",\n            fill: \"none\",\n            strokeLinecap: \"round\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 896,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n            d: \"M100 120 C 150 140, 200 150, 250 150 L 550 150 C 600 150, 650 140, 700 120\",\n            stroke: isAutoCaptureEnabled && isHandInPosition ? \"#2D8C88\" : isAutoCaptureEnabled && !isHandInPosition ? \"#ff6b6b\" : \"white\",\n            strokeWidth: \"5\",\n            fill: \"none\",\n            strokeLinecap: \"round\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 909,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"rect\", {\n            x: \"320\",\n            y: \"150\",\n            width: \"160\",\n            height: \"300\",\n            fill: isAutoCaptureEnabled && isHandInPosition ? \"#2D8C88\" : isAutoCaptureEnabled && !isHandInPosition ? \"#ff6b6b\" : \"white\",\n            opacity: isAutoCaptureEnabled && isHandInPosition ? \"0.25\" : \"0.15\",\n            rx: \"15\",\n            stroke: isAutoCaptureEnabled && isHandInPosition ? \"#2D8C88\" : isAutoCaptureEnabled && !isHandInPosition ? \"#ff6b6b\" : \"white\",\n            strokeWidth: \"2\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 923,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"circle\", {\n            cx: \"400\",\n            cy: \"300\",\n            r: \"60\",\n            fill: isAutoCaptureEnabled && isHandInPosition ? \"#2D8C88\" : isAutoCaptureEnabled && !isHandInPosition ? \"#ff6b6b\" : \"white\",\n            opacity: isAutoCaptureEnabled && isHandInPosition ? \"0.3\" : \"0.2\",\n            stroke: isAutoCaptureEnabled && isHandInPosition ? \"#2D8C88\" : isAutoCaptureEnabled && !isHandInPosition ? \"#ff6b6b\" : \"white\",\n            strokeWidth: \"2\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 947,\n            columnNumber: 15\n          }, this), isAutoCaptureEnabled && /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: [/*#__PURE__*/_jsxDEV(\"text\", {\n              x: \"400\",\n              y: \"140\",\n              textAnchor: \"middle\",\n              fill: \"white\",\n              fontSize: \"14\",\n              fontWeight: \"bold\",\n              children: \"WRIST & FOREARM\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 971,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"text\", {\n              x: \"400\",\n              y: \"480\",\n              textAnchor: \"middle\",\n              fill: \"white\",\n              fontSize: \"14\",\n              fontWeight: \"bold\",\n              children: \"HAND\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 974,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 894,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 881,\n        columnNumber: 11\n      }, this), selectedProduct && /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          ...styles.productPosition,\n          width: activeTab === 'Watches' ? `${WATCH_WIDTH}%` : `${BRACELET_WIDTH}%`,\n          height: activeTab === 'Watches' ? `${WATCH_HEIGHT}%` : `${BRACELET_HEIGHT}%`\n        },\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            position: 'relative',\n            width: '100%',\n            height: '100%',\n            display: 'flex',\n            alignItems: 'center',\n            justifyContent: 'center'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"img\", {\n            src: typeof selectedProduct === 'object' ? selectedProduct.path : selectedProduct,\n            alt: \"Selected product\",\n            style: {\n              width: '100%',\n              height: '100%',\n              objectFit: 'contain',\n              transform: activeTab === 'Bracelets' ? `rotate(90deg) scale(${BRACELET_HEIGHT / 30})${isRightHand ? ' scaleX(-1)' : ''}` : `scale(${WATCH_HEIGHT / 25 * (userWristSize / DEFAULT_WRIST_SIZES[userGender])})`,\n              filter: 'drop-shadow(0 2px 8px rgba(0,0,0,0.3))'\n            },\n            onLoad: e => removeBackground(e.target, activeTab === 'Watches' ? 'watch' : 'bracelet')\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 998,\n            columnNumber: 15\n          }, this), activeTab === 'Watches' && typeof selectedProduct === 'object' && /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              position: 'absolute',\n              bottom: '-30px',\n              left: '50%',\n              transform: 'translateX(-50%)',\n              fontSize: '11px',\n              fontWeight: '600',\n              color: 'white',\n              backgroundColor: 'rgba(45, 140, 136, 0.9)',\n              padding: '3px 8px',\n              borderRadius: '12px',\n              whiteSpace: 'nowrap',\n              pointerEvents: 'none',\n              boxShadow: '0 2px 6px rgba(0,0,0,0.3)',\n              zIndex: 2\n            },\n            children: [selectedProduct.dialSize, \"mm\", userWristSize !== DEFAULT_WRIST_SIZES[userGender] && /*#__PURE__*/_jsxDEV(\"span\", {\n              style: {\n                fontSize: '10px',\n                opacity: 0.8,\n                marginLeft: '4px'\n              },\n              children: [\"(scaled \", ((userWristSize / DEFAULT_WRIST_SIZES[userGender] - 1) * 100).toFixed(0), \"%)\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1031,\n              columnNumber: 21\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1013,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 990,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 985,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        style: styles.captureBtn,\n        className: isMobile ? 'mobile-capture-btn' : '',\n        onClick: handleCapture,\n        \"aria-label\": isCaptured ? \"Select Products\" : \"Capture\",\n        children: !isCaptured ? /*#__PURE__*/_jsxDEV(\"div\", {\n          style: styles.captureInner,\n          className: isMobile ? 'mobile-inner-circle' : ''\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1053,\n          columnNumber: 13\n        }, this) : /*#__PURE__*/_jsxDEV(\"svg\", {\n          width: \"24\",\n          height: \"24\",\n          viewBox: \"0 0 24 24\",\n          fill: \"white\",\n          children: /*#__PURE__*/_jsxDEV(\"path\", {\n            d: \"M8.59 16.59L13.17 12L8.59 7.41L10 6L16 12L10 18L8.59 16.59Z\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1056,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1055,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1046,\n        columnNumber: 9\n      }, this), isCaptured && /*#__PURE__*/_jsxDEV(\"button\", {\n        style: styles.resetBtn,\n        onClick: () => window.location.reload(),\n        \"aria-label\": \"Reset\",\n        children: /*#__PURE__*/_jsxDEV(\"svg\", {\n          width: \"20\",\n          height: \"20\",\n          viewBox: \"0 0 24 24\",\n          fill: \"white\",\n          children: /*#__PURE__*/_jsxDEV(\"path\", {\n            d: \"M17.65 6.35C16.2 4.9 14.21 4 12 4C7.58 4 4 7.58 4 12S7.58 20 12 20C15.73 20 18.84 17.45 19.73 14H17.65C16.83 16.33 14.61 18 12 18C8.69 18 6 15.31 6 12S8.69 6 12 6C13.66 6 15.14 6.69 16.22 7.78L13 11H20V4L17.65 6.35Z\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1069,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1068,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1063,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 779,\n      columnNumber: 7\n    }, this), isCaptured && /*#__PURE__*/_jsxDEV(\"button\", {\n      style: styles.wristSizeFloatingBtn,\n      className: isMobile ? 'mobile-btn' : '',\n      onClick: () => setShowWristSizeModal(true),\n      \"aria-label\": \"Adjust wrist size\",\n      children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n        width: \"16\",\n        height: \"16\",\n        viewBox: \"0 0 24 24\",\n        fill: \"white\",\n        children: /*#__PURE__*/_jsxDEV(\"path\", {\n          d: \"M12,15.5A3.5,3.5 0 0,1 8.5,12A3.5,3.5 0 0,1 12,8.5A3.5,3.5 0 0,1 15.5,12A3.5,3.5 0 0,1 12,15.5M19.43,12.97C19.47,12.65 19.5,12.33 19.5,12C19.5,11.67 19.47,11.34 19.43,11L21.54,9.37C21.73,9.22 21.78,8.95 21.66,8.73L19.66,5.27C19.54,5.05 19.27,4.96 19.05,5.05L16.56,6.05C16.04,5.66 15.5,5.32 14.87,5.07L14.5,2.42C14.46,2.18 14.25,2 14,2H10C9.75,2 9.54,2.18 9.5,2.42L9.13,5.07C8.5,5.32 7.96,5.66 7.44,6.05L4.95,5.05C4.73,4.96 4.46,5.05 4.34,5.27L2.34,8.73C2.22,8.95 2.27,9.22 2.46,9.37L4.57,11C4.53,11.34 4.5,11.67 4.5,12C4.5,12.33 4.53,12.65 4.57,12.97L2.46,14.63C2.27,14.78 2.22,15.05 2.34,15.27L4.34,18.73C4.46,18.95 4.73,19.03 4.95,18.95L7.44,17.94C7.96,18.34 8.5,18.68 9.13,18.93L9.5,21.58C9.54,21.82 9.75,22 10,22H14C14.25,22 14.46,21.82 14.5,21.58L14.87,18.93C15.5,18.68 16.04,18.34 16.56,17.94L19.05,18.95C19.27,19.03 19.54,18.95 19.66,18.73L21.66,15.27C21.78,15.05 21.73,14.78 21.54,14.63L19.43,12.97Z\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1084,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1083,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n        style: styles.wristSizeText,\n        children: [userWristSize, \"mm\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1086,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1077,\n      columnNumber: 9\n    }, this), showWristSizeModal && /*#__PURE__*/_jsxDEV(\"div\", {\n      style: styles.modalOverlay,\n      onClick: () => setShowWristSizeModal(false),\n      className: \"modal-overlay\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        style: styles.wristSizeModal,\n        onClick: e => e.stopPropagation(),\n        className: \"modal-content\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: styles.modalHeader,\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            style: styles.modalTitle,\n            children: \"Adjust Wrist Size\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1103,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            style: styles.modalCloseBtn,\n            onClick: () => setShowWristSizeModal(false),\n            \"aria-label\": \"Close\",\n            children: /*#__PURE__*/_jsxDEV(\"svg\", {\n              width: \"20\",\n              height: \"20\",\n              viewBox: \"0 0 24 24\",\n              fill: \"currentColor\",\n              children: /*#__PURE__*/_jsxDEV(\"path\", {\n                d: \"M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1110,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1109,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1104,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1102,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: styles.modalContent,\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            style: styles.genderSelection,\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              style: {\n                ...styles.genderButton,\n                ...(userGender === 'men' ? styles.genderButtonActive : {})\n              },\n              onClick: () => handleGenderChange('men'),\n              children: \"Men (64mm)\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1118,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              style: {\n                ...styles.genderButton,\n                ...(userGender === 'women' ? styles.genderButtonActive : {})\n              },\n              onClick: () => handleGenderChange('women'),\n              children: \"Women (54mm)\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1127,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1117,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: styles.sliderContainer,\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              style: styles.sliderLabel,\n              children: [\"Wrist Size: \", userWristSize, \"mm\", userWristSize !== DEFAULT_WRIST_SIZES[userGender] && /*#__PURE__*/_jsxDEV(\"span\", {\n                style: styles.sizeChange,\n                children: [\"(\", userWristSize > DEFAULT_WRIST_SIZES[userGender] ? '+' : '', ((userWristSize / DEFAULT_WRIST_SIZES[userGender] - 1) * 100).toFixed(0), \"%)\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1143,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1140,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"range\",\n              min: \"45\",\n              max: \"80\",\n              value: userWristSize,\n              onChange: e => handleWristSizeChange(parseInt(e.target.value)),\n              style: styles.slider\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1149,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: styles.sliderLabels,\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                children: \"45mm\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1158,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: \"80mm\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1159,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1157,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: styles.presetButtons,\n              children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                style: styles.presetButton,\n                onClick: () => handleWristSizeChange(50),\n                children: \"Small (50mm)\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1164,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                style: styles.presetButton,\n                onClick: () => handleWristSizeChange(DEFAULT_WRIST_SIZES[userGender]),\n                children: [\"Default (\", DEFAULT_WRIST_SIZES[userGender], \"mm)\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1170,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                style: styles.presetButton,\n                onClick: () => handleWristSizeChange(70),\n                children: \"Large (70mm)\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1176,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1163,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1139,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1115,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1097,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1092,\n      columnNumber: 9\n    }, this), showProductSelection && /*#__PURE__*/_jsxDEV(\"div\", {\n      ref: panelRef,\n      style: {\n        ...styles.productSelection,\n        transform: `translateY(${panelPosition}px)`,\n        touchAction: 'none'\n      },\n      className: isMobile ? 'mobile-product-panel' : '',\n      \"aria-modal\": \"true\",\n      role: \"dialog\",\n      onTouchStart: handleTouchStart,\n      onTouchMove: handleTouchMove,\n      onTouchEnd: handleTouchEnd,\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        style: styles.dragHandle,\n        \"aria-hidden\": \"true\",\n        onTouchStart: handleTouchStart,\n        onTouchMove: handleTouchMove,\n        onTouchEnd: handleTouchEnd\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1205,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: styles.productTabs,\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          style: {\n            ...styles.tab,\n            ...(activeTab === 'Watches' ? styles.activeTab : {})\n          },\n          onClick: () => handleTabChange('Watches'),\n          children: \"Watches\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1213,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          style: {\n            ...styles.tab,\n            ...(activeTab === 'Bracelets' ? styles.activeTab : {})\n          },\n          onClick: () => handleTabChange('Bracelets'),\n          children: \"Bracelets\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1222,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1212,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: styles.productScroll,\n        className: \"product-scroll\",\n        children: getCurrentProducts().map((product, index) => {\n          // Simple null check only\n          if (!product) return null;\n          const isSelected = (typeof selectedProduct === 'object' ? selectedProduct === null || selectedProduct === void 0 ? void 0 : selectedProduct.path : selectedProduct) === product.path;\n          return /*#__PURE__*/_jsxDEV(\"button\", {\n            style: {\n              ...styles.productItem,\n              borderColor: isSelected ? '#2D8C88' : '#e9ecef',\n              backgroundColor: isSelected ? '#f0fffe' : '#ffffff'\n            },\n            title: `${product.name} - ${product.caseDiameter || 'N/A'}mm`,\n            onClick: () => handleProductSelect(product),\n            \"aria-label\": `Select ${product.name} ${product.caseDiameter || 'N/A'}mm`,\n            children: [/*#__PURE__*/_jsxDEV(\"img\", {\n              src: product.path,\n              alt: product.name,\n              style: styles.productImage,\n              onError: e => {\n                e.target.parentElement.style.display = 'none';\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1251,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: styles.productLabel,\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                style: styles.productName,\n                children: product.name\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1260,\n                columnNumber: 21\n              }, this), activeTab === 'Watches' && product.caseDiameter && /*#__PURE__*/_jsxDEV(\"div\", {\n                style: styles.productSize,\n                children: [product.caseDiameter, \"mm\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1262,\n                columnNumber: 23\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1259,\n              columnNumber: 19\n            }, this)]\n          }, index, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1240,\n            columnNumber: 17\n          }, this);\n        })\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1232,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1191,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 778,\n    columnNumber: 5\n  }, this);\n};\n\n// Styles object - Clean, modern design with improved mobile responsiveness\n_s(Tryon, \"EQyd90FOSd6ufQjNdFvw/BEwAfU=\");\n_c = Tryon;\nconst styles = {\n  container: {\n    position: 'relative',\n    height: 'calc(var(--vh, 1vh) * 100)',\n    display: 'flex',\n    flexDirection: 'column',\n    backgroundColor: '#f8f9fa',\n    color: '#333',\n    fontFamily: \"'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif\",\n    overflow: 'hidden',\n    touchAction: 'manipulation',\n    WebkitTapHighlightColor: 'transparent',\n    WebkitOverflowScrolling: 'touch' // Smooth scrolling on iOS\n  },\n  cameraContainer: {\n    flex: 1,\n    position: 'relative',\n    overflow: 'hidden',\n    backgroundColor: '#000',\n    display: 'flex',\n    alignItems: 'center',\n    justifyContent: 'center'\n  },\n  cameraFeed: {\n    width: '100%',\n    height: '100%',\n    objectFit: 'cover',\n    transform: 'scaleX(1)' // Fix for Safari mirroring\n  },\n  capturedImage: {\n    position: 'absolute',\n    top: 0,\n    left: 0,\n    width: '100%',\n    height: '100%',\n    objectFit: 'cover',\n    display: 'none',\n    WebkitTransform: 'scaleX(1)' // Fix for Safari mirroring\n  },\n  homeBtn: {\n    position: 'absolute',\n    top: '20px',\n    left: '20px',\n    backgroundColor: 'rgba(0, 0, 0, 0.6)',\n    color: 'white',\n    padding: '12px',\n    borderRadius: '50%',\n    fontSize: '20px',\n    fontWeight: '700',\n    cursor: 'pointer',\n    zIndex: 10,\n    border: 'none',\n    boxShadow: '0 2px 10px rgba(0, 0, 0, 0.3)',\n    transition: 'all 0.2s ease',\n    display: 'flex',\n    alignItems: 'center',\n    justifyContent: 'center',\n    width: '44px',\n    height: '44px',\n    outline: 'none'\n  },\n  backBtn: {\n    position: 'absolute',\n    top: '20px',\n    left: '20px',\n    backgroundColor: 'rgba(0, 0, 0, 0.6)',\n    color: 'white',\n    padding: '12px',\n    borderRadius: '50%',\n    fontSize: '20px',\n    fontWeight: '700',\n    cursor: 'pointer',\n    zIndex: 10,\n    border: 'none',\n    boxShadow: '0 2px 10px rgba(0, 0, 0, 0.3)',\n    transition: 'all 0.2s ease',\n    display: 'flex',\n    alignItems: 'center',\n    justifyContent: 'center',\n    width: '44px',\n    height: '44px',\n    outline: 'none'\n  },\n  switchContainer: {\n    position: 'absolute',\n    top: '20px',\n    right: '20px',\n    zIndex: 10,\n    display: 'flex',\n    flexDirection: 'column',\n    alignItems: 'center',\n    gap: '4px',\n    padding: '12px',\n    backgroundColor: 'rgba(0, 0, 0, 0.5)',\n    borderRadius: '20px',\n    backdropFilter: 'blur(10px)',\n    WebkitBackdropFilter: 'blur(10px)',\n    boxShadow: '0 4px 12px rgba(0, 0, 0, 0.2)',\n    border: '1px solid rgba(255, 255, 255, 0.1)'\n  },\n  switchTrack: {\n    position: 'absolute',\n    top: '12px',\n    left: '12px',\n    width: '60px',\n    height: '32px',\n    backgroundColor: 'rgba(0, 0, 0, 0.3)',\n    borderRadius: '16px',\n    border: '2px solid rgba(255, 255, 255, 0.2)',\n    zIndex: 9,\n    transition: 'all 0.3s ease'\n  },\n  switchButton: {\n    position: 'relative',\n    width: '28px',\n    height: '28px',\n    borderRadius: '50%',\n    border: 'none',\n    cursor: 'pointer',\n    transition: 'all 0.3s ease',\n    display: 'flex',\n    alignItems: 'center',\n    justifyContent: 'center',\n    zIndex: 11,\n    margin: '2px',\n    boxShadow: '0 2px 8px rgba(0, 0, 0, 0.3)',\n    outline: 'none',\n    '&:hover': {\n      transform: 'scale(1.1)'\n    }\n  },\n  switchLabel: {\n    fontSize: '12px',\n    fontWeight: '700',\n    color: 'white',\n    textShadow: '0 1px 2px rgba(0, 0, 0, 0.7)',\n    marginTop: '4px',\n    padding: '4px 12px',\n    borderRadius: '12px',\n    backgroundColor: 'rgba(0, 0, 0, 0.3)',\n    letterSpacing: '0.5px'\n  },\n  countdownDisplay: {\n    position: 'absolute',\n    top: '35%',\n    left: '50%',\n    transform: 'translate(-50%, -50%)',\n    zIndex: 15,\n    textAlign: 'center',\n    pointerEvents: 'none',\n    padding: '16px 24px',\n    backgroundColor: 'rgba(0, 0, 0, 0.4)',\n    borderRadius: '20px',\n    backdropFilter: 'blur(10px)',\n    WebkitBackdropFilter: 'blur(10px)',\n    boxShadow: '0 4px 20px rgba(0, 0, 0, 0.3)'\n  },\n  countdownNumber: {\n    fontSize: '72px',\n    fontWeight: '900',\n    color: '#2D8C88',\n    textShadow: '0 2px 8px rgba(0, 0, 0, 0.5)',\n    marginBottom: '8px',\n    animation: 'pulse 1s ease-in-out'\n  },\n  countdownText: {\n    fontSize: '16px',\n    fontWeight: '600',\n    color: 'white',\n    textShadow: '0 1px 4px rgba(0, 0, 0, 0.7)'\n  },\n  statusMessage: {\n    position: 'absolute',\n    top: '25%',\n    left: '50%',\n    transform: 'translate(-50%, -50%)',\n    zIndex: 15,\n    textAlign: 'center',\n    pointerEvents: 'none',\n    padding: '12px 20px',\n    borderRadius: '16px',\n    backgroundColor: 'rgba(0, 0, 0, 0.4)',\n    backdropFilter: 'blur(10px)',\n    WebkitBackdropFilter: 'blur(10px)',\n    boxShadow: '0 4px 20px rgba(0, 0, 0, 0.3)'\n  },\n  statusText: {\n    fontSize: '16px',\n    fontWeight: '600',\n    color: 'white',\n    textShadow: '0 1px 4px rgba(0, 0, 0, 0.7)',\n    backgroundColor: 'rgba(255, 107, 107, 0.9)',\n    padding: '12px 20px',\n    borderRadius: '25px',\n    marginBottom: '8px',\n    transition: 'all 0.3s ease'\n  },\n  statusSubtext: {\n    fontSize: '12px',\n    fontWeight: '500',\n    color: 'white',\n    textShadow: '0 1px 4px rgba(0, 0, 0, 0.7)',\n    backgroundColor: 'rgba(0, 0, 0, 0.6)',\n    padding: '6px 12px',\n    borderRadius: '15px'\n  },\n  handGuide: {\n    position: 'absolute',\n    top: '50%',\n    left: '50%',\n    transform: 'translate(-50%, -50%)',\n    width: '80%',\n    maxWidth: '400px',\n    height: 'auto',\n    opacity: 0.8,\n    pointerEvents: 'none',\n    zIndex: 5,\n    filter: 'drop-shadow(0 2px 8px rgba(255, 255, 255, 0.3))',\n    WebkitFilter: 'drop-shadow(0 2px 8px rgba(255, 255, 255, 0.3))',\n    transition: 'all 0.3s ease'\n  },\n  productPosition: {\n    position: 'absolute',\n    top: '50%',\n    left: '50%',\n    transform: 'translate(-50%, -50%)',\n    zIndex: 8,\n    display: 'flex',\n    alignItems: 'center',\n    justifyContent: 'center',\n    pointerEvents: 'none',\n    minWidth: '100px',\n    // Ensure minimum size\n    minHeight: '100px' // Ensure minimum size\n  },\n  captureBtn: {\n    position: 'absolute',\n    bottom: '30px',\n    left: '50%',\n    transform: 'translateX(-50%)',\n    width: '80px',\n    height: '80px',\n    backgroundColor: 'rgba(255, 255, 255, 0.9)',\n    borderRadius: '50%',\n    display: 'flex',\n    alignItems: 'center',\n    justifyContent: 'center',\n    cursor: 'pointer',\n    zIndex: 10,\n    transition: 'all 0.2s ease',\n    border: '4px solid rgba(255, 255, 255, 0.3)',\n    boxShadow: '0 4px 20px rgba(0, 0, 0, 0.3)',\n    outline: 'none',\n    padding: 0,\n    WebkitTapHighlightColor: 'transparent',\n    touchAction: 'manipulation'\n  },\n  captureInner: {\n    width: '60px',\n    height: '60px',\n    backgroundColor: '#2D8C88',\n    borderRadius: '50%',\n    transition: 'all 0.2s ease'\n  },\n  resetBtn: {\n    position: 'absolute',\n    bottom: '30px',\n    right: '30px',\n    width: '50px',\n    height: '50px',\n    backgroundColor: 'rgba(0, 0, 0, 0.6)',\n    borderRadius: '50%',\n    display: 'flex',\n    alignItems: 'center',\n    justifyContent: 'center',\n    cursor: 'pointer',\n    zIndex: 10,\n    transition: 'all 0.2s ease',\n    border: 'none',\n    boxShadow: '0 2px 10px rgba(0, 0, 0, 0.3)',\n    outline: 'none',\n    padding: 0\n  },\n  // Wrist Size Floating Button - Follows CSS mobile patterns\n  wristSizeFloatingBtn: {\n    position: 'absolute',\n    top: '20px',\n    right: '20px',\n    backgroundColor: '#2D8C88',\n    color: 'white',\n    padding: '12px 16px',\n    borderRadius: '24px',\n    fontSize: '14px',\n    fontWeight: '600',\n    cursor: 'pointer',\n    border: 'none',\n    display: 'flex',\n    alignItems: 'center',\n    gap: '8px',\n    outline: 'none',\n    transition: 'all 0.2s ease',\n    zIndex: 15,\n    boxShadow: '0 2px 8px rgba(45, 140, 136, 0.3)',\n    minHeight: '48px',\n    minWidth: '48px',\n    WebkitTapHighlightColor: 'transparent',\n    touchAction: 'manipulation'\n  },\n  wristSizeText: {\n    fontSize: '12px',\n    fontWeight: '600'\n  },\n  // Modal Styles\n  modalOverlay: {\n    position: 'fixed',\n    top: 0,\n    left: 0,\n    right: 0,\n    bottom: 0,\n    backgroundColor: 'rgba(0, 0, 0, 0.5)',\n    display: 'flex',\n    alignItems: 'center',\n    justifyContent: 'center',\n    zIndex: 50,\n    padding: '20px',\n    backdropFilter: 'blur(5px)',\n    WebkitBackdropFilter: 'blur(5px)',\n    touchAction: 'none'\n  },\n  wristSizeModal: {\n    backgroundColor: 'white',\n    borderRadius: '24px',\n    width: '100%',\n    maxWidth: '95vw',\n    maxHeight: '80vh',\n    overflow: 'hidden',\n    boxShadow: '0 10px 30px rgba(0, 0, 0, 0.3)',\n    display: 'flex',\n    flexDirection: 'column',\n    margin: '10px',\n    position: 'relative'\n  },\n  modalHeader: {\n    display: 'flex',\n    alignItems: 'center',\n    justifyContent: 'space-between',\n    padding: '16px 20px 12px 20px',\n    borderBottom: '1px solid #f0f0f0'\n  },\n  modalTitle: {\n    fontSize: '18px',\n    fontWeight: '700',\n    color: '#2D8C88',\n    margin: 0\n  },\n  modalCloseBtn: {\n    width: '32px',\n    height: '32px',\n    borderRadius: '50%',\n    border: 'none',\n    backgroundColor: '#f5f5f5',\n    color: '#666',\n    cursor: 'pointer',\n    display: 'flex',\n    alignItems: 'center',\n    justifyContent: 'center',\n    transition: 'all 0.2s ease',\n    outline: 'none'\n  },\n  modalContent: {\n    padding: '16px 20px 20px 20px',\n    display: 'flex',\n    flexDirection: 'column',\n    gap: '16px',\n    overflowY: 'auto',\n    WebkitOverflowScrolling: 'touch',\n    maxHeight: 'calc(80vh - 60px)'\n  },\n  // Mobile styles handled by CSS file\n  wristSizeContent: {\n    display: 'flex',\n    flexDirection: 'column',\n    alignItems: 'center',\n    gap: '12px'\n  },\n  wristSizeTitle: {\n    fontSize: '20px',\n    fontWeight: '700',\n    color: '#2D8C88',\n    margin: 0,\n    textAlign: 'center'\n  },\n  wristSizeSubtitle: {\n    fontSize: '14px',\n    color: '#666',\n    margin: 0,\n    textAlign: 'center',\n    lineHeight: '1.4'\n  },\n  genderSelection: {\n    display: 'flex',\n    gap: '12px',\n    width: '100%',\n    maxWidth: '300px'\n  },\n  genderButton: {\n    flex: 1,\n    padding: '12px 16px',\n    borderRadius: '10px',\n    fontSize: '14px',\n    fontWeight: '600',\n    cursor: 'pointer',\n    transition: 'all 0.2s ease',\n    border: '2px solid #e0e0e0',\n    backgroundColor: '#ffffff',\n    color: '#666',\n    outline: 'none'\n  },\n  genderButtonActive: {\n    backgroundColor: '#2D8C88',\n    color: '#ffffff',\n    borderColor: '#2D8C88',\n    boxShadow: '0 2px 8px rgba(45, 140, 136, 0.3)'\n  },\n  sliderContainer: {\n    width: '100%',\n    maxWidth: '300px',\n    display: 'flex',\n    flexDirection: 'column',\n    gap: '8px'\n  },\n  sliderLabel: {\n    fontSize: '16px',\n    fontWeight: '600',\n    color: '#333',\n    textAlign: 'center',\n    display: 'flex',\n    alignItems: 'center',\n    justifyContent: 'center',\n    gap: '8px'\n  },\n  sizeChange: {\n    fontSize: '14px',\n    fontWeight: '500',\n    color: '#2D8C88',\n    backgroundColor: 'rgba(45, 140, 136, 0.1)',\n    padding: '2px 8px',\n    borderRadius: '12px'\n  },\n  slider: {\n    width: '100%',\n    height: '6px',\n    borderRadius: '3px',\n    background: '#e0e0e0',\n    outline: 'none',\n    cursor: 'pointer',\n    WebkitAppearance: 'none',\n    appearance: 'none'\n  },\n  sliderLabels: {\n    display: 'flex',\n    justifyContent: 'space-between',\n    fontSize: '12px',\n    color: '#999',\n    marginTop: '4px'\n  },\n  presetButtons: {\n    display: 'flex',\n    gap: '8px',\n    width: '100%',\n    maxWidth: '300px'\n  },\n  presetButton: {\n    flex: 1,\n    padding: '8px 12px',\n    borderRadius: '8px',\n    fontSize: '12px',\n    fontWeight: '500',\n    cursor: 'pointer',\n    transition: 'all 0.2s ease',\n    border: '1px solid #e0e0e0',\n    backgroundColor: '#ffffff',\n    color: '#666',\n    outline: 'none'\n  },\n  continueButton: {\n    width: '100%',\n    maxWidth: '300px',\n    padding: '14px 24px',\n    backgroundColor: '#2D8C88',\n    color: '#ffffff',\n    borderRadius: '12px',\n    fontSize: '16px',\n    fontWeight: '600',\n    cursor: 'pointer',\n    transition: 'all 0.2s ease',\n    border: 'none',\n    outline: 'none',\n    boxShadow: '0 4px 12px rgba(45, 140, 136, 0.3)'\n  },\n  productSelection: {\n    position: 'absolute',\n    bottom: 0,\n    left: 0,\n    right: 0,\n    backgroundColor: 'rgba(255, 255, 255, 0.98)',\n    backdropFilter: 'blur(20px)',\n    WebkitBackdropFilter: 'blur(20px)',\n    borderTopLeftRadius: '24px',\n    borderTopRightRadius: '24px',\n    padding: '16px',\n    maxHeight: '85vh',\n    display: 'flex',\n    flexDirection: 'column',\n    zIndex: 20,\n    boxShadow: '0 -4px 20px rgba(0, 0, 0, 0.15)',\n    border: 'none',\n    transform: 'translateY(0)',\n    transition: 'transform 0.3s ease-out',\n    overflow: 'hidden',\n    touchAction: 'none',\n    willChange: 'transform',\n    userSelect: 'none',\n    WebkitUserSelect: 'none'\n  },\n  closeBtn: {\n    position: 'absolute',\n    top: '12px',\n    right: '16px',\n    color: '#666',\n    cursor: 'pointer',\n    zIndex: 21,\n    width: '32px',\n    height: '32px',\n    display: 'flex',\n    alignItems: 'center',\n    justifyContent: 'center',\n    borderRadius: '50%',\n    backgroundColor: 'rgba(0, 0, 0, 0.1)',\n    transition: 'all 0.2s ease',\n    border: 'none',\n    outline: 'none',\n    padding: 0\n  },\n  productTabs: {\n    display: 'flex',\n    marginBottom: '16px',\n    backgroundColor: '#f0f0f0',\n    borderRadius: '12px',\n    padding: '4px',\n    gap: '4px'\n  },\n  tab: {\n    flex: 1,\n    textAlign: 'center',\n    padding: '12px 16px',\n    borderRadius: '8px',\n    fontSize: '16px',\n    fontWeight: '600',\n    cursor: 'pointer',\n    transition: 'all 0.2s ease',\n    color: '#666',\n    outline: 'none',\n    border: 'none',\n    backgroundColor: 'transparent',\n    WebkitTapHighlightColor: 'transparent',\n    touchAction: 'manipulation'\n  },\n  activeTab: {\n    backgroundColor: '#2D8C88',\n    color: '#ffffff',\n    boxShadow: '0 1px 4px rgba(45, 140, 136, 0.3)'\n  },\n  productScroll: {\n    display: 'grid',\n    gridTemplateColumns: 'repeat(auto-fill, minmax(100px, 1fr))',\n    gap: '12px',\n    maxHeight: 'calc(85vh - 120px)',\n    overflowY: 'auto',\n    paddingBottom: '16px',\n    scrollbarWidth: 'thin',\n    scrollbarColor: '#ddd #f5f5f5',\n    WebkitOverflowScrolling: 'touch'\n  },\n  productItem: {\n    position: 'relative',\n    width: '100%',\n    aspectRatio: '1/1',\n    backgroundColor: '#ffffff',\n    borderRadius: '16px',\n    display: 'flex',\n    alignItems: 'center',\n    justifyContent: 'center',\n    cursor: 'pointer',\n    transition: 'all 0.2s ease',\n    border: '1px solid #e0e0e0',\n    overflow: 'hidden',\n    boxShadow: '0 2px 8px rgba(0, 0, 0, 0.08)',\n    padding: '8px',\n    outline: 'none',\n    WebkitTapHighlightColor: 'transparent',\n    touchAction: 'manipulation'\n  },\n  productImage: {\n    width: '100%',\n    height: '100%',\n    objectFit: 'contain',\n    borderRadius: '8px',\n    backgroundColor: '#ffffff'\n  },\n  productLabel: {\n    position: 'absolute',\n    bottom: '3px',\n    left: '3px',\n    right: '3px',\n    fontSize: '9px',\n    color: '#666',\n    textAlign: 'center',\n    backgroundColor: 'rgba(255, 255, 255, 0.95)',\n    borderRadius: '3px',\n    padding: '3px 2px',\n    overflow: 'hidden'\n  },\n  productName: {\n    fontSize: '9px',\n    fontWeight: '600',\n    whiteSpace: 'nowrap',\n    textOverflow: 'ellipsis',\n    overflow: 'hidden',\n    marginBottom: '1px'\n  },\n  productSize: {\n    fontSize: '8px',\n    fontWeight: '500',\n    color: '#2D8C88',\n    whiteSpace: 'nowrap'\n  },\n  dragHandle: {\n    width: '40px',\n    height: '4px',\n    backgroundColor: '#e0e0e0',\n    borderRadius: '2px',\n    margin: '0 auto 12px',\n    cursor: 'grab',\n    touchAction: 'none',\n    userSelect: 'none',\n    WebkitUserSelect: 'none'\n  }\n};\nexport default Tryon;\nvar _c;\n$RefreshReg$(_c, \"Tryon\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useRef", "motion", "ArrowLeft", "Settings", "RotateCcw", "Home", "Eye", "Camera", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "<PERSON><PERSON>", "onBackToHome", "_s", "videoRef", "capturedImageRef", "canvasRef", "isCaptured", "setIsCaptured", "selectedProduct", "setSelectedProduct", "isRightHand", "setIsRightHand", "showProductSelection", "setShowProductSelection", "activeTab", "setActiveTab", "showHandGuide", "setShowHandGuide", "isMobile", "setIsMobile", "userGender", "setUserGender", "userWristSize", "setUserWristSize", "showWristSizeModal", "setShowWristSizeModal", "isAutoCaptureEnabled", "setIsAutoCaptureEnabled", "countdown", "setCountdown", "isHandInPosition", "setIsHandInPosition", "isCountdownActive", "setIsCountdownActive", "panelPosition", "setPanelPosition", "isDragging", "setIsDragging", "startY", "setStartY", "panelRef", "checkMobile", "window", "innerWidth", "setVH", "vh", "innerHeight", "document", "documentElement", "style", "setProperty", "addEventListener", "setTimeout", "removeEventListener", "DEFAULT_WRIST_SIZES", "men", "women", "SVG_WRIST_CIRCLE_DIAMETER", "SVG_VIEWBOX_WIDTH", "SVG_VIEWBOX_HEIGHT", "WATCH_WIDTH", "BRACELET_WIDTH", "WATCH_HEIGHT", "BRACELET_HEIGHT", "calculateWatchDimensions", "watch", "containerWidth", "containerHeight", "defaultWristSize", "wristSizeRatio", "mmToSvgScale", "watchWidthSvg", "totalWidth", "watchHeightSvg", "totalHeight", "dialDiameterSvg", "dialDiameter", "watchWidthPercent", "watchHeightPercent", "dialDiameterPercent", "positionX", "positionY", "width", "Math", "max", "height", "scale", "min", "realWidth", "realHeight", "caseDiameter", "getWatchPosition", "watchData", "baseDimensions", "adjustedX", "adjustedY", "type", "watches", "name", "path", "caseThickness", "dialSize", "bracelets", "initCamera", "stream", "navigator", "mediaDevices", "getUserMedia", "video", "facingMode", "ideal", "current", "srcObject", "err", "console", "error", "src", "display", "log", "<PERSON><PERSON><PERSON><PERSON>", "canvas", "videoWidth", "videoHeight", "ctx", "getContext", "drawImage", "toDataURL", "removeBackground", "imgElement", "productType", "img", "Image", "crossOrigin", "onload", "createElement", "naturalWidth", "naturalHeight", "imageData", "getImageData", "data", "edgePixels", "Set", "y", "x", "idx", "r", "g", "b", "isEdge", "dy", "dx", "neighborIdx", "nr", "ng", "nb", "colorDiff", "abs", "add", "i", "length", "pixelIndex", "brightness", "isNearEdge", "has", "isPureWhite", "putImageData", "filter", "mixBlendMode", "opacity", "e", "warn", "onerror", "detectHandOrientation", "random", "detectHandInPosition", "videoContainer", "parentElement", "containerRect", "getBoundingClientRect", "videoAspect", "containerAspect", "displayWidth", "displayHeight", "offsetX", "offsetY", "scaleX", "scaleY", "rectX", "rectY", "rectWidth", "rectHeight", "circleX", "circleY", "circleWidth", "circleHeight", "rectImageData", "rectData", "circleImageData", "circleData", "rectSkinPixels", "rectTotalPixels", "circleSkinPixels", "circleTotalPixels", "isSkinTone", "condition1", "condition2", "condition3", "condition4", "rectSkinRatio", "circleSkinRatio", "wristInPosition", "handInPosition", "applyProductToWatchPosition", "productPath", "find", "w", "handleCapture", "capturedDataUrl", "handleBack", "handleGenderChange", "gender", "handleWristSizeChange", "size", "handleTabChange", "tabName", "handleProductSelect", "product", "interval", "setInterval", "clearInterval", "countdownInterval", "prev", "handleAutoCaptureToggle", "newState", "handleBackWithReset", "getCurrentProducts", "handleTouchStart", "touches", "clientY", "handleTouchMove", "currentY", "diff", "handleTouchEnd", "handleClickOutside", "target", "closest", "className", "children", "ref", "autoPlay", "playsInline", "muted", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "alt", "onClick", "checked", "onChange", "disabled", "viewBox", "points", "fill", "stroke", "strokeWidth", "strokeLinecap", "strokeLinejoin", "x1", "y1", "x2", "y2", "styles", "handGuide", "xmlns", "d", "rx", "cx", "cy", "textAnchor", "fontSize", "fontWeight", "productPosition", "position", "alignItems", "justifyContent", "objectFit", "transform", "onLoad", "bottom", "left", "color", "backgroundColor", "padding", "borderRadius", "whiteSpace", "pointerEvents", "boxShadow", "zIndex", "marginLeft", "toFixed", "captureBtn", "captureInner", "resetBtn", "location", "reload", "wristSizeFloatingBtn", "wristSizeText", "modalOverlay", "wristSizeModal", "stopPropagation", "modalHeader", "modalTitle", "modalCloseBtn", "modalContent", "genderSelection", "genderButton", "genderButtonActive", "slide<PERSON><PERSON><PERSON><PERSON>", "slider<PERSON><PERSON><PERSON>", "sizeChange", "value", "parseInt", "slider", "slider<PERSON><PERSON><PERSON>", "presetButtons", "presetButton", "productSelection", "touchAction", "role", "onTouchStart", "onTouchMove", "onTouchEnd", "dragHandle", "productTabs", "tab", "productScroll", "map", "index", "isSelected", "productItem", "borderColor", "title", "productImage", "onError", "productLabel", "productName", "productSize", "_c", "container", "flexDirection", "fontFamily", "overflow", "WebkitTapHighlightColor", "WebkitOverflowScrolling", "cameraContainer", "flex", "cameraFeed", "capturedImage", "top", "WebkitTransform", "homeBtn", "cursor", "border", "transition", "outline", "backBtn", "switchContainer", "right", "gap", "<PERSON><PERSON>ilter", "WebkitBackdropFilter", "switchTrack", "switchButton", "margin", "switchLabel", "textShadow", "marginTop", "letterSpacing", "countdownDisplay", "textAlign", "countdownNumber", "marginBottom", "animation", "countdownText", "statusMessage", "statusText", "statusSubtext", "max<PERSON><PERSON><PERSON>", "WebkitFilter", "min<PERSON><PERSON><PERSON>", "minHeight", "maxHeight", "borderBottom", "overflowY", "wristSizeContent", "wristSizeTitle", "wristSizeSubtitle", "lineHeight", "background", "WebkitAppearance", "appearance", "continueButton", "borderTopLeftRadius", "borderTopRightRadius", "<PERSON><PERSON><PERSON><PERSON>", "userSelect", "WebkitUserSelect", "closeBtn", "gridTemplateColumns", "paddingBottom", "scrollbarWidth", "scrollbarColor", "aspectRatio", "textOverflow", "$RefreshReg$"], "sources": ["D:/Via/New folder/v2/src/pages/VirtualTryOn.jsx"], "sourcesContent": ["import React, { useState, useEffect, useRef } from 'react';\nimport { motion } from 'framer-motion';\nimport { ArrowLeft, Settings, RotateCcw, Home, Eye, Camera } from 'lucide-react';\n\nconst Tryon = ({ onBackToHome }) => {\n  // Refs for DOM elements\n  const videoRef = useRef(null);\n  const capturedImageRef = useRef(null);\n  const canvasRef = useRef(null);\n\n  // State variables\n  const [isCaptured, setIsCaptured] = useState(false);\n  const [selectedProduct, setSelectedProduct] = useState(null);\n  const [isRightHand, setIsRightHand] = useState(false);\n  const [showProductSelection, setShowProductSelection] = useState(false);\n  const [activeTab, setActiveTab] = useState('Watches');\n  const [showHandGuide, setShowHandGuide] = useState(true);\n  const [isMobile, setIsMobile] = useState(false);\n\n  // Watch size customization state\n  const [userGender, setUserGender] = useState('men'); // 'men' or 'women'\n  const [userWristSize, setUserWristSize] = useState(64); // Default men's wrist size in mm\n  const [showWristSizeModal, setShowWristSizeModal] = useState(false); // Mobile-friendly modal\n\n  // Autocapture state variables\n  const [isAutoCaptureEnabled, setIsAutoCaptureEnabled] = useState(false);\n  const [countdown, setCountdown] = useState(0);\n  const [isHandInPosition, setIsHandInPosition] = useState(false);\n  const [isCountdownActive, setIsCountdownActive] = useState(false);\n\n  // Add new state for panel position\n  const [panelPosition, setPanelPosition] = useState(0);\n  const [isDragging, setIsDragging] = useState(false);\n  const [startY, setStartY] = useState(0);\n  const panelRef = useRef(null);\n\n  // Detect mobile device and set viewport height for Chrome mobile\n  useEffect(() => {\n    const checkMobile = () => {\n      setIsMobile(window.innerWidth <= 768);\n    };\n\n    // Fix viewport height for Chrome mobile\n    const setVH = () => {\n      const vh = window.innerHeight * 0.01;\n      document.documentElement.style.setProperty('--vh', `${vh}px`);\n    };\n\n    checkMobile();\n    setVH();\n\n    window.addEventListener('resize', () => {\n      checkMobile();\n      setVH();\n    });\n\n    window.addEventListener('orientationchange', () => {\n      setTimeout(() => {\n        setVH();\n      }, 100);\n    });\n\n    return () => {\n      window.removeEventListener('resize', checkMobile);\n      window.removeEventListener('orientationchange', setVH);\n    };\n  }, []);\n\n  // Realistic sizing configuration\n  // Average adult wrist circumference: 165mm (men), 155mm (women)\n  // Average wrist width when viewed from top: ~55-65mm\n  // SVG guide circle represents the wrist area (120px diameter in 800px viewBox)\n  // This means the circle represents approximately 60mm in real life\n\n  // Default wrist sizes by gender\n  const DEFAULT_WRIST_SIZES = {\n    men: 64,    // mm - default men's wrist size\n    women: 54   // mm - default women's wrist size\n  };\n\n  const SVG_WRIST_CIRCLE_DIAMETER = 120; // SVG circle diameter in viewBox units\n  const SVG_VIEWBOX_WIDTH = 800; // SVG viewBox width\n  const SVG_VIEWBOX_HEIGHT = 600; // SVG viewBox height\n\n  // Size configuration - Increased for better visibility\n  const WATCH_WIDTH = 25; // percentage of container width\n  const BRACELET_WIDTH = 15; // percentage of container width\n  const WATCH_HEIGHT = 38; // percentage of container height\n  const BRACELET_HEIGHT = 55; // percentage of container height - INCREASED for better visibility\n\n  // Test: Try changing WATCH_HEIGHT to 50 to see a much taller watch\n  // Test: Try changing WATCH_HEIGHT to 15 to see a shorter watch\n  // Test: Try changing BRACELET_HEIGHT to 70 to see a much taller bracelet\n  // Test: Try changing BRACELET_HEIGHT to 20 to see a shorter bracelet\n  // Note: Scale is now dynamic - smaller height = smaller scale, larger height = larger scale\n\n  // Calculate realistic watch sizing based on actual dimensions and user's wrist size\n  const calculateWatchDimensions = (watch, containerWidth, containerHeight) => {\n    // Get the default wrist size for the current gender\n    const defaultWristSize = DEFAULT_WRIST_SIZES[userGender];\n\n    // Calculate relative scaling factor based on user's wrist size vs default\n    const wristSizeRatio = userWristSize / defaultWristSize;\n\n    // Calculate the scale factor from real world to SVG coordinates using user's wrist size\n    const mmToSvgScale = SVG_WRIST_CIRCLE_DIAMETER / userWristSize;\n\n    // Convert watch dimensions to SVG units\n    const watchWidthSvg = watch.totalWidth * mmToSvgScale;\n    const watchHeightSvg = watch.totalHeight * mmToSvgScale;\n    const dialDiameterSvg = watch.dialDiameter * mmToSvgScale;\n\n    // Convert SVG units to container percentages\n    const watchWidthPercent = (watchWidthSvg / SVG_VIEWBOX_WIDTH) * 100;\n    const watchHeightPercent = (watchHeightSvg / SVG_VIEWBOX_HEIGHT) * 100;\n    const dialDiameterPercent = (dialDiameterSvg / SVG_VIEWBOX_WIDTH) * 100;\n\n    // Calculate positioning - watches should be positioned on the wrist circle\n    // SVG circle is at cx=\"400\" cy=\"300\" (center of the hand area)\n    const positionX = 50; // Center horizontally (400/800 = 50%)\n    const positionY = 50; // Center vertically (300/600 = 50%)\n\n    return {\n      width: Math.max(watchWidthPercent, 8), // Minimum 8% for visibility\n      height: Math.max(watchHeightPercent, 10), // Minimum 10% for visibility\n      dialDiameter: dialDiameterPercent,\n      positionX,\n      positionY,\n      scale: Math.min(watchWidthPercent / 15, watchHeightPercent / 20) * wristSizeRatio, // Dynamic scale with wrist size adjustment\n      realWidth: watch.totalWidth,\n      realHeight: watch.totalHeight,\n      caseDiameter: watch.caseDiameter,\n      wristSizeRatio // Include ratio for additional scaling if needed\n    };\n  };\n\n  // Calculate watch position based on hand orientation and anatomy\n  const getWatchPosition = (watchData, isRightHand) => {\n    const baseDimensions = calculateWatchDimensions(watchData, 400, 600);\n\n    // Adjust position based on hand orientation\n    // Watches are typically worn on the top of the wrist\n    let adjustedX = baseDimensions.positionX;\n    let adjustedY = baseDimensions.positionY - 2; // Slightly higher on the wrist\n\n    // For right hand, watch might be positioned slightly differently\n    if (isRightHand) {\n      adjustedX = baseDimensions.positionX + 1; // Slight adjustment for right hand\n    }\n\n    // Adjust for different watch types\n    switch (watchData.type) {\n      case 'smartwatch':\n        // Smart watches are often worn higher on the wrist\n        adjustedY -= 1;\n        break;\n      case 'luxury':\n        // Luxury watches might be positioned more precisely\n        adjustedY -= 0.5;\n        break;\n      case 'sport':\n        // Sport watches might be worn slightly looser\n        adjustedY += 0.5;\n        break;\n      default:\n        break;\n    }\n\n    return {\n      ...baseDimensions,\n      positionX: adjustedX,\n      positionY: adjustedY\n    };\n  };\n  // Realistic watch data with actual dimensions (in millimeters)\n  const watches = [\n    {\n      name: \"Classic Black\",\n      path: \"imgs/watches/watch_1.png\",\n      // Rolex Submariner style - 40mm case\n      caseDiameter: 41, // mm\n      caseThickness: 12.5, // mm\n      totalWidth: 42, // mm (including crown)\n      totalHeight: 47, // mm (lug to lug)\n      dialDiameter: 31, // mm (visible dial)\n      type: \"dress\",\n      dialSize: 40\n    },\n    {\n      name: \"Silver Chrono\",\n      path: \"imgs/watches/watch_2.png\",\n      // Omega Speedmaster style - 42mm case\n      caseDiameter: 42, // mm\n      caseThickness: 13.2, // mm\n      totalWidth: 44, // mm\n      totalHeight: 48.5, // mm\n      dialDiameter: 33, // mm\n      type: \"sport\",\n      dialSize: 42\n    },\n    {\n      name: \"Gold Luxury\",\n      path: \"imgs/watches/watch_3.png\",\n      // Patek Philippe Calatrava style - 38mm case\n      caseDiameter: 39, // mm\n      caseThickness: 8.5, // mm\n      totalWidth: 39, // mm\n      totalHeight: 45, // mm\n      dialDiameter: 30, // mm\n      type: \"luxury\",\n      dialSize: 38\n    },\n    {\n      name: \"Sport Blue\",\n      path: \"imgs/watches/watch_6.png\",\n      // Apple Watch style - 44mm case\n      caseDiameter: 41, // mm (width)\n      caseThickness: 10.7, // mm\n      totalWidth: 44, // mm\n      totalHeight: 38, // mm (height - rectangular)\n      dialDiameter: 35, // mm (screen diagonal)\n      type: \"smartwatch\",\n      dialSize: 44\n    },\n    {\n      name: \"Minimalist\",\n      path: \"imgs/watches/watch_5.png\",\n      // Daniel Wellington style - 36mm case\n      caseDiameter: 36, // mm\n      caseThickness: 6, // mm\n      totalWidth: 37, // mm\n      totalHeight: 43, // mm\n      dialDiameter: 28, // mm\n      type: \"minimalist\",\n      dialSize: 36\n    },\n    {\n      name: \"Rose Gold\",\n      path: \"imgs/watches/watch_4.png\",\n      // Michael Kors style - 39mm case\n      caseDiameter: 44, // mm\n      caseThickness: 11, // mm\n      totalWidth: 41, // mm\n      totalHeight: 46, // mm\n      dialDiameter: 31, // mm\n      type: \"fashion\",\n      dialSize: 41\n    }\n  ];\n\n  const bracelets = [\n    { name: \"Silver Chain\", path: \"bracelets/bracelet_1.png\" },\n    { name: \"Gold Bangle\", path: \"bracelets/bracelet_2.png\" },\n    { name: \"Leather Wrap\", path: \"bracelets/bracelet_3.png\" },\n    { name: \"Diamond Tennis\", path: \"bracelets/bracelet_4.png\" },\n    { name: \"Beaded Stone\", path: \"bracelets/bracelet_5.png\" },\n    { name: \"Charm Bracelet\", path: \"bracelets/bracelet_6.png\" }\n  ];\n\n  // Initialize camera\n  const initCamera = async () => {\n    try {\n      const stream = await navigator.mediaDevices.getUserMedia({\n        video: {\n          facingMode: 'environment',\n          width: { ideal: 1920 },\n          height: { ideal: 1080 }\n        }\n      });\n      if (videoRef.current) {\n        videoRef.current.srcObject = stream;\n      }\n    } catch (err) {\n      console.error(\"Error accessing camera:\", err);\n      // Demo mode fallback\n      if (capturedImageRef.current) {\n        capturedImageRef.current.src = \"sample-hand.jpg\";\n        capturedImageRef.current.style.display = \"block\";\n      }\n      console.log(\"Camera not available - demo mode\");\n      setIsCaptured(true);\n      setShowProductSelection(true);\n    }\n  };\n\n  // Capture current frame\n  const captureFrame = () => {\n    if (!videoRef.current || !canvasRef.current) return null;\n\n    const canvas = canvasRef.current;\n    const video = videoRef.current;\n    canvas.width = video.videoWidth;\n    canvas.height = video.videoHeight;\n    const ctx = canvas.getContext('2d');\n    ctx.drawImage(video, 0, 0, canvas.width, canvas.height);\n    return canvas.toDataURL('image/png');\n  };\n\n  // Improved background remover that preserves light colors\n  const removeBackground = (imgElement, productType = 'watch') => {\n    const img = new Image();\n    img.crossOrigin = 'anonymous';\n\n    img.onload = function() {\n      const canvas = document.createElement('canvas');\n      const ctx = canvas.getContext('2d');\n      canvas.width = img.naturalWidth;\n      canvas.height = img.naturalHeight;\n      ctx.drawImage(img, 0, 0);\n\n      try {\n        const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);\n        const data = imageData.data;\n        const width = canvas.width;\n        const height = canvas.height;\n\n        // First pass: Identify edge pixels to preserve product boundaries\n        const edgePixels = new Set();\n        for (let y = 1; y < height - 1; y++) {\n          for (let x = 1; x < width - 1; x++) {\n            const idx = (y * width + x) * 4;\n            const r = data[idx];\n            const g = data[idx + 1];\n            const b = data[idx + 2];\n\n            // Check surrounding pixels for edge detection\n            let isEdge = false;\n            for (let dy = -1; dy <= 1; dy++) {\n              for (let dx = -1; dx <= 1; dx++) {\n                if (dx === 0 && dy === 0) continue;\n                const neighborIdx = ((y + dy) * width + (x + dx)) * 4;\n                const nr = data[neighborIdx];\n                const ng = data[neighborIdx + 1];\n                const nb = data[neighborIdx + 2];\n\n                // If there's a significant color difference, it's an edge\n                const colorDiff = Math.abs(r - nr) + Math.abs(g - ng) + Math.abs(b - nb);\n                if (colorDiff > 60) {\n                  isEdge = true;\n                  break;\n                }\n              }\n              if (isEdge) break;\n            }\n\n            if (isEdge) {\n              edgePixels.add(idx / 4);\n            }\n          }\n        }\n\n        // Second pass: Remove only white background while preserving all product colors\n        for (let i = 0; i < data.length; i += 4) {\n          const r = data[i];\n          const g = data[i + 1];\n          const b = data[i + 2];\n          const pixelIndex = i / 4;\n\n          // Calculate color properties\n          const brightness = (r + g + b) / 3;\n\n          // Check if pixel is near edges (preserve product boundaries)\n          const isNearEdge = edgePixels.has(pixelIndex);\n\n          // Only remove pure white or near-white pixels that aren't edges\n          const isPureWhite = (\n            r > 245 &&\n            g > 245 &&\n            b > 245 &&\n            Math.abs(r - g) < 10 &&\n            Math.abs(g - b) < 10 &&\n            !isNearEdge\n          );\n\n          // Remove background if it's pure white and not an edge\n          if (isPureWhite) {\n            data[i + 3] = 0; // Make fully transparent\n          } else if (brightness > 240 && !isNearEdge) {\n            // For very bright but not pure white, reduce opacity slightly\n            data[i + 3] = Math.max(0, data[i + 3] - 50);\n          }\n        }\n\n        ctx.putImageData(imageData, 0, 0);\n        imgElement.src = canvas.toDataURL('image/png');\n\n        // Apply product-specific styling that preserves all colors\n        imgElement.style.filter = 'none';\n        imgElement.style.mixBlendMode = 'normal';\n        imgElement.style.opacity = '1';\n\n      } catch (e) {\n        console.warn('Canvas operation failed:', e);\n        // Fallback styling that preserves all colors\n        imgElement.style.filter = 'none';\n        imgElement.style.mixBlendMode = 'normal';\n        imgElement.style.opacity = '1';\n      }\n    };\n\n    img.onerror = function() {\n      console.warn('Image loading failed');\n      // Fallback styling\n      imgElement.style.filter = 'none';\n      imgElement.style.mixBlendMode = 'normal';\n      imgElement.style.opacity = '1';\n    };\n\n    img.src = imgElement.src;\n  };\n\n  // Placeholder for hand detection\n  const detectHandOrientation = (imageData) => {\n    // Simple heuristic for demo purposes\n    return Math.random() > 0.5;\n  };\n\n  // Detect if arm and wrist are within the SVG guide area\n  const detectHandInPosition = () => {\n    if (!videoRef.current || !canvasRef.current) return false;\n\n    const video = videoRef.current;\n    const canvas = canvasRef.current;\n    const ctx = canvas.getContext('2d');\n\n    // Set canvas size to match video\n    canvas.width = video.videoWidth;\n    canvas.height = video.videoHeight;\n\n    // Draw current video frame\n    ctx.drawImage(video, 0, 0, canvas.width, canvas.height);\n\n    // Get the video container dimensions to calculate the guide area\n    const videoContainer = video.parentElement;\n    const containerRect = videoContainer.getBoundingClientRect();\n\n    // Calculate the actual video display area (accounting for object-fit: cover)\n    const videoAspect = video.videoWidth / video.videoHeight;\n    const containerAspect = containerRect.width / containerRect.height;\n\n    let displayWidth, displayHeight, offsetX, offsetY;\n\n    if (videoAspect > containerAspect) {\n      // Video is wider - height fills container, width is cropped\n      displayHeight = containerRect.height;\n      displayWidth = displayHeight * videoAspect;\n      offsetX = (displayWidth - containerRect.width) / 2;\n      offsetY = 0;\n    } else {\n      // Video is taller - width fills container, height is cropped\n      displayWidth = containerRect.width;\n      displayHeight = displayWidth / videoAspect;\n      offsetX = 0;\n      offsetY = (displayHeight - containerRect.height) / 2;\n    }\n\n    // Calculate the guide areas in canvas coordinates\n    // SVG viewBox is 800x600\n    // Main rectangle: x=\"320\" y=\"150\" width=\"160\" height=\"300\" (wrist/forearm area)\n    // Circle: cx=\"400\" cy=\"300\" r=\"60\" (hand area)\n\n    const scaleX = canvas.width / displayWidth;\n    const scaleY = canvas.height / displayHeight;\n\n    // Wrist/forearm area (rectangle)\n    const rectX = Math.max(0, ((320 / 800) * displayWidth - offsetX) * scaleX);\n    const rectY = Math.max(0, ((150 / 600) * displayHeight - offsetY) * scaleY);\n    const rectWidth = Math.min(canvas.width - rectX, ((160 / 800) * displayWidth) * scaleX);\n    const rectHeight = Math.min(canvas.height - rectY, ((300 / 600) * displayHeight) * scaleY);\n\n    // Hand area (circle)\n    const circleX = Math.max(0, ((340 / 800) * displayWidth - offsetX) * scaleX); // Adjusted for circle bounds\n    const circleY = Math.max(0, ((240 / 600) * displayHeight - offsetY) * scaleY); // Adjusted for circle bounds\n    const circleWidth = Math.min(canvas.width - circleX, ((120 / 800) * displayWidth) * scaleX); // Circle diameter\n    const circleHeight = Math.min(canvas.height - circleY, ((120 / 600) * displayHeight) * scaleY); // Circle diameter\n\n    try {\n      // Check wrist/forearm area (rectangle)\n      const rectImageData = ctx.getImageData(rectX, rectY, rectWidth, rectHeight);\n      const rectData = rectImageData.data;\n\n      // Check hand area (circle approximation)\n      const circleImageData = ctx.getImageData(circleX, circleY, circleWidth, circleHeight);\n      const circleData = circleImageData.data;\n\n      let rectSkinPixels = 0;\n      let rectTotalPixels = 0;\n      let circleSkinPixels = 0;\n      let circleTotalPixels = 0;\n\n      // Enhanced skin tone detection\n      const isSkinTone = (r, g, b) => {\n        // Multiple skin tone ranges to cover different skin colors\n        const condition1 = r > 95 && g > 40 && b > 20 && r > g && r > b && Math.abs(r - g) > 15;\n        const condition2 = r > 220 && g > 210 && b > 170 && Math.abs(r - g) < 15 && Math.abs(g - b) < 15;\n        const condition3 = r > 120 && g > 80 && b > 50 && r > g && g > b;\n        const condition4 = r > 180 && g > 120 && b > 90 && r > g && g >= b;\n\n        return condition1 || condition2 || condition3 || condition4;\n      };\n\n      // Analyze rectangle area (wrist/forearm)\n      for (let i = 0; i < rectData.length; i += 4) {\n        const r = rectData[i];\n        const g = rectData[i + 1];\n        const b = rectData[i + 2];\n\n        if (isSkinTone(r, g, b)) {\n          rectSkinPixels++;\n        }\n        rectTotalPixels++;\n      }\n\n      // Analyze circle area (hand)\n      for (let i = 0; i < circleData.length; i += 4) {\n        const r = circleData[i];\n        const g = circleData[i + 1];\n        const b = circleData[i + 2];\n\n        if (isSkinTone(r, g, b)) {\n          circleSkinPixels++;\n        }\n        circleTotalPixels++;\n      }\n\n      // Calculate skin ratios\n      const rectSkinRatio = rectSkinPixels / rectTotalPixels;\n      const circleSkinRatio = circleSkinPixels / circleTotalPixels;\n\n      // Both wrist/forearm AND hand areas must have sufficient skin tone presence\n      // Wrist area needs at least 20% skin tone (forearm/wrist)\n      // Hand area needs at least 25% skin tone (hand/fingers)\n      const wristInPosition = rectSkinRatio > 0.20;\n      const handInPosition = circleSkinRatio > 0.25;\n\n      return wristInPosition && handInPosition;\n\n    } catch (error) {\n      console.warn('Hand detection error:', error);\n      return false;\n    }\n  };\n\n  // Apply product to watch position with dynamic scaling\n  const applyProductToWatchPosition = (productPath, productType) => {\n    // Clear any existing product first\n    setSelectedProduct(null);\n\n    // Find the watch data for dial size\n    const watchData = watches.find(w => w.path === productPath);\n\n    // Small delay to ensure the old product is removed before adding new one\n    setTimeout(() => {\n      setSelectedProduct({\n        path: productPath,\n        dialSize: watchData?.dialSize || 40 // Default to 40mm if not found\n      });\n    }, 50);\n  };\n\n  // Handle capture button click\n  const handleCapture = () => {\n    if (!isCaptured) {\n      const capturedDataUrl = captureFrame();\n      if (capturedImageRef.current && capturedDataUrl) {\n        capturedImageRef.current.src = capturedDataUrl;\n        capturedImageRef.current.style.display = 'block';\n      }\n      setIsCaptured(true);\n      setShowProductSelection(true); // Show products immediately\n      setShowHandGuide(false);\n\n      // Detect hand orientation\n      if (canvasRef.current && videoRef.current) {\n        const canvas = canvasRef.current;\n        const ctx = canvas.getContext('2d');\n        canvas.width = videoRef.current.videoWidth;\n        canvas.height = videoRef.current.videoHeight;\n        ctx.drawImage(videoRef.current, 0, 0);\n        const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);\n        setIsRightHand(detectHandOrientation(imageData));\n      }\n    } else {\n      setShowProductSelection(true);\n    }\n  };\n\n  // Handle back button click\n  const handleBack = () => {\n    if (capturedImageRef.current) {\n      capturedImageRef.current.style.display = 'none';\n    }\n    setIsCaptured(false);\n    setSelectedProduct(null);\n    setIsRightHand(false);\n    setShowProductSelection(false);\n    setShowWristSizeModal(false);\n    setShowHandGuide(true);\n  };\n\n  // Handle gender selection\n  const handleGenderChange = (gender) => {\n    setUserGender(gender);\n    setUserWristSize(DEFAULT_WRIST_SIZES[gender]);\n  };\n\n  // Handle wrist size change\n  const handleWristSizeChange = (size) => {\n    setUserWristSize(size);\n  };\n\n  // Handle continue to product selection - No longer needed since both panels show together\n  // const handleContinueToProducts = () => {\n  //   setShowWristSizeInput(false);\n  //   setShowProductSelection(true);\n  // };\n\n  // Handle tab change\n  const handleTabChange = (tabName) => {\n    setActiveTab(tabName);\n  };\n\n  // Handle product selection\n  const handleProductSelect = (product) => {\n    applyProductToWatchPosition(product.path, activeTab);\n  };\n\n  // Initialize camera on component mount\n  useEffect(() => {\n    initCamera();\n  }, []);\n\n  // Hand position monitoring effect (only when autocapture is enabled)\n  useEffect(() => {\n    if (!isAutoCaptureEnabled || isCaptured) return;\n\n    const interval = setInterval(() => {\n      const handInPosition = detectHandInPosition();\n      setIsHandInPosition(handInPosition);\n\n      // Only start countdown if hand is properly positioned\n      if (handInPosition && !isCountdownActive && countdown === 0) {\n        setIsCountdownActive(true);\n      }\n\n      // Stop countdown if hand moves out of position\n      if (!handInPosition && isCountdownActive) {\n        setIsCountdownActive(false);\n        setCountdown(0);\n      }\n    }, 150); // Check every 150ms for better performance\n\n    return () => clearInterval(interval);\n  }, [isAutoCaptureEnabled, isCaptured, isCountdownActive, countdown]);\n\n  // Countdown effect (starts automatically when hand is in position)\n  useEffect(() => {\n    if (!isCountdownActive || isCaptured) {\n      setCountdown(0);\n      return;\n    }\n\n    // Only start countdown if hand is still in position\n    if (!isHandInPosition) {\n      setIsCountdownActive(false);\n      setCountdown(0);\n      return;\n    }\n\n    // Start countdown\n    setCountdown(3);\n\n    const countdownInterval = setInterval(() => {\n      setCountdown(prev => {\n        // Double-check hand position before continuing countdown\n        if (!isHandInPosition) {\n          clearInterval(countdownInterval);\n          setIsCountdownActive(false);\n          return 0;\n        }\n\n        if (prev <= 1) {\n          // Countdown finished - trigger capture\n          clearInterval(countdownInterval);\n          setIsCountdownActive(false);\n          setIsAutoCaptureEnabled(false); // Turn off autocapture after capture\n          handleCapture();\n          return 0;\n        }\n        return prev - 1;\n      });\n    }, 1000);\n\n    return () => clearInterval(countdownInterval);\n  }, [isCountdownActive, isCaptured, isHandInPosition]);\n\n  // Handle autocapture switch toggle\n  const handleAutoCaptureToggle = () => {\n    const newState = !isAutoCaptureEnabled;\n    setIsAutoCaptureEnabled(newState);\n\n    // Reset states when turning off\n    if (!newState) {\n      setIsCountdownActive(false);\n      setCountdown(0);\n      setIsHandInPosition(false);\n    }\n  };\n\n  // Reset autocapture when going back\n  const handleBackWithReset = () => {\n    setIsAutoCaptureEnabled(false);\n    setIsCountdownActive(false);\n    setCountdown(0);\n    setIsHandInPosition(false);\n    setShowWristSizeModal(false);\n    setUserGender('men');\n    setUserWristSize(64);\n    handleBack();\n  };\n\n  // Get current products based on active tab\n  const getCurrentProducts = () => {\n    return activeTab === 'Watches' ? watches : bracelets;\n  };\n\n  // Add touch gesture handlers\n  const handleTouchStart = (e) => {\n    setIsDragging(true);\n    setStartY(e.touches[0].clientY);\n  };\n\n  const handleTouchMove = (e) => {\n    if (!isDragging) return;\n    \n    const currentY = e.touches[0].clientY;\n    const diff = currentY - startY;\n    \n    // Only allow dragging down\n    if (diff > 0) {\n      setPanelPosition(diff);\n    }\n  };\n\n  const handleTouchEnd = () => {\n    if (!isDragging) return;\n    \n    setIsDragging(false);\n    \n    // If dragged more than 100px down, close the panel\n    if (panelPosition > 100) {\n      setShowProductSelection(false);\n    }\n    \n    // Reset position\n    setPanelPosition(0);\n  };\n\n  // Add click outside handler for modals\n  useEffect(() => {\n    const handleClickOutside = (e) => {\n      if (showWristSizeModal && !e.target.closest('.modal-content')) {\n        setShowWristSizeModal(false);\n      }\n    };\n\n    document.addEventListener('mousedown', handleClickOutside);\n    document.addEventListener('touchstart', handleClickOutside);\n    \n    return () => {\n      document.removeEventListener('mousedown', handleClickOutside);\n      document.removeEventListener('touchstart', handleClickOutside);\n    };\n  }, [showWristSizeModal]);\n\n  // Update product selection panel JSX\n  return (\n    <div className=\"relative h-screen flex flex-col bg-gray-50 font-sans overflow-hidden\">\n      <div className=\"flex-1 relative overflow-hidden bg-black flex items-center justify-center\">\n        <video\n          ref={videoRef}\n          className=\"w-full h-full object-cover\"\n          autoPlay\n          playsInline\n          muted\n        />\n        <canvas ref={canvasRef} className=\"hidden\" />\n        <img\n          ref={capturedImageRef}\n          className=\"absolute top-0 left-0 w-full h-full object-cover hidden\"\n          alt=\"Captured hand\"\n        />\n\n        {/* Simple Home Button - Only visible when not captured */}\n        {!isCaptured && (\n          <button\n            className=\"absolute top-4 left-4 bg-black/60 text-white p-3 rounded-full text-xl font-bold cursor-pointer z-10 border-none shadow-lg transition-all duration-200 flex items-center justify-center w-11 h-11 hover:bg-black/80 focus:outline-none focus:ring-2 focus:ring-primary-green focus:ring-offset-2\"\n            onClick={onBackToHome}\n            aria-label=\"Home\"\n          >\n            <Home className=\"w-5 h-5\" />\n          </button>\n        )}\n\n        {/* Autocapture Switch Button - Only visible when not captured */}\n        {!isCaptured && (\n          <div className=\"absolute top-4 right-4 z-20 flex flex-col items-center\">\n            <label className=\"text-primary-green font-bold text-sm md:text-base mb-1.5 tracking-wide\">Auto Capture</label>\n            <label className=\"relative inline-flex items-center cursor-pointer\">\n              <input\n                type=\"checkbox\"\n                checked={isAutoCaptureEnabled}\n                onChange={handleAutoCaptureToggle}\n                disabled={isCountdownActive}\n                aria-label=\"Toggle auto capture\"\n                className=\"sr-only\"\n              />\n              <div className={`relative w-14 h-8 rounded-full transition-colors duration-200 ${\n                isAutoCaptureEnabled ? 'bg-primary-green' : 'bg-gray-300'\n              }`}>\n                <div className={`absolute top-1 left-1 w-6 h-6 bg-white rounded-full transition-transform duration-200 ${\n                  isAutoCaptureEnabled ? 'translate-x-6' : 'translate-x-0'\n                }`}>\n                  {isAutoCaptureEnabled ? (\n                    <svg className=\"w-4 h-4 text-primary-green absolute top-1 left-1\" viewBox=\"0 0 12 10\">\n                      <polyline points=\"1.5 6 5 9 10.5 1\" fill=\"none\" stroke=\"currentColor\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\"/>\n                    </svg>\n                  ) : (\n                    <svg className=\"w-4 h-4 text-gray-400 absolute top-1 left-1\" viewBox=\"0 0 10 10\">\n                      <line x1=\"1\" y1=\"1\" x2=\"9\" y2=\"9\" stroke=\"currentColor\" strokeWidth=\"2\" strokeLinecap=\"round\"/>\n                      <line x1=\"9\" y1=\"1\" x2=\"1\" y2=\"9\" stroke=\"currentColor\" strokeWidth=\"2\" strokeLinecap=\"round\"/>\n                    </svg>\n                  )}\n                </div>\n              </div>\n            </label>\n          </div>\n        )}\n\n        {/* Simple Back Button - Only visible when captured */}\n        {isCaptured && (\n          <button\n            className=\"absolute top-4 left-4 bg-black/60 text-white p-3 rounded-full text-xl font-bold cursor-pointer z-10 border-none shadow-lg transition-all duration-200 flex items-center justify-center w-11 h-11 hover:bg-black/80 focus:outline-none focus:ring-2 focus:ring-primary-green focus:ring-offset-2\"\n            onClick={handleBackWithReset}\n            aria-label=\"Back\"\n          >\n            <ArrowLeft className=\"w-5 h-5\" />\n          </button>\n        )}\n\n        {/* Countdown Display - Only visible during active countdown */}\n        {isCountdownActive && (\n          <div className=\"absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 z-30 text-center\">\n            <div className=\"text-6xl md:text-8xl font-bold text-white mb-2 drop-shadow-lg\">{countdown}</div>\n            <div className=\"text-lg md:text-xl text-white font-medium bg-black/50 px-4 py-2 rounded-full\">Auto capturing...</div>\n          </div>\n        )}\n\n        {/* Status Messages */}\n        {isAutoCaptureEnabled && !isHandInPosition && !isCountdownActive && (\n          <div className=\"absolute bottom-32 left-1/2 transform -translate-x-1/2 z-20 text-center max-w-sm mx-auto\">\n            <div className=\"bg-black/70 text-white px-4 py-3 rounded-xl mb-2 font-medium\">\n              Position your arm and wrist in the guide area\n            </div>\n            <div className=\"bg-black/50 text-white/80 px-3 py-2 rounded-lg text-sm\">\n              Countdown will start automatically when detected\n            </div>\n          </div>\n        )}\n\n        {isAutoCaptureEnabled && isHandInPosition && !isCountdownActive && (\n          <div className=\"absolute bottom-32 left-1/2 transform -translate-x-1/2 z-20 text-center\">\n            <div className=\"bg-primary-green/90 text-white px-6 py-3 rounded-xl font-medium\">\n              Perfect! Starting countdown...\n            </div>\n          </div>\n        )}\n\n        {/* Hand Guide SVG */}\n        {showHandGuide && (\n          <div\n            style={{\n              ...styles.handGuide,\n              opacity: isAutoCaptureEnabled && isHandInPosition ? 0.9 : 0.8,\n              filter: isAutoCaptureEnabled && isHandInPosition\n                ? 'drop-shadow(0 2px 8px rgba(45, 140, 136, 0.5))'\n                : isAutoCaptureEnabled && !isHandInPosition\n                  ? 'drop-shadow(0 2px 8px rgba(255, 107, 107, 0.5))'\n                  : 'drop-shadow(0 2px 8px rgba(255, 255, 255, 0.3))'\n            }}\n            className={isMobile ? 'mobile-hand-guide' : ''}\n            aria-hidden=\"true\"\n          >\n            <svg viewBox=\"0 0 800 600\" xmlns=\"http://www.w3.org/2000/svg\">\n              {/* Wrist guide lines */}\n              <path\n                d=\"M100 480 C 150 460, 200 450, 250 450 L 550 450 C 600 450, 650 460, 700 480\"\n                stroke={\n                  isAutoCaptureEnabled && isHandInPosition\n                    ? \"#2D8C88\"\n                    : isAutoCaptureEnabled && !isHandInPosition\n                      ? \"#ff6b6b\"\n                      : \"white\"\n                }\n                strokeWidth=\"5\"\n                fill=\"none\"\n                strokeLinecap=\"round\"\n              />\n              <path\n                d=\"M100 120 C 150 140, 200 150, 250 150 L 550 150 C 600 150, 650 140, 700 120\"\n                stroke={\n                  isAutoCaptureEnabled && isHandInPosition\n                    ? \"#2D8C88\"\n                    : isAutoCaptureEnabled && !isHandInPosition\n                      ? \"#ff6b6b\"\n                      : \"white\"\n                }\n                strokeWidth=\"5\"\n                fill=\"none\"\n                strokeLinecap=\"round\"\n              />\n              {/* Wrist/Forearm area (rectangle) */}\n              <rect\n                x=\"320\"\n                y=\"150\"\n                width=\"160\"\n                height=\"300\"\n                fill={\n                  isAutoCaptureEnabled && isHandInPosition\n                    ? \"#2D8C88\"\n                    : isAutoCaptureEnabled && !isHandInPosition\n                      ? \"#ff6b6b\"\n                      : \"white\"\n                }\n                opacity={isAutoCaptureEnabled && isHandInPosition ? \"0.25\" : \"0.15\"}\n                rx=\"15\"\n                stroke={\n                  isAutoCaptureEnabled && isHandInPosition\n                    ? \"#2D8C88\"\n                    : isAutoCaptureEnabled && !isHandInPosition\n                      ? \"#ff6b6b\"\n                      : \"white\"\n                }\n                strokeWidth=\"2\"\n              />\n              {/* Hand area (circle) */}\n              <circle\n                cx=\"400\"\n                cy=\"300\"\n                r=\"60\"\n                fill={\n                  isAutoCaptureEnabled && isHandInPosition\n                    ? \"#2D8C88\"\n                    : isAutoCaptureEnabled && !isHandInPosition\n                      ? \"#ff6b6b\"\n                      : \"white\"\n                }\n                opacity={isAutoCaptureEnabled && isHandInPosition ? \"0.3\" : \"0.2\"}\n                stroke={\n                  isAutoCaptureEnabled && isHandInPosition\n                    ? \"#2D8C88\"\n                    : isAutoCaptureEnabled && !isHandInPosition\n                      ? \"#ff6b6b\"\n                      : \"white\"\n                }\n                strokeWidth=\"2\"\n              />\n              {/* Labels for clarity */}\n              {isAutoCaptureEnabled && (\n                <>\n                  <text x=\"400\" y=\"140\" textAnchor=\"middle\" fill=\"white\" fontSize=\"14\" fontWeight=\"bold\">\n                    WRIST & FOREARM\n                  </text>\n                  <text x=\"400\" y=\"480\" textAnchor=\"middle\" fill=\"white\" fontSize=\"14\" fontWeight=\"bold\">\n                    HAND\n                  </text>\n                </>\n              )}\n            </svg>\n          </div>\n        )}\n\n        {/* Product Display */}\n        {selectedProduct && (\n          <div style={{\n            ...styles.productPosition,\n            width: activeTab === 'Watches' ? `${WATCH_WIDTH}%` : `${BRACELET_WIDTH}%`,\n            height: activeTab === 'Watches' ? `${WATCH_HEIGHT}%` : `${BRACELET_HEIGHT}%`\n          }}>\n            <div style={{\n              position: 'relative',\n              width: '100%',\n              height: '100%',\n              display: 'flex',\n              alignItems: 'center',\n              justifyContent: 'center'\n            }}>\n              <img\n                src={typeof selectedProduct === 'object' ? selectedProduct.path : selectedProduct}\n                alt=\"Selected product\"\n                style={{\n                  width: '100%',\n                  height: '100%',\n                  objectFit: 'contain',\n                  transform: activeTab === 'Bracelets'\n                    ? `rotate(90deg) scale(${BRACELET_HEIGHT / 30})${isRightHand ? ' scaleX(-1)' : ''}`\n                    : `scale(${(WATCH_HEIGHT / 25) * (userWristSize / DEFAULT_WRIST_SIZES[userGender])})`,\n                  filter: 'drop-shadow(0 2px 8px rgba(0,0,0,0.3))'\n                }}\n                onLoad={(e) => removeBackground(e.target, activeTab === 'Watches' ? 'watch' : 'bracelet')}\n              />\n              {activeTab === 'Watches' && typeof selectedProduct === 'object' && (\n                <div style={{\n                  position: 'absolute',\n                  bottom: '-30px',\n                  left: '50%',\n                  transform: 'translateX(-50%)',\n                  fontSize: '11px',\n                  fontWeight: '600',\n                  color: 'white',\n                  backgroundColor: 'rgba(45, 140, 136, 0.9)',\n                  padding: '3px 8px',\n                  borderRadius: '12px',\n                  whiteSpace: 'nowrap',\n                  pointerEvents: 'none',\n                  boxShadow: '0 2px 6px rgba(0,0,0,0.3)',\n                  zIndex: 2\n                }}>\n                  {selectedProduct.dialSize}mm\n                  {userWristSize !== DEFAULT_WRIST_SIZES[userGender] && (\n                    <span style={{\n                      fontSize: '10px',\n                      opacity: 0.8,\n                      marginLeft: '4px'\n                    }}>\n                      (scaled {((userWristSize / DEFAULT_WRIST_SIZES[userGender] - 1) * 100).toFixed(0)}%)\n                    </span>\n                  )}\n                </div>\n              )}\n            </div>\n          </div>\n        )}\n\n        {/* Camera-style Capture Button */}\n        <button\n          style={styles.captureBtn}\n          className={isMobile ? 'mobile-capture-btn' : ''}\n          onClick={handleCapture}\n          aria-label={isCaptured ? \"Select Products\" : \"Capture\"}\n        >\n          {!isCaptured ? (\n            <div style={styles.captureInner} className={isMobile ? 'mobile-inner-circle' : ''}></div>\n          ) : (\n            <svg width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"white\">\n              <path d=\"M8.59 16.59L13.17 12L8.59 7.41L10 6L16 12L10 18L8.59 16.59Z\"/>\n            </svg>\n          )}\n        </button>\n\n        {/* Reset Button - Only visible when captured */}\n        {isCaptured && (\n          <button\n            style={styles.resetBtn}\n            onClick={() => window.location.reload()}\n            aria-label=\"Reset\"\n          >\n            <svg width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"white\">\n              <path d=\"M17.65 6.35C16.2 4.9 14.21 4 12 4C7.58 4 4 7.58 4 12S7.58 20 12 20C15.73 20 18.84 17.45 19.73 14H17.65C16.83 16.33 14.61 18 12 18C8.69 18 6 15.31 6 12S8.69 6 12 6C13.66 6 15.14 6.69 16.22 7.78L13 11H20V4L17.65 6.35Z\"/>\n            </svg>\n          </button>\n        )}\n      </div>\n\n      {/* Mobile Wrist Size Button - Top right corner */}\n      {isCaptured && (\n        <button\n          style={styles.wristSizeFloatingBtn}\n          className={isMobile ? 'mobile-btn' : ''}\n          onClick={() => setShowWristSizeModal(true)}\n          aria-label=\"Adjust wrist size\"\n        >\n          <svg width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"white\">\n            <path d=\"M12,15.5A3.5,3.5 0 0,1 8.5,12A3.5,3.5 0 0,1 12,8.5A3.5,3.5 0 0,1 15.5,12A3.5,3.5 0 0,1 12,15.5M19.43,12.97C19.47,12.65 19.5,12.33 19.5,12C19.5,11.67 19.47,11.34 19.43,11L21.54,9.37C21.73,9.22 21.78,8.95 21.66,8.73L19.66,5.27C19.54,5.05 19.27,4.96 19.05,5.05L16.56,6.05C16.04,5.66 15.5,5.32 14.87,5.07L14.5,2.42C14.46,2.18 14.25,2 14,2H10C9.75,2 9.54,2.18 9.5,2.42L9.13,5.07C8.5,5.32 7.96,5.66 7.44,6.05L4.95,5.05C4.73,4.96 4.46,5.05 4.34,5.27L2.34,8.73C2.22,8.95 2.27,9.22 2.46,9.37L4.57,11C4.53,11.34 4.5,11.67 4.5,12C4.5,12.33 4.53,12.65 4.57,12.97L2.46,14.63C2.27,14.78 2.22,15.05 2.34,15.27L4.34,18.73C4.46,18.95 4.73,19.03 4.95,18.95L7.44,17.94C7.96,18.34 8.5,18.68 9.13,18.93L9.5,21.58C9.54,21.82 9.75,22 10,22H14C14.25,22 14.46,21.82 14.5,21.58L14.87,18.93C15.5,18.68 16.04,18.34 16.56,17.94L19.05,18.95C19.27,19.03 19.54,18.95 19.66,18.73L21.66,15.27C21.78,15.05 21.73,14.78 21.54,14.63L19.43,12.97Z\"/>\n          </svg>\n          <span style={styles.wristSizeText}>{userWristSize}mm</span>\n        </button>\n      )}\n\n      {/* Wrist Size Modal - Mobile-friendly popup */}\n      {showWristSizeModal && (\n        <div \n          style={styles.modalOverlay} \n          onClick={() => setShowWristSizeModal(false)}\n          className=\"modal-overlay\"\n        >\n          <div \n            style={styles.wristSizeModal} \n            onClick={(e) => e.stopPropagation()}\n            className=\"modal-content\"\n          >\n            <div style={styles.modalHeader}>\n              <h3 style={styles.modalTitle}>Adjust Wrist Size</h3>\n              <button\n                style={styles.modalCloseBtn}\n                onClick={() => setShowWristSizeModal(false)}\n                aria-label=\"Close\"\n              >\n                <svg width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"currentColor\">\n                  <path d=\"M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z\"/>\n                </svg>\n              </button>\n            </div>\n\n            <div style={styles.modalContent}>\n              {/* Gender Selection */}\n              <div style={styles.genderSelection}>\n                <button\n                  style={{\n                    ...styles.genderButton,\n                    ...(userGender === 'men' ? styles.genderButtonActive : {})\n                  }}\n                  onClick={() => handleGenderChange('men')}\n                >\n                  Men (64mm)\n                </button>\n                <button\n                  style={{\n                    ...styles.genderButton,\n                    ...(userGender === 'women' ? styles.genderButtonActive : {})\n                  }}\n                  onClick={() => handleGenderChange('women')}\n                >\n                  Women (54mm)\n                </button>\n              </div>\n\n              {/* Wrist Size Slider */}\n              <div style={styles.sliderContainer}>\n                <label style={styles.sliderLabel}>\n                  Wrist Size: {userWristSize}mm\n                  {userWristSize !== DEFAULT_WRIST_SIZES[userGender] && (\n                    <span style={styles.sizeChange}>\n                      ({userWristSize > DEFAULT_WRIST_SIZES[userGender] ? '+' : ''}\n                      {((userWristSize / DEFAULT_WRIST_SIZES[userGender] - 1) * 100).toFixed(0)}%)\n                    </span>\n                  )}\n                </label>\n                <input\n                  type=\"range\"\n                  min=\"45\"\n                  max=\"80\"\n                  value={userWristSize}\n                  onChange={(e) => handleWristSizeChange(parseInt(e.target.value))}\n                  style={styles.slider}\n                />\n                <div style={styles.sliderLabels}>\n                  <span>45mm</span>\n                  <span>80mm</span>\n                </div>\n\n                {/* Quick Size Presets */}\n                <div style={styles.presetButtons}>\n                  <button\n                    style={styles.presetButton}\n                    onClick={() => handleWristSizeChange(50)}\n                  >\n                    Small (50mm)\n                  </button>\n                  <button\n                    style={styles.presetButton}\n                    onClick={() => handleWristSizeChange(DEFAULT_WRIST_SIZES[userGender])}\n                  >\n                    Default ({DEFAULT_WRIST_SIZES[userGender]}mm)\n                  </button>\n                  <button\n                    style={styles.presetButton}\n                    onClick={() => handleWristSizeChange(70)}\n                  >\n                    Large (70mm)\n                  </button>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n      )}\n\n      {/* Product Selection Panel */}\n      {showProductSelection && (\n        <div\n          ref={panelRef}\n          style={{\n            ...styles.productSelection,\n            transform: `translateY(${panelPosition}px)`,\n            touchAction: 'none'\n          }}\n          className={isMobile ? 'mobile-product-panel' : ''}\n          aria-modal=\"true\"\n          role=\"dialog\"\n          onTouchStart={handleTouchStart}\n          onTouchMove={handleTouchMove}\n          onTouchEnd={handleTouchEnd}\n        >\n          <div \n            style={styles.dragHandle} \n            aria-hidden=\"true\"\n            onTouchStart={handleTouchStart}\n            onTouchMove={handleTouchMove}\n            onTouchEnd={handleTouchEnd}\n          />\n          <div style={styles.productTabs}>\n            <button\n              style={{\n                ...styles.tab,\n                ...(activeTab === 'Watches' ? styles.activeTab : {})\n              }}\n              onClick={() => handleTabChange('Watches')}\n            >\n              Watches\n            </button>\n            <button\n              style={{\n                ...styles.tab,\n                ...(activeTab === 'Bracelets' ? styles.activeTab : {})\n              }}\n              onClick={() => handleTabChange('Bracelets')}\n            >\n              Bracelets\n            </button>\n          </div>\n          <div style={styles.productScroll} className=\"product-scroll\">\n            {getCurrentProducts().map((product, index) => {\n              // Simple null check only\n              if (!product) return null;\n\n              const isSelected = (typeof selectedProduct === 'object' ? selectedProduct?.path : selectedProduct) === product.path;\n\n              return (\n                <button\n                  key={index}\n                  style={{\n                    ...styles.productItem,\n                    borderColor: isSelected ? '#2D8C88' : '#e9ecef',\n                    backgroundColor: isSelected ? '#f0fffe' : '#ffffff'\n                  }}\n                  title={`${product.name} - ${product.caseDiameter || 'N/A'}mm`}\n                  onClick={() => handleProductSelect(product)}\n                  aria-label={`Select ${product.name} ${product.caseDiameter || 'N/A'}mm`}\n                >\n                  <img\n                    src={product.path}\n                    alt={product.name}\n                    style={styles.productImage}\n                    onError={(e) => {\n                      e.target.parentElement.style.display = 'none';\n                    }}\n                  />\n                  <div style={styles.productLabel}>\n                    <div style={styles.productName}>{product.name}</div>\n                    {activeTab === 'Watches' && product.caseDiameter && (\n                      <div style={styles.productSize}>{product.caseDiameter}mm</div>\n                    )}\n                  </div>\n                </button>\n              );\n            })}\n          </div>\n        </div>\n      )}\n    </div>\n  );\n};\n\n// Styles object - Clean, modern design with improved mobile responsiveness\nconst styles = {\n  container: {\n    position: 'relative',\n    height: 'calc(var(--vh, 1vh) * 100)',\n    display: 'flex',\n    flexDirection: 'column',\n    backgroundColor: '#f8f9fa',\n    color: '#333',\n    fontFamily: \"'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif\",\n    overflow: 'hidden',\n    touchAction: 'manipulation',\n    WebkitTapHighlightColor: 'transparent',\n    WebkitOverflowScrolling: 'touch' // Smooth scrolling on iOS\n  },\n  cameraContainer: {\n    flex: 1,\n    position: 'relative',\n    overflow: 'hidden',\n    backgroundColor: '#000',\n    display: 'flex',\n    alignItems: 'center',\n    justifyContent: 'center'\n  },\n  cameraFeed: {\n    width: '100%',\n    height: '100%',\n    objectFit: 'cover',\n    transform: 'scaleX(1)' // Fix for Safari mirroring\n  },\n  capturedImage: {\n    position: 'absolute',\n    top: 0,\n    left: 0,\n    width: '100%',\n    height: '100%',\n    objectFit: 'cover',\n    display: 'none',\n    WebkitTransform: 'scaleX(1)' // Fix for Safari mirroring\n  },\n\n  homeBtn: {\n    position: 'absolute',\n    top: '20px',\n    left: '20px',\n    backgroundColor: 'rgba(0, 0, 0, 0.6)',\n    color: 'white',\n    padding: '12px',\n    borderRadius: '50%',\n    fontSize: '20px',\n    fontWeight: '700',\n    cursor: 'pointer',\n    zIndex: 10,\n    border: 'none',\n    boxShadow: '0 2px 10px rgba(0, 0, 0, 0.3)',\n    transition: 'all 0.2s ease',\n    display: 'flex',\n    alignItems: 'center',\n    justifyContent: 'center',\n    width: '44px',\n    height: '44px',\n    outline: 'none'\n  },\n  backBtn: {\n    position: 'absolute',\n    top: '20px',\n    left: '20px',\n    backgroundColor: 'rgba(0, 0, 0, 0.6)',\n    color: 'white',\n    padding: '12px',\n    borderRadius: '50%',\n    fontSize: '20px',\n    fontWeight: '700',\n    cursor: 'pointer',\n    zIndex: 10,\n    border: 'none',\n    boxShadow: '0 2px 10px rgba(0, 0, 0, 0.3)',\n    transition: 'all 0.2s ease',\n    display: 'flex',\n    alignItems: 'center',\n    justifyContent: 'center',\n    width: '44px',\n    height: '44px',\n    outline: 'none'\n  },\n  switchContainer: {\n    position: 'absolute',\n    top: '20px',\n    right: '20px',\n    zIndex: 10,\n    display: 'flex',\n    flexDirection: 'column',\n    alignItems: 'center',\n    gap: '4px',\n    padding: '12px',\n    backgroundColor: 'rgba(0, 0, 0, 0.5)',\n    borderRadius: '20px',\n    backdropFilter: 'blur(10px)',\n    WebkitBackdropFilter: 'blur(10px)',\n    boxShadow: '0 4px 12px rgba(0, 0, 0, 0.2)',\n    border: '1px solid rgba(255, 255, 255, 0.1)'\n  },\n  switchTrack: {\n    position: 'absolute',\n    top: '12px',\n    left: '12px',\n    width: '60px',\n    height: '32px',\n    backgroundColor: 'rgba(0, 0, 0, 0.3)',\n    borderRadius: '16px',\n    border: '2px solid rgba(255, 255, 255, 0.2)',\n    zIndex: 9,\n    transition: 'all 0.3s ease'\n  },\n  switchButton: {\n    position: 'relative',\n    width: '28px',\n    height: '28px',\n    borderRadius: '50%',\n    border: 'none',\n    cursor: 'pointer',\n    transition: 'all 0.3s ease',\n    display: 'flex',\n    alignItems: 'center',\n    justifyContent: 'center',\n    zIndex: 11,\n    margin: '2px',\n    boxShadow: '0 2px 8px rgba(0, 0, 0, 0.3)',\n    outline: 'none',\n    '&:hover': {\n      transform: 'scale(1.1)'\n    }\n  },\n  switchLabel: {\n    fontSize: '12px',\n    fontWeight: '700',\n    color: 'white',\n    textShadow: '0 1px 2px rgba(0, 0, 0, 0.7)',\n    marginTop: '4px',\n    padding: '4px 12px',\n    borderRadius: '12px',\n    backgroundColor: 'rgba(0, 0, 0, 0.3)',\n    letterSpacing: '0.5px'\n  },\n  countdownDisplay: {\n    position: 'absolute',\n    top: '35%',\n    left: '50%',\n    transform: 'translate(-50%, -50%)',\n    zIndex: 15,\n    textAlign: 'center',\n    pointerEvents: 'none',\n    padding: '16px 24px',\n    backgroundColor: 'rgba(0, 0, 0, 0.4)',\n    borderRadius: '20px',\n    backdropFilter: 'blur(10px)',\n    WebkitBackdropFilter: 'blur(10px)',\n    boxShadow: '0 4px 20px rgba(0, 0, 0, 0.3)'\n  },\n  countdownNumber: {\n    fontSize: '72px',\n    fontWeight: '900',\n    color: '#2D8C88',\n    textShadow: '0 2px 8px rgba(0, 0, 0, 0.5)',\n    marginBottom: '8px',\n    animation: 'pulse 1s ease-in-out'\n  },\n  countdownText: {\n    fontSize: '16px',\n    fontWeight: '600',\n    color: 'white',\n    textShadow: '0 1px 4px rgba(0, 0, 0, 0.7)'\n  },\n  statusMessage: {\n    position: 'absolute',\n    top: '25%',\n    left: '50%',\n    transform: 'translate(-50%, -50%)',\n    zIndex: 15,\n    textAlign: 'center',\n    pointerEvents: 'none',\n    padding: '12px 20px',\n    borderRadius: '16px',\n    backgroundColor: 'rgba(0, 0, 0, 0.4)',\n    backdropFilter: 'blur(10px)',\n    WebkitBackdropFilter: 'blur(10px)',\n    boxShadow: '0 4px 20px rgba(0, 0, 0, 0.3)'\n  },\n  statusText: {\n    fontSize: '16px',\n    fontWeight: '600',\n    color: 'white',\n    textShadow: '0 1px 4px rgba(0, 0, 0, 0.7)',\n    backgroundColor: 'rgba(255, 107, 107, 0.9)',\n    padding: '12px 20px',\n    borderRadius: '25px',\n    marginBottom: '8px',\n    transition: 'all 0.3s ease'\n  },\n  statusSubtext: {\n    fontSize: '12px',\n    fontWeight: '500',\n    color: 'white',\n    textShadow: '0 1px 4px rgba(0, 0, 0, 0.7)',\n    backgroundColor: 'rgba(0, 0, 0, 0.6)',\n    padding: '6px 12px',\n    borderRadius: '15px'\n  },\n  handGuide: {\n    position: 'absolute',\n    top: '50%',\n    left: '50%',\n    transform: 'translate(-50%, -50%)',\n    width: '80%',\n    maxWidth: '400px',\n    height: 'auto',\n    opacity: 0.8,\n    pointerEvents: 'none',\n    zIndex: 5,\n    filter: 'drop-shadow(0 2px 8px rgba(255, 255, 255, 0.3))',\n    WebkitFilter: 'drop-shadow(0 2px 8px rgba(255, 255, 255, 0.3))',\n    transition: 'all 0.3s ease'\n  },\n  productPosition: {\n    position: 'absolute',\n    top: '50%',\n    left: '50%',\n    transform: 'translate(-50%, -50%)',\n    zIndex: 8,\n    display: 'flex',\n    alignItems: 'center',\n    justifyContent: 'center',\n    pointerEvents: 'none',\n    minWidth: '100px', // Ensure minimum size\n    minHeight: '100px' // Ensure minimum size\n  },\n  captureBtn: {\n    position: 'absolute',\n    bottom: '30px',\n    left: '50%',\n    transform: 'translateX(-50%)',\n    width: '80px',\n    height: '80px',\n    backgroundColor: 'rgba(255, 255, 255, 0.9)',\n    borderRadius: '50%',\n    display: 'flex',\n    alignItems: 'center',\n    justifyContent: 'center',\n    cursor: 'pointer',\n    zIndex: 10,\n    transition: 'all 0.2s ease',\n    border: '4px solid rgba(255, 255, 255, 0.3)',\n    boxShadow: '0 4px 20px rgba(0, 0, 0, 0.3)',\n    outline: 'none',\n    padding: 0,\n    WebkitTapHighlightColor: 'transparent',\n    touchAction: 'manipulation'\n  },\n  captureInner: {\n    width: '60px',\n    height: '60px',\n    backgroundColor: '#2D8C88',\n    borderRadius: '50%',\n    transition: 'all 0.2s ease'\n  },\n  resetBtn: {\n    position: 'absolute',\n    bottom: '30px',\n    right: '30px',\n    width: '50px',\n    height: '50px',\n    backgroundColor: 'rgba(0, 0, 0, 0.6)',\n    borderRadius: '50%',\n    display: 'flex',\n    alignItems: 'center',\n    justifyContent: 'center',\n    cursor: 'pointer',\n    zIndex: 10,\n    transition: 'all 0.2s ease',\n    border: 'none',\n    boxShadow: '0 2px 10px rgba(0, 0, 0, 0.3)',\n    outline: 'none',\n    padding: 0\n  },\n\n  // Wrist Size Floating Button - Follows CSS mobile patterns\n  wristSizeFloatingBtn: {\n    position: 'absolute',\n    top: '20px',\n    right: '20px',\n    backgroundColor: '#2D8C88',\n    color: 'white',\n    padding: '12px 16px',\n    borderRadius: '24px',\n    fontSize: '14px',\n    fontWeight: '600',\n    cursor: 'pointer',\n    border: 'none',\n    display: 'flex',\n    alignItems: 'center',\n    gap: '8px',\n    outline: 'none',\n    transition: 'all 0.2s ease',\n    zIndex: 15,\n    boxShadow: '0 2px 8px rgba(45, 140, 136, 0.3)',\n    minHeight: '48px',\n    minWidth: '48px',\n    WebkitTapHighlightColor: 'transparent',\n    touchAction: 'manipulation'\n  },\n  wristSizeText: {\n    fontSize: '12px',\n    fontWeight: '600'\n  },\n\n  // Modal Styles\n  modalOverlay: {\n    position: 'fixed',\n    top: 0,\n    left: 0,\n    right: 0,\n    bottom: 0,\n    backgroundColor: 'rgba(0, 0, 0, 0.5)',\n    display: 'flex',\n    alignItems: 'center',\n    justifyContent: 'center',\n    zIndex: 50,\n    padding: '20px',\n    backdropFilter: 'blur(5px)',\n    WebkitBackdropFilter: 'blur(5px)',\n    touchAction: 'none'\n  },\n  wristSizeModal: {\n    backgroundColor: 'white',\n    borderRadius: '24px',\n    width: '100%',\n    maxWidth: '95vw',\n    maxHeight: '80vh',\n    overflow: 'hidden',\n    boxShadow: '0 10px 30px rgba(0, 0, 0, 0.3)',\n    display: 'flex',\n    flexDirection: 'column',\n    margin: '10px',\n    position: 'relative'\n  },\n  modalHeader: {\n    display: 'flex',\n    alignItems: 'center',\n    justifyContent: 'space-between',\n    padding: '16px 20px 12px 20px',\n    borderBottom: '1px solid #f0f0f0'\n  },\n  modalTitle: {\n    fontSize: '18px',\n    fontWeight: '700',\n    color: '#2D8C88',\n    margin: 0\n  },\n  modalCloseBtn: {\n    width: '32px',\n    height: '32px',\n    borderRadius: '50%',\n    border: 'none',\n    backgroundColor: '#f5f5f5',\n    color: '#666',\n    cursor: 'pointer',\n    display: 'flex',\n    alignItems: 'center',\n    justifyContent: 'center',\n    transition: 'all 0.2s ease',\n    outline: 'none'\n  },\n  modalContent: {\n    padding: '16px 20px 20px 20px',\n    display: 'flex',\n    flexDirection: 'column',\n    gap: '16px',\n    overflowY: 'auto',\n    WebkitOverflowScrolling: 'touch',\n    maxHeight: 'calc(80vh - 60px)'\n  },\n\n  // Mobile styles handled by CSS file\n  wristSizeContent: {\n    display: 'flex',\n    flexDirection: 'column',\n    alignItems: 'center',\n    gap: '12px'\n  },\n  wristSizeTitle: {\n    fontSize: '20px',\n    fontWeight: '700',\n    color: '#2D8C88',\n    margin: 0,\n    textAlign: 'center'\n  },\n  wristSizeSubtitle: {\n    fontSize: '14px',\n    color: '#666',\n    margin: 0,\n    textAlign: 'center',\n    lineHeight: '1.4'\n  },\n  genderSelection: {\n    display: 'flex',\n    gap: '12px',\n    width: '100%',\n    maxWidth: '300px'\n  },\n  genderButton: {\n    flex: 1,\n    padding: '12px 16px',\n    borderRadius: '10px',\n    fontSize: '14px',\n    fontWeight: '600',\n    cursor: 'pointer',\n    transition: 'all 0.2s ease',\n    border: '2px solid #e0e0e0',\n    backgroundColor: '#ffffff',\n    color: '#666',\n    outline: 'none'\n  },\n  genderButtonActive: {\n    backgroundColor: '#2D8C88',\n    color: '#ffffff',\n    borderColor: '#2D8C88',\n    boxShadow: '0 2px 8px rgba(45, 140, 136, 0.3)'\n  },\n  sliderContainer: {\n    width: '100%',\n    maxWidth: '300px',\n    display: 'flex',\n    flexDirection: 'column',\n    gap: '8px'\n  },\n  sliderLabel: {\n    fontSize: '16px',\n    fontWeight: '600',\n    color: '#333',\n    textAlign: 'center',\n    display: 'flex',\n    alignItems: 'center',\n    justifyContent: 'center',\n    gap: '8px'\n  },\n  sizeChange: {\n    fontSize: '14px',\n    fontWeight: '500',\n    color: '#2D8C88',\n    backgroundColor: 'rgba(45, 140, 136, 0.1)',\n    padding: '2px 8px',\n    borderRadius: '12px'\n  },\n  slider: {\n    width: '100%',\n    height: '6px',\n    borderRadius: '3px',\n    background: '#e0e0e0',\n    outline: 'none',\n    cursor: 'pointer',\n    WebkitAppearance: 'none',\n    appearance: 'none'\n  },\n  sliderLabels: {\n    display: 'flex',\n    justifyContent: 'space-between',\n    fontSize: '12px',\n    color: '#999',\n    marginTop: '4px'\n  },\n  presetButtons: {\n    display: 'flex',\n    gap: '8px',\n    width: '100%',\n    maxWidth: '300px'\n  },\n  presetButton: {\n    flex: 1,\n    padding: '8px 12px',\n    borderRadius: '8px',\n    fontSize: '12px',\n    fontWeight: '500',\n    cursor: 'pointer',\n    transition: 'all 0.2s ease',\n    border: '1px solid #e0e0e0',\n    backgroundColor: '#ffffff',\n    color: '#666',\n    outline: 'none'\n  },\n  continueButton: {\n    width: '100%',\n    maxWidth: '300px',\n    padding: '14px 24px',\n    backgroundColor: '#2D8C88',\n    color: '#ffffff',\n    borderRadius: '12px',\n    fontSize: '16px',\n    fontWeight: '600',\n    cursor: 'pointer',\n    transition: 'all 0.2s ease',\n    border: 'none',\n    outline: 'none',\n    boxShadow: '0 4px 12px rgba(45, 140, 136, 0.3)'\n  },\n\n  productSelection: {\n    position: 'absolute',\n    bottom: 0,\n    left: 0,\n    right: 0,\n    backgroundColor: 'rgba(255, 255, 255, 0.98)',\n    backdropFilter: 'blur(20px)',\n    WebkitBackdropFilter: 'blur(20px)',\n    borderTopLeftRadius: '24px',\n    borderTopRightRadius: '24px',\n    padding: '16px',\n    maxHeight: '85vh',\n    display: 'flex',\n    flexDirection: 'column',\n    zIndex: 20,\n    boxShadow: '0 -4px 20px rgba(0, 0, 0, 0.15)',\n    border: 'none',\n    transform: 'translateY(0)',\n    transition: 'transform 0.3s ease-out',\n    overflow: 'hidden',\n    touchAction: 'none',\n    willChange: 'transform',\n    userSelect: 'none',\n    WebkitUserSelect: 'none'\n  },\n  closeBtn: {\n    position: 'absolute',\n    top: '12px',\n    right: '16px',\n    color: '#666',\n    cursor: 'pointer',\n    zIndex: 21,\n    width: '32px',\n    height: '32px',\n    display: 'flex',\n    alignItems: 'center',\n    justifyContent: 'center',\n    borderRadius: '50%',\n    backgroundColor: 'rgba(0, 0, 0, 0.1)',\n    transition: 'all 0.2s ease',\n    border: 'none',\n    outline: 'none',\n    padding: 0\n  },\n  productTabs: {\n    display: 'flex',\n    marginBottom: '16px',\n    backgroundColor: '#f0f0f0',\n    borderRadius: '12px',\n    padding: '4px',\n    gap: '4px'\n  },\n  tab: {\n    flex: 1,\n    textAlign: 'center',\n    padding: '12px 16px',\n    borderRadius: '8px',\n    fontSize: '16px',\n    fontWeight: '600',\n    cursor: 'pointer',\n    transition: 'all 0.2s ease',\n    color: '#666',\n    outline: 'none',\n    border: 'none',\n    backgroundColor: 'transparent',\n    WebkitTapHighlightColor: 'transparent',\n    touchAction: 'manipulation'\n  },\n  activeTab: {\n    backgroundColor: '#2D8C88',\n    color: '#ffffff',\n    boxShadow: '0 1px 4px rgba(45, 140, 136, 0.3)'\n  },\n  productScroll: {\n    display: 'grid',\n    gridTemplateColumns: 'repeat(auto-fill, minmax(100px, 1fr))',\n    gap: '12px',\n    maxHeight: 'calc(85vh - 120px)',\n    overflowY: 'auto',\n    paddingBottom: '16px',\n    scrollbarWidth: 'thin',\n    scrollbarColor: '#ddd #f5f5f5',\n    WebkitOverflowScrolling: 'touch'\n  },\n  productItem: {\n    position: 'relative',\n    width: '100%',\n    aspectRatio: '1/1',\n    backgroundColor: '#ffffff',\n    borderRadius: '16px',\n    display: 'flex',\n    alignItems: 'center',\n    justifyContent: 'center',\n    cursor: 'pointer',\n    transition: 'all 0.2s ease',\n    border: '1px solid #e0e0e0',\n    overflow: 'hidden',\n    boxShadow: '0 2px 8px rgba(0, 0, 0, 0.08)',\n    padding: '8px',\n    outline: 'none',\n    WebkitTapHighlightColor: 'transparent',\n    touchAction: 'manipulation'\n  },\n  productImage: {\n    width: '100%',\n    height: '100%',\n    objectFit: 'contain',\n    borderRadius: '8px',\n    backgroundColor: '#ffffff'\n  },\n  productLabel: {\n    position: 'absolute',\n    bottom: '3px',\n    left: '3px',\n    right: '3px',\n    fontSize: '9px',\n    color: '#666',\n    textAlign: 'center',\n    backgroundColor: 'rgba(255, 255, 255, 0.95)',\n    borderRadius: '3px',\n    padding: '3px 2px',\n    overflow: 'hidden'\n  },\n  productName: {\n    fontSize: '9px',\n    fontWeight: '600',\n    whiteSpace: 'nowrap',\n    textOverflow: 'ellipsis',\n    overflow: 'hidden',\n    marginBottom: '1px'\n  },\n  productSize: {\n    fontSize: '8px',\n    fontWeight: '500',\n    color: '#2D8C88',\n    whiteSpace: 'nowrap'\n  },\n  dragHandle: {\n    width: '40px',\n    height: '4px',\n    backgroundColor: '#e0e0e0',\n    borderRadius: '2px',\n    margin: '0 auto 12px',\n    cursor: 'grab',\n    touchAction: 'none',\n    userSelect: 'none',\n    WebkitUserSelect: 'none'\n  }\n};\n\nexport default Tryon;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,MAAM,QAAQ,OAAO;AAC1D,SAASC,MAAM,QAAQ,eAAe;AACtC,SAASC,SAAS,EAAEC,QAAQ,EAAEC,SAAS,EAAEC,IAAI,EAAEC,GAAG,EAAEC,MAAM,QAAQ,cAAc;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAEjF,MAAMC,KAAK,GAAGA,CAAC;EAAEC;AAAa,CAAC,KAAK;EAAAC,EAAA;EAClC;EACA,MAAMC,QAAQ,GAAGf,MAAM,CAAC,IAAI,CAAC;EAC7B,MAAMgB,gBAAgB,GAAGhB,MAAM,CAAC,IAAI,CAAC;EACrC,MAAMiB,SAAS,GAAGjB,MAAM,CAAC,IAAI,CAAC;;EAE9B;EACA,MAAM,CAACkB,UAAU,EAAEC,aAAa,CAAC,GAAGrB,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAACsB,eAAe,EAAEC,kBAAkB,CAAC,GAAGvB,QAAQ,CAAC,IAAI,CAAC;EAC5D,MAAM,CAACwB,WAAW,EAAEC,cAAc,CAAC,GAAGzB,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAAC0B,oBAAoB,EAAEC,uBAAuB,CAAC,GAAG3B,QAAQ,CAAC,KAAK,CAAC;EACvE,MAAM,CAAC4B,SAAS,EAAEC,YAAY,CAAC,GAAG7B,QAAQ,CAAC,SAAS,CAAC;EACrD,MAAM,CAAC8B,aAAa,EAAEC,gBAAgB,CAAC,GAAG/B,QAAQ,CAAC,IAAI,CAAC;EACxD,MAAM,CAACgC,QAAQ,EAAEC,WAAW,CAAC,GAAGjC,QAAQ,CAAC,KAAK,CAAC;;EAE/C;EACA,MAAM,CAACkC,UAAU,EAAEC,aAAa,CAAC,GAAGnC,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC;EACrD,MAAM,CAACoC,aAAa,EAAEC,gBAAgB,CAAC,GAAGrC,QAAQ,CAAC,EAAE,CAAC,CAAC,CAAC;EACxD,MAAM,CAACsC,kBAAkB,EAAEC,qBAAqB,CAAC,GAAGvC,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC;;EAErE;EACA,MAAM,CAACwC,oBAAoB,EAAEC,uBAAuB,CAAC,GAAGzC,QAAQ,CAAC,KAAK,CAAC;EACvE,MAAM,CAAC0C,SAAS,EAAEC,YAAY,CAAC,GAAG3C,QAAQ,CAAC,CAAC,CAAC;EAC7C,MAAM,CAAC4C,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG7C,QAAQ,CAAC,KAAK,CAAC;EAC/D,MAAM,CAAC8C,iBAAiB,EAAEC,oBAAoB,CAAC,GAAG/C,QAAQ,CAAC,KAAK,CAAC;;EAEjE;EACA,MAAM,CAACgD,aAAa,EAAEC,gBAAgB,CAAC,GAAGjD,QAAQ,CAAC,CAAC,CAAC;EACrD,MAAM,CAACkD,UAAU,EAAEC,aAAa,CAAC,GAAGnD,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAACoD,MAAM,EAAEC,SAAS,CAAC,GAAGrD,QAAQ,CAAC,CAAC,CAAC;EACvC,MAAMsD,QAAQ,GAAGpD,MAAM,CAAC,IAAI,CAAC;;EAE7B;EACAD,SAAS,CAAC,MAAM;IACd,MAAMsD,WAAW,GAAGA,CAAA,KAAM;MACxBtB,WAAW,CAACuB,MAAM,CAACC,UAAU,IAAI,GAAG,CAAC;IACvC,CAAC;;IAED;IACA,MAAMC,KAAK,GAAGA,CAAA,KAAM;MAClB,MAAMC,EAAE,GAAGH,MAAM,CAACI,WAAW,GAAG,IAAI;MACpCC,QAAQ,CAACC,eAAe,CAACC,KAAK,CAACC,WAAW,CAAC,MAAM,EAAE,GAAGL,EAAE,IAAI,CAAC;IAC/D,CAAC;IAEDJ,WAAW,CAAC,CAAC;IACbG,KAAK,CAAC,CAAC;IAEPF,MAAM,CAACS,gBAAgB,CAAC,QAAQ,EAAE,MAAM;MACtCV,WAAW,CAAC,CAAC;MACbG,KAAK,CAAC,CAAC;IACT,CAAC,CAAC;IAEFF,MAAM,CAACS,gBAAgB,CAAC,mBAAmB,EAAE,MAAM;MACjDC,UAAU,CAAC,MAAM;QACfR,KAAK,CAAC,CAAC;MACT,CAAC,EAAE,GAAG,CAAC;IACT,CAAC,CAAC;IAEF,OAAO,MAAM;MACXF,MAAM,CAACW,mBAAmB,CAAC,QAAQ,EAAEZ,WAAW,CAAC;MACjDC,MAAM,CAACW,mBAAmB,CAAC,mBAAmB,EAAET,KAAK,CAAC;IACxD,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA;EACA;EACA;EACA;;EAEA;EACA,MAAMU,mBAAmB,GAAG;IAC1BC,GAAG,EAAE,EAAE;IAAK;IACZC,KAAK,EAAE,EAAE,CAAG;EACd,CAAC;EAED,MAAMC,yBAAyB,GAAG,GAAG,CAAC,CAAC;EACvC,MAAMC,iBAAiB,GAAG,GAAG,CAAC,CAAC;EAC/B,MAAMC,kBAAkB,GAAG,GAAG,CAAC,CAAC;;EAEhC;EACA,MAAMC,WAAW,GAAG,EAAE,CAAC,CAAC;EACxB,MAAMC,cAAc,GAAG,EAAE,CAAC,CAAC;EAC3B,MAAMC,YAAY,GAAG,EAAE,CAAC,CAAC;EACzB,MAAMC,eAAe,GAAG,EAAE,CAAC,CAAC;;EAE5B;EACA;EACA;EACA;EACA;;EAEA;EACA,MAAMC,wBAAwB,GAAGA,CAACC,KAAK,EAAEC,cAAc,EAAEC,eAAe,KAAK;IAC3E;IACA,MAAMC,gBAAgB,GAAGd,mBAAmB,CAAClC,UAAU,CAAC;;IAExD;IACA,MAAMiD,cAAc,GAAG/C,aAAa,GAAG8C,gBAAgB;;IAEvD;IACA,MAAME,YAAY,GAAGb,yBAAyB,GAAGnC,aAAa;;IAE9D;IACA,MAAMiD,aAAa,GAAGN,KAAK,CAACO,UAAU,GAAGF,YAAY;IACrD,MAAMG,cAAc,GAAGR,KAAK,CAACS,WAAW,GAAGJ,YAAY;IACvD,MAAMK,eAAe,GAAGV,KAAK,CAACW,YAAY,GAAGN,YAAY;;IAEzD;IACA,MAAMO,iBAAiB,GAAIN,aAAa,GAAGb,iBAAiB,GAAI,GAAG;IACnE,MAAMoB,kBAAkB,GAAIL,cAAc,GAAGd,kBAAkB,GAAI,GAAG;IACtE,MAAMoB,mBAAmB,GAAIJ,eAAe,GAAGjB,iBAAiB,GAAI,GAAG;;IAEvE;IACA;IACA,MAAMsB,SAAS,GAAG,EAAE,CAAC,CAAC;IACtB,MAAMC,SAAS,GAAG,EAAE,CAAC,CAAC;;IAEtB,OAAO;MACLC,KAAK,EAAEC,IAAI,CAACC,GAAG,CAACP,iBAAiB,EAAE,CAAC,CAAC;MAAE;MACvCQ,MAAM,EAAEF,IAAI,CAACC,GAAG,CAACN,kBAAkB,EAAE,EAAE,CAAC;MAAE;MAC1CF,YAAY,EAAEG,mBAAmB;MACjCC,SAAS;MACTC,SAAS;MACTK,KAAK,EAAEH,IAAI,CAACI,GAAG,CAACV,iBAAiB,GAAG,EAAE,EAAEC,kBAAkB,GAAG,EAAE,CAAC,GAAGT,cAAc;MAAE;MACnFmB,SAAS,EAAEvB,KAAK,CAACO,UAAU;MAC3BiB,UAAU,EAAExB,KAAK,CAACS,WAAW;MAC7BgB,YAAY,EAAEzB,KAAK,CAACyB,YAAY;MAChCrB,cAAc,CAAC;IACjB,CAAC;EACH,CAAC;;EAED;EACA,MAAMsB,gBAAgB,GAAGA,CAACC,SAAS,EAAElF,WAAW,KAAK;IACnD,MAAMmF,cAAc,GAAG7B,wBAAwB,CAAC4B,SAAS,EAAE,GAAG,EAAE,GAAG,CAAC;;IAEpE;IACA;IACA,IAAIE,SAAS,GAAGD,cAAc,CAACb,SAAS;IACxC,IAAIe,SAAS,GAAGF,cAAc,CAACZ,SAAS,GAAG,CAAC,CAAC,CAAC;;IAE9C;IACA,IAAIvE,WAAW,EAAE;MACfoF,SAAS,GAAGD,cAAc,CAACb,SAAS,GAAG,CAAC,CAAC,CAAC;IAC5C;;IAEA;IACA,QAAQY,SAAS,CAACI,IAAI;MACpB,KAAK,YAAY;QACf;QACAD,SAAS,IAAI,CAAC;QACd;MACF,KAAK,QAAQ;QACX;QACAA,SAAS,IAAI,GAAG;QAChB;MACF,KAAK,OAAO;QACV;QACAA,SAAS,IAAI,GAAG;QAChB;MACF;QACE;IACJ;IAEA,OAAO;MACL,GAAGF,cAAc;MACjBb,SAAS,EAAEc,SAAS;MACpBb,SAAS,EAAEc;IACb,CAAC;EACH,CAAC;EACD;EACA,MAAME,OAAO,GAAG,CACd;IACEC,IAAI,EAAE,eAAe;IACrBC,IAAI,EAAE,0BAA0B;IAChC;IACAT,YAAY,EAAE,EAAE;IAAE;IAClBU,aAAa,EAAE,IAAI;IAAE;IACrB5B,UAAU,EAAE,EAAE;IAAE;IAChBE,WAAW,EAAE,EAAE;IAAE;IACjBE,YAAY,EAAE,EAAE;IAAE;IAClBoB,IAAI,EAAE,OAAO;IACbK,QAAQ,EAAE;EACZ,CAAC,EACD;IACEH,IAAI,EAAE,eAAe;IACrBC,IAAI,EAAE,0BAA0B;IAChC;IACAT,YAAY,EAAE,EAAE;IAAE;IAClBU,aAAa,EAAE,IAAI;IAAE;IACrB5B,UAAU,EAAE,EAAE;IAAE;IAChBE,WAAW,EAAE,IAAI;IAAE;IACnBE,YAAY,EAAE,EAAE;IAAE;IAClBoB,IAAI,EAAE,OAAO;IACbK,QAAQ,EAAE;EACZ,CAAC,EACD;IACEH,IAAI,EAAE,aAAa;IACnBC,IAAI,EAAE,0BAA0B;IAChC;IACAT,YAAY,EAAE,EAAE;IAAE;IAClBU,aAAa,EAAE,GAAG;IAAE;IACpB5B,UAAU,EAAE,EAAE;IAAE;IAChBE,WAAW,EAAE,EAAE;IAAE;IACjBE,YAAY,EAAE,EAAE;IAAE;IAClBoB,IAAI,EAAE,QAAQ;IACdK,QAAQ,EAAE;EACZ,CAAC,EACD;IACEH,IAAI,EAAE,YAAY;IAClBC,IAAI,EAAE,0BAA0B;IAChC;IACAT,YAAY,EAAE,EAAE;IAAE;IAClBU,aAAa,EAAE,IAAI;IAAE;IACrB5B,UAAU,EAAE,EAAE;IAAE;IAChBE,WAAW,EAAE,EAAE;IAAE;IACjBE,YAAY,EAAE,EAAE;IAAE;IAClBoB,IAAI,EAAE,YAAY;IAClBK,QAAQ,EAAE;EACZ,CAAC,EACD;IACEH,IAAI,EAAE,YAAY;IAClBC,IAAI,EAAE,0BAA0B;IAChC;IACAT,YAAY,EAAE,EAAE;IAAE;IAClBU,aAAa,EAAE,CAAC;IAAE;IAClB5B,UAAU,EAAE,EAAE;IAAE;IAChBE,WAAW,EAAE,EAAE;IAAE;IACjBE,YAAY,EAAE,EAAE;IAAE;IAClBoB,IAAI,EAAE,YAAY;IAClBK,QAAQ,EAAE;EACZ,CAAC,EACD;IACEH,IAAI,EAAE,WAAW;IACjBC,IAAI,EAAE,0BAA0B;IAChC;IACAT,YAAY,EAAE,EAAE;IAAE;IAClBU,aAAa,EAAE,EAAE;IAAE;IACnB5B,UAAU,EAAE,EAAE;IAAE;IAChBE,WAAW,EAAE,EAAE;IAAE;IACjBE,YAAY,EAAE,EAAE;IAAE;IAClBoB,IAAI,EAAE,SAAS;IACfK,QAAQ,EAAE;EACZ,CAAC,CACF;EAED,MAAMC,SAAS,GAAG,CAChB;IAAEJ,IAAI,EAAE,cAAc;IAAEC,IAAI,EAAE;EAA2B,CAAC,EAC1D;IAAED,IAAI,EAAE,aAAa;IAAEC,IAAI,EAAE;EAA2B,CAAC,EACzD;IAAED,IAAI,EAAE,cAAc;IAAEC,IAAI,EAAE;EAA2B,CAAC,EAC1D;IAAED,IAAI,EAAE,gBAAgB;IAAEC,IAAI,EAAE;EAA2B,CAAC,EAC5D;IAAED,IAAI,EAAE,cAAc;IAAEC,IAAI,EAAE;EAA2B,CAAC,EAC1D;IAAED,IAAI,EAAE,gBAAgB;IAAEC,IAAI,EAAE;EAA2B,CAAC,CAC7D;;EAED;EACA,MAAMI,UAAU,GAAG,MAAAA,CAAA,KAAY;IAC7B,IAAI;MACF,MAAMC,MAAM,GAAG,MAAMC,SAAS,CAACC,YAAY,CAACC,YAAY,CAAC;QACvDC,KAAK,EAAE;UACLC,UAAU,EAAE,aAAa;UACzB3B,KAAK,EAAE;YAAE4B,KAAK,EAAE;UAAK,CAAC;UACtBzB,MAAM,EAAE;YAAEyB,KAAK,EAAE;UAAK;QACxB;MACF,CAAC,CAAC;MACF,IAAI3G,QAAQ,CAAC4G,OAAO,EAAE;QACpB5G,QAAQ,CAAC4G,OAAO,CAACC,SAAS,GAAGR,MAAM;MACrC;IACF,CAAC,CAAC,OAAOS,GAAG,EAAE;MACZC,OAAO,CAACC,KAAK,CAAC,yBAAyB,EAAEF,GAAG,CAAC;MAC7C;MACA,IAAI7G,gBAAgB,CAAC2G,OAAO,EAAE;QAC5B3G,gBAAgB,CAAC2G,OAAO,CAACK,GAAG,GAAG,iBAAiB;QAChDhH,gBAAgB,CAAC2G,OAAO,CAAC9D,KAAK,CAACoE,OAAO,GAAG,OAAO;MAClD;MACAH,OAAO,CAACI,GAAG,CAAC,kCAAkC,CAAC;MAC/C/G,aAAa,CAAC,IAAI,CAAC;MACnBM,uBAAuB,CAAC,IAAI,CAAC;IAC/B;EACF,CAAC;;EAED;EACA,MAAM0G,YAAY,GAAGA,CAAA,KAAM;IACzB,IAAI,CAACpH,QAAQ,CAAC4G,OAAO,IAAI,CAAC1G,SAAS,CAAC0G,OAAO,EAAE,OAAO,IAAI;IAExD,MAAMS,MAAM,GAAGnH,SAAS,CAAC0G,OAAO;IAChC,MAAMH,KAAK,GAAGzG,QAAQ,CAAC4G,OAAO;IAC9BS,MAAM,CAACtC,KAAK,GAAG0B,KAAK,CAACa,UAAU;IAC/BD,MAAM,CAACnC,MAAM,GAAGuB,KAAK,CAACc,WAAW;IACjC,MAAMC,GAAG,GAAGH,MAAM,CAACI,UAAU,CAAC,IAAI,CAAC;IACnCD,GAAG,CAACE,SAAS,CAACjB,KAAK,EAAE,CAAC,EAAE,CAAC,EAAEY,MAAM,CAACtC,KAAK,EAAEsC,MAAM,CAACnC,MAAM,CAAC;IACvD,OAAOmC,MAAM,CAACM,SAAS,CAAC,WAAW,CAAC;EACtC,CAAC;;EAED;EACA,MAAMC,gBAAgB,GAAGA,CAACC,UAAU,EAAEC,WAAW,GAAG,OAAO,KAAK;IAC9D,MAAMC,GAAG,GAAG,IAAIC,KAAK,CAAC,CAAC;IACvBD,GAAG,CAACE,WAAW,GAAG,WAAW;IAE7BF,GAAG,CAACG,MAAM,GAAG,YAAW;MACtB,MAAMb,MAAM,GAAGzE,QAAQ,CAACuF,aAAa,CAAC,QAAQ,CAAC;MAC/C,MAAMX,GAAG,GAAGH,MAAM,CAACI,UAAU,CAAC,IAAI,CAAC;MACnCJ,MAAM,CAACtC,KAAK,GAAGgD,GAAG,CAACK,YAAY;MAC/Bf,MAAM,CAACnC,MAAM,GAAG6C,GAAG,CAACM,aAAa;MACjCb,GAAG,CAACE,SAAS,CAACK,GAAG,EAAE,CAAC,EAAE,CAAC,CAAC;MAExB,IAAI;QACF,MAAMO,SAAS,GAAGd,GAAG,CAACe,YAAY,CAAC,CAAC,EAAE,CAAC,EAAElB,MAAM,CAACtC,KAAK,EAAEsC,MAAM,CAACnC,MAAM,CAAC;QACrE,MAAMsD,IAAI,GAAGF,SAAS,CAACE,IAAI;QAC3B,MAAMzD,KAAK,GAAGsC,MAAM,CAACtC,KAAK;QAC1B,MAAMG,MAAM,GAAGmC,MAAM,CAACnC,MAAM;;QAE5B;QACA,MAAMuD,UAAU,GAAG,IAAIC,GAAG,CAAC,CAAC;QAC5B,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGzD,MAAM,GAAG,CAAC,EAAEyD,CAAC,EAAE,EAAE;UACnC,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG7D,KAAK,GAAG,CAAC,EAAE6D,CAAC,EAAE,EAAE;YAClC,MAAMC,GAAG,GAAG,CAACF,CAAC,GAAG5D,KAAK,GAAG6D,CAAC,IAAI,CAAC;YAC/B,MAAME,CAAC,GAAGN,IAAI,CAACK,GAAG,CAAC;YACnB,MAAME,CAAC,GAAGP,IAAI,CAACK,GAAG,GAAG,CAAC,CAAC;YACvB,MAAMG,CAAC,GAAGR,IAAI,CAACK,GAAG,GAAG,CAAC,CAAC;;YAEvB;YACA,IAAII,MAAM,GAAG,KAAK;YAClB,KAAK,IAAIC,EAAE,GAAG,CAAC,CAAC,EAAEA,EAAE,IAAI,CAAC,EAAEA,EAAE,EAAE,EAAE;cAC/B,KAAK,IAAIC,EAAE,GAAG,CAAC,CAAC,EAAEA,EAAE,IAAI,CAAC,EAAEA,EAAE,EAAE,EAAE;gBAC/B,IAAIA,EAAE,KAAK,CAAC,IAAID,EAAE,KAAK,CAAC,EAAE;gBAC1B,MAAME,WAAW,GAAG,CAAC,CAACT,CAAC,GAAGO,EAAE,IAAInE,KAAK,IAAI6D,CAAC,GAAGO,EAAE,CAAC,IAAI,CAAC;gBACrD,MAAME,EAAE,GAAGb,IAAI,CAACY,WAAW,CAAC;gBAC5B,MAAME,EAAE,GAAGd,IAAI,CAACY,WAAW,GAAG,CAAC,CAAC;gBAChC,MAAMG,EAAE,GAAGf,IAAI,CAACY,WAAW,GAAG,CAAC,CAAC;;gBAEhC;gBACA,MAAMI,SAAS,GAAGxE,IAAI,CAACyE,GAAG,CAACX,CAAC,GAAGO,EAAE,CAAC,GAAGrE,IAAI,CAACyE,GAAG,CAACV,CAAC,GAAGO,EAAE,CAAC,GAAGtE,IAAI,CAACyE,GAAG,CAACT,CAAC,GAAGO,EAAE,CAAC;gBACxE,IAAIC,SAAS,GAAG,EAAE,EAAE;kBAClBP,MAAM,GAAG,IAAI;kBACb;gBACF;cACF;cACA,IAAIA,MAAM,EAAE;YACd;YAEA,IAAIA,MAAM,EAAE;cACVR,UAAU,CAACiB,GAAG,CAACb,GAAG,GAAG,CAAC,CAAC;YACzB;UACF;QACF;;QAEA;QACA,KAAK,IAAIc,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGnB,IAAI,CAACoB,MAAM,EAAED,CAAC,IAAI,CAAC,EAAE;UACvC,MAAMb,CAAC,GAAGN,IAAI,CAACmB,CAAC,CAAC;UACjB,MAAMZ,CAAC,GAAGP,IAAI,CAACmB,CAAC,GAAG,CAAC,CAAC;UACrB,MAAMX,CAAC,GAAGR,IAAI,CAACmB,CAAC,GAAG,CAAC,CAAC;UACrB,MAAME,UAAU,GAAGF,CAAC,GAAG,CAAC;;UAExB;UACA,MAAMG,UAAU,GAAG,CAAChB,CAAC,GAAGC,CAAC,GAAGC,CAAC,IAAI,CAAC;;UAElC;UACA,MAAMe,UAAU,GAAGtB,UAAU,CAACuB,GAAG,CAACH,UAAU,CAAC;;UAE7C;UACA,MAAMI,WAAW,GACfnB,CAAC,GAAG,GAAG,IACPC,CAAC,GAAG,GAAG,IACPC,CAAC,GAAG,GAAG,IACPhE,IAAI,CAACyE,GAAG,CAACX,CAAC,GAAGC,CAAC,CAAC,GAAG,EAAE,IACpB/D,IAAI,CAACyE,GAAG,CAACV,CAAC,GAAGC,CAAC,CAAC,GAAG,EAAE,IACpB,CAACe,UACF;;UAED;UACA,IAAIE,WAAW,EAAE;YACfzB,IAAI,CAACmB,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;UACnB,CAAC,MAAM,IAAIG,UAAU,GAAG,GAAG,IAAI,CAACC,UAAU,EAAE;YAC1C;YACAvB,IAAI,CAACmB,CAAC,GAAG,CAAC,CAAC,GAAG3E,IAAI,CAACC,GAAG,CAAC,CAAC,EAAEuD,IAAI,CAACmB,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,CAAC;UAC7C;QACF;QAEAnC,GAAG,CAAC0C,YAAY,CAAC5B,SAAS,EAAE,CAAC,EAAE,CAAC,CAAC;QACjCT,UAAU,CAACZ,GAAG,GAAGI,MAAM,CAACM,SAAS,CAAC,WAAW,CAAC;;QAE9C;QACAE,UAAU,CAAC/E,KAAK,CAACqH,MAAM,GAAG,MAAM;QAChCtC,UAAU,CAAC/E,KAAK,CAACsH,YAAY,GAAG,QAAQ;QACxCvC,UAAU,CAAC/E,KAAK,CAACuH,OAAO,GAAG,GAAG;MAEhC,CAAC,CAAC,OAAOC,CAAC,EAAE;QACVvD,OAAO,CAACwD,IAAI,CAAC,0BAA0B,EAAED,CAAC,CAAC;QAC3C;QACAzC,UAAU,CAAC/E,KAAK,CAACqH,MAAM,GAAG,MAAM;QAChCtC,UAAU,CAAC/E,KAAK,CAACsH,YAAY,GAAG,QAAQ;QACxCvC,UAAU,CAAC/E,KAAK,CAACuH,OAAO,GAAG,GAAG;MAChC;IACF,CAAC;IAEDtC,GAAG,CAACyC,OAAO,GAAG,YAAW;MACvBzD,OAAO,CAACwD,IAAI,CAAC,sBAAsB,CAAC;MACpC;MACA1C,UAAU,CAAC/E,KAAK,CAACqH,MAAM,GAAG,MAAM;MAChCtC,UAAU,CAAC/E,KAAK,CAACsH,YAAY,GAAG,QAAQ;MACxCvC,UAAU,CAAC/E,KAAK,CAACuH,OAAO,GAAG,GAAG;IAChC,CAAC;IAEDtC,GAAG,CAACd,GAAG,GAAGY,UAAU,CAACZ,GAAG;EAC1B,CAAC;;EAED;EACA,MAAMwD,qBAAqB,GAAInC,SAAS,IAAK;IAC3C;IACA,OAAOtD,IAAI,CAAC0F,MAAM,CAAC,CAAC,GAAG,GAAG;EAC5B,CAAC;;EAED;EACA,MAAMC,oBAAoB,GAAGA,CAAA,KAAM;IACjC,IAAI,CAAC3K,QAAQ,CAAC4G,OAAO,IAAI,CAAC1G,SAAS,CAAC0G,OAAO,EAAE,OAAO,KAAK;IAEzD,MAAMH,KAAK,GAAGzG,QAAQ,CAAC4G,OAAO;IAC9B,MAAMS,MAAM,GAAGnH,SAAS,CAAC0G,OAAO;IAChC,MAAMY,GAAG,GAAGH,MAAM,CAACI,UAAU,CAAC,IAAI,CAAC;;IAEnC;IACAJ,MAAM,CAACtC,KAAK,GAAG0B,KAAK,CAACa,UAAU;IAC/BD,MAAM,CAACnC,MAAM,GAAGuB,KAAK,CAACc,WAAW;;IAEjC;IACAC,GAAG,CAACE,SAAS,CAACjB,KAAK,EAAE,CAAC,EAAE,CAAC,EAAEY,MAAM,CAACtC,KAAK,EAAEsC,MAAM,CAACnC,MAAM,CAAC;;IAEvD;IACA,MAAM0F,cAAc,GAAGnE,KAAK,CAACoE,aAAa;IAC1C,MAAMC,aAAa,GAAGF,cAAc,CAACG,qBAAqB,CAAC,CAAC;;IAE5D;IACA,MAAMC,WAAW,GAAGvE,KAAK,CAACa,UAAU,GAAGb,KAAK,CAACc,WAAW;IACxD,MAAM0D,eAAe,GAAGH,aAAa,CAAC/F,KAAK,GAAG+F,aAAa,CAAC5F,MAAM;IAElE,IAAIgG,YAAY,EAAEC,aAAa,EAAEC,OAAO,EAAEC,OAAO;IAEjD,IAAIL,WAAW,GAAGC,eAAe,EAAE;MACjC;MACAE,aAAa,GAAGL,aAAa,CAAC5F,MAAM;MACpCgG,YAAY,GAAGC,aAAa,GAAGH,WAAW;MAC1CI,OAAO,GAAG,CAACF,YAAY,GAAGJ,aAAa,CAAC/F,KAAK,IAAI,CAAC;MAClDsG,OAAO,GAAG,CAAC;IACb,CAAC,MAAM;MACL;MACAH,YAAY,GAAGJ,aAAa,CAAC/F,KAAK;MAClCoG,aAAa,GAAGD,YAAY,GAAGF,WAAW;MAC1CI,OAAO,GAAG,CAAC;MACXC,OAAO,GAAG,CAACF,aAAa,GAAGL,aAAa,CAAC5F,MAAM,IAAI,CAAC;IACtD;;IAEA;IACA;IACA;IACA;;IAEA,MAAMoG,MAAM,GAAGjE,MAAM,CAACtC,KAAK,GAAGmG,YAAY;IAC1C,MAAMK,MAAM,GAAGlE,MAAM,CAACnC,MAAM,GAAGiG,aAAa;;IAE5C;IACA,MAAMK,KAAK,GAAGxG,IAAI,CAACC,GAAG,CAAC,CAAC,EAAE,CAAE,GAAG,GAAG,GAAG,GAAIiG,YAAY,GAAGE,OAAO,IAAIE,MAAM,CAAC;IAC1E,MAAMG,KAAK,GAAGzG,IAAI,CAACC,GAAG,CAAC,CAAC,EAAE,CAAE,GAAG,GAAG,GAAG,GAAIkG,aAAa,GAAGE,OAAO,IAAIE,MAAM,CAAC;IAC3E,MAAMG,SAAS,GAAG1G,IAAI,CAACI,GAAG,CAACiC,MAAM,CAACtC,KAAK,GAAGyG,KAAK,EAAI,GAAG,GAAG,GAAG,GAAIN,YAAY,GAAII,MAAM,CAAC;IACvF,MAAMK,UAAU,GAAG3G,IAAI,CAACI,GAAG,CAACiC,MAAM,CAACnC,MAAM,GAAGuG,KAAK,EAAI,GAAG,GAAG,GAAG,GAAIN,aAAa,GAAII,MAAM,CAAC;;IAE1F;IACA,MAAMK,OAAO,GAAG5G,IAAI,CAACC,GAAG,CAAC,CAAC,EAAE,CAAE,GAAG,GAAG,GAAG,GAAIiG,YAAY,GAAGE,OAAO,IAAIE,MAAM,CAAC,CAAC,CAAC;IAC9E,MAAMO,OAAO,GAAG7G,IAAI,CAACC,GAAG,CAAC,CAAC,EAAE,CAAE,GAAG,GAAG,GAAG,GAAIkG,aAAa,GAAGE,OAAO,IAAIE,MAAM,CAAC,CAAC,CAAC;IAC/E,MAAMO,WAAW,GAAG9G,IAAI,CAACI,GAAG,CAACiC,MAAM,CAACtC,KAAK,GAAG6G,OAAO,EAAI,GAAG,GAAG,GAAG,GAAIV,YAAY,GAAII,MAAM,CAAC,CAAC,CAAC;IAC7F,MAAMS,YAAY,GAAG/G,IAAI,CAACI,GAAG,CAACiC,MAAM,CAACnC,MAAM,GAAG2G,OAAO,EAAI,GAAG,GAAG,GAAG,GAAIV,aAAa,GAAII,MAAM,CAAC,CAAC,CAAC;;IAEhG,IAAI;MACF;MACA,MAAMS,aAAa,GAAGxE,GAAG,CAACe,YAAY,CAACiD,KAAK,EAAEC,KAAK,EAAEC,SAAS,EAAEC,UAAU,CAAC;MAC3E,MAAMM,QAAQ,GAAGD,aAAa,CAACxD,IAAI;;MAEnC;MACA,MAAM0D,eAAe,GAAG1E,GAAG,CAACe,YAAY,CAACqD,OAAO,EAAEC,OAAO,EAAEC,WAAW,EAAEC,YAAY,CAAC;MACrF,MAAMI,UAAU,GAAGD,eAAe,CAAC1D,IAAI;MAEvC,IAAI4D,cAAc,GAAG,CAAC;MACtB,IAAIC,eAAe,GAAG,CAAC;MACvB,IAAIC,gBAAgB,GAAG,CAAC;MACxB,IAAIC,iBAAiB,GAAG,CAAC;;MAEzB;MACA,MAAMC,UAAU,GAAGA,CAAC1D,CAAC,EAAEC,CAAC,EAAEC,CAAC,KAAK;QAC9B;QACA,MAAMyD,UAAU,GAAG3D,CAAC,GAAG,EAAE,IAAIC,CAAC,GAAG,EAAE,IAAIC,CAAC,GAAG,EAAE,IAAIF,CAAC,GAAGC,CAAC,IAAID,CAAC,GAAGE,CAAC,IAAIhE,IAAI,CAACyE,GAAG,CAACX,CAAC,GAAGC,CAAC,CAAC,GAAG,EAAE;QACvF,MAAM2D,UAAU,GAAG5D,CAAC,GAAG,GAAG,IAAIC,CAAC,GAAG,GAAG,IAAIC,CAAC,GAAG,GAAG,IAAIhE,IAAI,CAACyE,GAAG,CAACX,CAAC,GAAGC,CAAC,CAAC,GAAG,EAAE,IAAI/D,IAAI,CAACyE,GAAG,CAACV,CAAC,GAAGC,CAAC,CAAC,GAAG,EAAE;QAChG,MAAM2D,UAAU,GAAG7D,CAAC,GAAG,GAAG,IAAIC,CAAC,GAAG,EAAE,IAAIC,CAAC,GAAG,EAAE,IAAIF,CAAC,GAAGC,CAAC,IAAIA,CAAC,GAAGC,CAAC;QAChE,MAAM4D,UAAU,GAAG9D,CAAC,GAAG,GAAG,IAAIC,CAAC,GAAG,GAAG,IAAIC,CAAC,GAAG,EAAE,IAAIF,CAAC,GAAGC,CAAC,IAAIA,CAAC,IAAIC,CAAC;QAElE,OAAOyD,UAAU,IAAIC,UAAU,IAAIC,UAAU,IAAIC,UAAU;MAC7D,CAAC;;MAED;MACA,KAAK,IAAIjD,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGsC,QAAQ,CAACrC,MAAM,EAAED,CAAC,IAAI,CAAC,EAAE;QAC3C,MAAMb,CAAC,GAAGmD,QAAQ,CAACtC,CAAC,CAAC;QACrB,MAAMZ,CAAC,GAAGkD,QAAQ,CAACtC,CAAC,GAAG,CAAC,CAAC;QACzB,MAAMX,CAAC,GAAGiD,QAAQ,CAACtC,CAAC,GAAG,CAAC,CAAC;QAEzB,IAAI6C,UAAU,CAAC1D,CAAC,EAAEC,CAAC,EAAEC,CAAC,CAAC,EAAE;UACvBoD,cAAc,EAAE;QAClB;QACAC,eAAe,EAAE;MACnB;;MAEA;MACA,KAAK,IAAI1C,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGwC,UAAU,CAACvC,MAAM,EAAED,CAAC,IAAI,CAAC,EAAE;QAC7C,MAAMb,CAAC,GAAGqD,UAAU,CAACxC,CAAC,CAAC;QACvB,MAAMZ,CAAC,GAAGoD,UAAU,CAACxC,CAAC,GAAG,CAAC,CAAC;QAC3B,MAAMX,CAAC,GAAGmD,UAAU,CAACxC,CAAC,GAAG,CAAC,CAAC;QAE3B,IAAI6C,UAAU,CAAC1D,CAAC,EAAEC,CAAC,EAAEC,CAAC,CAAC,EAAE;UACvBsD,gBAAgB,EAAE;QACpB;QACAC,iBAAiB,EAAE;MACrB;;MAEA;MACA,MAAMM,aAAa,GAAGT,cAAc,GAAGC,eAAe;MACtD,MAAMS,eAAe,GAAGR,gBAAgB,GAAGC,iBAAiB;;MAE5D;MACA;MACA;MACA,MAAMQ,eAAe,GAAGF,aAAa,GAAG,IAAI;MAC5C,MAAMG,cAAc,GAAGF,eAAe,GAAG,IAAI;MAE7C,OAAOC,eAAe,IAAIC,cAAc;IAE1C,CAAC,CAAC,OAAOhG,KAAK,EAAE;MACdD,OAAO,CAACwD,IAAI,CAAC,uBAAuB,EAAEvD,KAAK,CAAC;MAC5C,OAAO,KAAK;IACd;EACF,CAAC;;EAED;EACA,MAAMiG,2BAA2B,GAAGA,CAACC,WAAW,EAAEpF,WAAW,KAAK;IAChE;IACAxH,kBAAkB,CAAC,IAAI,CAAC;;IAExB;IACA,MAAMmF,SAAS,GAAGK,OAAO,CAACqH,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACpH,IAAI,KAAKkH,WAAW,CAAC;;IAE3D;IACAjK,UAAU,CAAC,MAAM;MACf3C,kBAAkB,CAAC;QACjB0F,IAAI,EAAEkH,WAAW;QACjBhH,QAAQ,EAAE,CAAAT,SAAS,aAATA,SAAS,uBAATA,SAAS,CAAES,QAAQ,KAAI,EAAE,CAAC;MACtC,CAAC,CAAC;IACJ,CAAC,EAAE,EAAE,CAAC;EACR,CAAC;;EAED;EACA,MAAMmH,aAAa,GAAGA,CAAA,KAAM;IAC1B,IAAI,CAAClN,UAAU,EAAE;MACf,MAAMmN,eAAe,GAAGlG,YAAY,CAAC,CAAC;MACtC,IAAInH,gBAAgB,CAAC2G,OAAO,IAAI0G,eAAe,EAAE;QAC/CrN,gBAAgB,CAAC2G,OAAO,CAACK,GAAG,GAAGqG,eAAe;QAC9CrN,gBAAgB,CAAC2G,OAAO,CAAC9D,KAAK,CAACoE,OAAO,GAAG,OAAO;MAClD;MACA9G,aAAa,CAAC,IAAI,CAAC;MACnBM,uBAAuB,CAAC,IAAI,CAAC,CAAC,CAAC;MAC/BI,gBAAgB,CAAC,KAAK,CAAC;;MAEvB;MACA,IAAIZ,SAAS,CAAC0G,OAAO,IAAI5G,QAAQ,CAAC4G,OAAO,EAAE;QACzC,MAAMS,MAAM,GAAGnH,SAAS,CAAC0G,OAAO;QAChC,MAAMY,GAAG,GAAGH,MAAM,CAACI,UAAU,CAAC,IAAI,CAAC;QACnCJ,MAAM,CAACtC,KAAK,GAAG/E,QAAQ,CAAC4G,OAAO,CAACU,UAAU;QAC1CD,MAAM,CAACnC,MAAM,GAAGlF,QAAQ,CAAC4G,OAAO,CAACW,WAAW;QAC5CC,GAAG,CAACE,SAAS,CAAC1H,QAAQ,CAAC4G,OAAO,EAAE,CAAC,EAAE,CAAC,CAAC;QACrC,MAAM0B,SAAS,GAAGd,GAAG,CAACe,YAAY,CAAC,CAAC,EAAE,CAAC,EAAElB,MAAM,CAACtC,KAAK,EAAEsC,MAAM,CAACnC,MAAM,CAAC;QACrE1E,cAAc,CAACiK,qBAAqB,CAACnC,SAAS,CAAC,CAAC;MAClD;IACF,CAAC,MAAM;MACL5H,uBAAuB,CAAC,IAAI,CAAC;IAC/B;EACF,CAAC;;EAED;EACA,MAAM6M,UAAU,GAAGA,CAAA,KAAM;IACvB,IAAItN,gBAAgB,CAAC2G,OAAO,EAAE;MAC5B3G,gBAAgB,CAAC2G,OAAO,CAAC9D,KAAK,CAACoE,OAAO,GAAG,MAAM;IACjD;IACA9G,aAAa,CAAC,KAAK,CAAC;IACpBE,kBAAkB,CAAC,IAAI,CAAC;IACxBE,cAAc,CAAC,KAAK,CAAC;IACrBE,uBAAuB,CAAC,KAAK,CAAC;IAC9BY,qBAAqB,CAAC,KAAK,CAAC;IAC5BR,gBAAgB,CAAC,IAAI,CAAC;EACxB,CAAC;;EAED;EACA,MAAM0M,kBAAkB,GAAIC,MAAM,IAAK;IACrCvM,aAAa,CAACuM,MAAM,CAAC;IACrBrM,gBAAgB,CAAC+B,mBAAmB,CAACsK,MAAM,CAAC,CAAC;EAC/C,CAAC;;EAED;EACA,MAAMC,qBAAqB,GAAIC,IAAI,IAAK;IACtCvM,gBAAgB,CAACuM,IAAI,CAAC;EACxB,CAAC;;EAED;EACA;EACA;EACA;EACA;;EAEA;EACA,MAAMC,eAAe,GAAIC,OAAO,IAAK;IACnCjN,YAAY,CAACiN,OAAO,CAAC;EACvB,CAAC;;EAED;EACA,MAAMC,mBAAmB,GAAIC,OAAO,IAAK;IACvCd,2BAA2B,CAACc,OAAO,CAAC/H,IAAI,EAAErF,SAAS,CAAC;EACtD,CAAC;;EAED;EACA3B,SAAS,CAAC,MAAM;IACdoH,UAAU,CAAC,CAAC;EACd,CAAC,EAAE,EAAE,CAAC;;EAEN;EACApH,SAAS,CAAC,MAAM;IACd,IAAI,CAACuC,oBAAoB,IAAIpB,UAAU,EAAE;IAEzC,MAAM6N,QAAQ,GAAGC,WAAW,CAAC,MAAM;MACjC,MAAMjB,cAAc,GAAGrC,oBAAoB,CAAC,CAAC;MAC7C/I,mBAAmB,CAACoL,cAAc,CAAC;;MAEnC;MACA,IAAIA,cAAc,IAAI,CAACnL,iBAAiB,IAAIJ,SAAS,KAAK,CAAC,EAAE;QAC3DK,oBAAoB,CAAC,IAAI,CAAC;MAC5B;;MAEA;MACA,IAAI,CAACkL,cAAc,IAAInL,iBAAiB,EAAE;QACxCC,oBAAoB,CAAC,KAAK,CAAC;QAC3BJ,YAAY,CAAC,CAAC,CAAC;MACjB;IACF,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC;;IAET,OAAO,MAAMwM,aAAa,CAACF,QAAQ,CAAC;EACtC,CAAC,EAAE,CAACzM,oBAAoB,EAAEpB,UAAU,EAAE0B,iBAAiB,EAAEJ,SAAS,CAAC,CAAC;;EAEpE;EACAzC,SAAS,CAAC,MAAM;IACd,IAAI,CAAC6C,iBAAiB,IAAI1B,UAAU,EAAE;MACpCuB,YAAY,CAAC,CAAC,CAAC;MACf;IACF;;IAEA;IACA,IAAI,CAACC,gBAAgB,EAAE;MACrBG,oBAAoB,CAAC,KAAK,CAAC;MAC3BJ,YAAY,CAAC,CAAC,CAAC;MACf;IACF;;IAEA;IACAA,YAAY,CAAC,CAAC,CAAC;IAEf,MAAMyM,iBAAiB,GAAGF,WAAW,CAAC,MAAM;MAC1CvM,YAAY,CAAC0M,IAAI,IAAI;QACnB;QACA,IAAI,CAACzM,gBAAgB,EAAE;UACrBuM,aAAa,CAACC,iBAAiB,CAAC;UAChCrM,oBAAoB,CAAC,KAAK,CAAC;UAC3B,OAAO,CAAC;QACV;QAEA,IAAIsM,IAAI,IAAI,CAAC,EAAE;UACb;UACAF,aAAa,CAACC,iBAAiB,CAAC;UAChCrM,oBAAoB,CAAC,KAAK,CAAC;UAC3BN,uBAAuB,CAAC,KAAK,CAAC,CAAC,CAAC;UAChC6L,aAAa,CAAC,CAAC;UACf,OAAO,CAAC;QACV;QACA,OAAOe,IAAI,GAAG,CAAC;MACjB,CAAC,CAAC;IACJ,CAAC,EAAE,IAAI,CAAC;IAER,OAAO,MAAMF,aAAa,CAACC,iBAAiB,CAAC;EAC/C,CAAC,EAAE,CAACtM,iBAAiB,EAAE1B,UAAU,EAAEwB,gBAAgB,CAAC,CAAC;;EAErD;EACA,MAAM0M,uBAAuB,GAAGA,CAAA,KAAM;IACpC,MAAMC,QAAQ,GAAG,CAAC/M,oBAAoB;IACtCC,uBAAuB,CAAC8M,QAAQ,CAAC;;IAEjC;IACA,IAAI,CAACA,QAAQ,EAAE;MACbxM,oBAAoB,CAAC,KAAK,CAAC;MAC3BJ,YAAY,CAAC,CAAC,CAAC;MACfE,mBAAmB,CAAC,KAAK,CAAC;IAC5B;EACF,CAAC;;EAED;EACA,MAAM2M,mBAAmB,GAAGA,CAAA,KAAM;IAChC/M,uBAAuB,CAAC,KAAK,CAAC;IAC9BM,oBAAoB,CAAC,KAAK,CAAC;IAC3BJ,YAAY,CAAC,CAAC,CAAC;IACfE,mBAAmB,CAAC,KAAK,CAAC;IAC1BN,qBAAqB,CAAC,KAAK,CAAC;IAC5BJ,aAAa,CAAC,KAAK,CAAC;IACpBE,gBAAgB,CAAC,EAAE,CAAC;IACpBmM,UAAU,CAAC,CAAC;EACd,CAAC;;EAED;EACA,MAAMiB,kBAAkB,GAAGA,CAAA,KAAM;IAC/B,OAAO7N,SAAS,KAAK,SAAS,GAAGmF,OAAO,GAAGK,SAAS;EACtD,CAAC;;EAED;EACA,MAAMsI,gBAAgB,GAAInE,CAAC,IAAK;IAC9BpI,aAAa,CAAC,IAAI,CAAC;IACnBE,SAAS,CAACkI,CAAC,CAACoE,OAAO,CAAC,CAAC,CAAC,CAACC,OAAO,CAAC;EACjC,CAAC;EAED,MAAMC,eAAe,GAAItE,CAAC,IAAK;IAC7B,IAAI,CAACrI,UAAU,EAAE;IAEjB,MAAM4M,QAAQ,GAAGvE,CAAC,CAACoE,OAAO,CAAC,CAAC,CAAC,CAACC,OAAO;IACrC,MAAMG,IAAI,GAAGD,QAAQ,GAAG1M,MAAM;;IAE9B;IACA,IAAI2M,IAAI,GAAG,CAAC,EAAE;MACZ9M,gBAAgB,CAAC8M,IAAI,CAAC;IACxB;EACF,CAAC;EAED,MAAMC,cAAc,GAAGA,CAAA,KAAM;IAC3B,IAAI,CAAC9M,UAAU,EAAE;IAEjBC,aAAa,CAAC,KAAK,CAAC;;IAEpB;IACA,IAAIH,aAAa,GAAG,GAAG,EAAE;MACvBrB,uBAAuB,CAAC,KAAK,CAAC;IAChC;;IAEA;IACAsB,gBAAgB,CAAC,CAAC,CAAC;EACrB,CAAC;;EAED;EACAhD,SAAS,CAAC,MAAM;IACd,MAAMgQ,kBAAkB,GAAI1E,CAAC,IAAK;MAChC,IAAIjJ,kBAAkB,IAAI,CAACiJ,CAAC,CAAC2E,MAAM,CAACC,OAAO,CAAC,gBAAgB,CAAC,EAAE;QAC7D5N,qBAAqB,CAAC,KAAK,CAAC;MAC9B;IACF,CAAC;IAEDsB,QAAQ,CAACI,gBAAgB,CAAC,WAAW,EAAEgM,kBAAkB,CAAC;IAC1DpM,QAAQ,CAACI,gBAAgB,CAAC,YAAY,EAAEgM,kBAAkB,CAAC;IAE3D,OAAO,MAAM;MACXpM,QAAQ,CAACM,mBAAmB,CAAC,WAAW,EAAE8L,kBAAkB,CAAC;MAC7DpM,QAAQ,CAACM,mBAAmB,CAAC,YAAY,EAAE8L,kBAAkB,CAAC;IAChE,CAAC;EACH,CAAC,EAAE,CAAC3N,kBAAkB,CAAC,CAAC;;EAExB;EACA,oBACE3B,OAAA;IAAKyP,SAAS,EAAC,sEAAsE;IAAAC,QAAA,gBACnF1P,OAAA;MAAKyP,SAAS,EAAC,2EAA2E;MAAAC,QAAA,gBACxF1P,OAAA;QACE2P,GAAG,EAAErP,QAAS;QACdmP,SAAS,EAAC,4BAA4B;QACtCG,QAAQ;QACRC,WAAW;QACXC,KAAK;MAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,eACFlQ,OAAA;QAAQ2P,GAAG,EAAEnP,SAAU;QAACiP,SAAS,EAAC;MAAQ;QAAAM,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAC7ClQ,OAAA;QACE2P,GAAG,EAAEpP,gBAAiB;QACtBkP,SAAS,EAAC,yDAAyD;QACnEU,GAAG,EAAC;MAAe;QAAAJ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACpB,CAAC,EAGD,CAACzP,UAAU,iBACVT,OAAA;QACEyP,SAAS,EAAC,iSAAiS;QAC3SW,OAAO,EAAEhQ,YAAa;QACtB,cAAW,MAAM;QAAAsP,QAAA,eAEjB1P,OAAA,CAACJ,IAAI;UAAC6P,SAAS,EAAC;QAAS;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACtB,CACT,EAGA,CAACzP,UAAU,iBACVT,OAAA;QAAKyP,SAAS,EAAC,wDAAwD;QAAAC,QAAA,gBACrE1P,OAAA;UAAOyP,SAAS,EAAC,wEAAwE;UAAAC,QAAA,EAAC;QAAY;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eAC9GlQ,OAAA;UAAOyP,SAAS,EAAC,kDAAkD;UAAAC,QAAA,gBACjE1P,OAAA;YACEmG,IAAI,EAAC,UAAU;YACfkK,OAAO,EAAExO,oBAAqB;YAC9ByO,QAAQ,EAAE3B,uBAAwB;YAClC4B,QAAQ,EAAEpO,iBAAkB;YAC5B,cAAW,qBAAqB;YAChCsN,SAAS,EAAC;UAAS;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpB,CAAC,eACFlQ,OAAA;YAAKyP,SAAS,EAAE,iEACd5N,oBAAoB,GAAG,kBAAkB,GAAG,aAAa,EACxD;YAAA6N,QAAA,eACD1P,OAAA;cAAKyP,SAAS,EAAE,yFACd5N,oBAAoB,GAAG,eAAe,GAAG,eAAe,EACvD;cAAA6N,QAAA,EACA7N,oBAAoB,gBACnB7B,OAAA;gBAAKyP,SAAS,EAAC,kDAAkD;gBAACe,OAAO,EAAC,WAAW;gBAAAd,QAAA,eACnF1P,OAAA;kBAAUyQ,MAAM,EAAC,kBAAkB;kBAACC,IAAI,EAAC,MAAM;kBAACC,MAAM,EAAC,cAAc;kBAACC,WAAW,EAAC,GAAG;kBAACC,aAAa,EAAC,OAAO;kBAACC,cAAc,EAAC;gBAAO;kBAAAf,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAC;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjI,CAAC,gBAENlQ,OAAA;gBAAKyP,SAAS,EAAC,6CAA6C;gBAACe,OAAO,EAAC,WAAW;gBAAAd,QAAA,gBAC9E1P,OAAA;kBAAM+Q,EAAE,EAAC,GAAG;kBAACC,EAAE,EAAC,GAAG;kBAACC,EAAE,EAAC,GAAG;kBAACC,EAAE,EAAC,GAAG;kBAACP,MAAM,EAAC,cAAc;kBAACC,WAAW,EAAC,GAAG;kBAACC,aAAa,EAAC;gBAAO;kBAAAd,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAC,CAAC,eAC/FlQ,OAAA;kBAAM+Q,EAAE,EAAC,GAAG;kBAACC,EAAE,EAAC,GAAG;kBAACC,EAAE,EAAC,GAAG;kBAACC,EAAE,EAAC,GAAG;kBAACP,MAAM,EAAC,cAAc;kBAACC,WAAW,EAAC,GAAG;kBAACC,aAAa,EAAC;gBAAO;kBAAAd,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5F;YACN;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CACN,EAGAzP,UAAU,iBACTT,OAAA;QACEyP,SAAS,EAAC,iSAAiS;QAC3SW,OAAO,EAAEvB,mBAAoB;QAC7B,cAAW,MAAM;QAAAa,QAAA,eAEjB1P,OAAA,CAACP,SAAS;UAACgQ,SAAS,EAAC;QAAS;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC3B,CACT,EAGA/N,iBAAiB,iBAChBnC,OAAA;QAAKyP,SAAS,EAAC,wFAAwF;QAAAC,QAAA,gBACrG1P,OAAA;UAAKyP,SAAS,EAAC,+DAA+D;UAAAC,QAAA,EAAE3N;QAAS;UAAAgO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eAChGlQ,OAAA;UAAKyP,SAAS,EAAC,8EAA8E;UAAAC,QAAA,EAAC;QAAiB;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAClH,CACN,EAGArO,oBAAoB,IAAI,CAACI,gBAAgB,IAAI,CAACE,iBAAiB,iBAC9DnC,OAAA;QAAKyP,SAAS,EAAC,0FAA0F;QAAAC,QAAA,gBACvG1P,OAAA;UAAKyP,SAAS,EAAC,8DAA8D;UAAAC,QAAA,EAAC;QAE9E;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACNlQ,OAAA;UAAKyP,SAAS,EAAC,wDAAwD;UAAAC,QAAA,EAAC;QAExE;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN,EAEArO,oBAAoB,IAAII,gBAAgB,IAAI,CAACE,iBAAiB,iBAC7DnC,OAAA;QAAKyP,SAAS,EAAC,yEAAyE;QAAAC,QAAA,eACtF1P,OAAA;UAAKyP,SAAS,EAAC,iEAAiE;UAAAC,QAAA,EAAC;QAEjF;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN,EAGA/O,aAAa,iBACZnB,OAAA;QACEoD,KAAK,EAAE;UACL,GAAG+N,MAAM,CAACC,SAAS;UACnBzG,OAAO,EAAE9I,oBAAoB,IAAII,gBAAgB,GAAG,GAAG,GAAG,GAAG;UAC7DwI,MAAM,EAAE5I,oBAAoB,IAAII,gBAAgB,GAC5C,gDAAgD,GAChDJ,oBAAoB,IAAI,CAACI,gBAAgB,GACvC,iDAAiD,GACjD;QACR,CAAE;QACFwN,SAAS,EAAEpO,QAAQ,GAAG,mBAAmB,GAAG,EAAG;QAC/C,eAAY,MAAM;QAAAqO,QAAA,eAElB1P,OAAA;UAAKwQ,OAAO,EAAC,aAAa;UAACa,KAAK,EAAC,4BAA4B;UAAA3B,QAAA,gBAE3D1P,OAAA;YACEsR,CAAC,EAAC,4EAA4E;YAC9EX,MAAM,EACJ9O,oBAAoB,IAAII,gBAAgB,GACpC,SAAS,GACTJ,oBAAoB,IAAI,CAACI,gBAAgB,GACvC,SAAS,GACT,OACP;YACD2O,WAAW,EAAC,GAAG;YACfF,IAAI,EAAC,MAAM;YACXG,aAAa,EAAC;UAAO;YAAAd,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtB,CAAC,eACFlQ,OAAA;YACEsR,CAAC,EAAC,4EAA4E;YAC9EX,MAAM,EACJ9O,oBAAoB,IAAII,gBAAgB,GACpC,SAAS,GACTJ,oBAAoB,IAAI,CAACI,gBAAgB,GACvC,SAAS,GACT,OACP;YACD2O,WAAW,EAAC,GAAG;YACfF,IAAI,EAAC,MAAM;YACXG,aAAa,EAAC;UAAO;YAAAd,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtB,CAAC,eAEFlQ,OAAA;YACEkJ,CAAC,EAAC,KAAK;YACPD,CAAC,EAAC,KAAK;YACP5D,KAAK,EAAC,KAAK;YACXG,MAAM,EAAC,KAAK;YACZkL,IAAI,EACF7O,oBAAoB,IAAII,gBAAgB,GACpC,SAAS,GACTJ,oBAAoB,IAAI,CAACI,gBAAgB,GACvC,SAAS,GACT,OACP;YACD0I,OAAO,EAAE9I,oBAAoB,IAAII,gBAAgB,GAAG,MAAM,GAAG,MAAO;YACpEsP,EAAE,EAAC,IAAI;YACPZ,MAAM,EACJ9O,oBAAoB,IAAII,gBAAgB,GACpC,SAAS,GACTJ,oBAAoB,IAAI,CAACI,gBAAgB,GACvC,SAAS,GACT,OACP;YACD2O,WAAW,EAAC;UAAG;YAAAb,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChB,CAAC,eAEFlQ,OAAA;YACEwR,EAAE,EAAC,KAAK;YACRC,EAAE,EAAC,KAAK;YACRrI,CAAC,EAAC,IAAI;YACNsH,IAAI,EACF7O,oBAAoB,IAAII,gBAAgB,GACpC,SAAS,GACTJ,oBAAoB,IAAI,CAACI,gBAAgB,GACvC,SAAS,GACT,OACP;YACD0I,OAAO,EAAE9I,oBAAoB,IAAII,gBAAgB,GAAG,KAAK,GAAG,KAAM;YAClE0O,MAAM,EACJ9O,oBAAoB,IAAII,gBAAgB,GACpC,SAAS,GACTJ,oBAAoB,IAAI,CAACI,gBAAgB,GACvC,SAAS,GACT,OACP;YACD2O,WAAW,EAAC;UAAG;YAAAb,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChB,CAAC,EAEDrO,oBAAoB,iBACnB7B,OAAA,CAAAE,SAAA;YAAAwP,QAAA,gBACE1P,OAAA;cAAMkJ,CAAC,EAAC,KAAK;cAACD,CAAC,EAAC,KAAK;cAACyI,UAAU,EAAC,QAAQ;cAAChB,IAAI,EAAC,OAAO;cAACiB,QAAQ,EAAC,IAAI;cAACC,UAAU,EAAC,MAAM;cAAAlC,QAAA,EAAC;YAEvF;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACPlQ,OAAA;cAAMkJ,CAAC,EAAC,KAAK;cAACD,CAAC,EAAC,KAAK;cAACyI,UAAU,EAAC,QAAQ;cAAChB,IAAI,EAAC,OAAO;cAACiB,QAAQ,EAAC,IAAI;cAACC,UAAU,EAAC,MAAM;cAAAlC,QAAA,EAAC;YAEvF;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA,eACP,CACH;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN,EAGAvP,eAAe,iBACdX,OAAA;QAAKoD,KAAK,EAAE;UACV,GAAG+N,MAAM,CAACU,eAAe;UACzBxM,KAAK,EAAEpE,SAAS,KAAK,SAAS,GAAG,GAAG8C,WAAW,GAAG,GAAG,GAAGC,cAAc,GAAG;UACzEwB,MAAM,EAAEvE,SAAS,KAAK,SAAS,GAAG,GAAGgD,YAAY,GAAG,GAAG,GAAGC,eAAe;QAC3E,CAAE;QAAAwL,QAAA,eACA1P,OAAA;UAAKoD,KAAK,EAAE;YACV0O,QAAQ,EAAE,UAAU;YACpBzM,KAAK,EAAE,MAAM;YACbG,MAAM,EAAE,MAAM;YACdgC,OAAO,EAAE,MAAM;YACfuK,UAAU,EAAE,QAAQ;YACpBC,cAAc,EAAE;UAClB,CAAE;UAAAtC,QAAA,gBACA1P,OAAA;YACEuH,GAAG,EAAE,OAAO5G,eAAe,KAAK,QAAQ,GAAGA,eAAe,CAAC2F,IAAI,GAAG3F,eAAgB;YAClFwP,GAAG,EAAC,kBAAkB;YACtB/M,KAAK,EAAE;cACLiC,KAAK,EAAE,MAAM;cACbG,MAAM,EAAE,MAAM;cACdyM,SAAS,EAAE,SAAS;cACpBC,SAAS,EAAEjR,SAAS,KAAK,WAAW,GAChC,uBAAuBiD,eAAe,GAAG,EAAE,IAAIrD,WAAW,GAAG,aAAa,GAAG,EAAE,EAAE,GACjF,SAAUoD,YAAY,GAAG,EAAE,IAAKxC,aAAa,GAAGgC,mBAAmB,CAAClC,UAAU,CAAC,CAAC,GAAG;cACvFkJ,MAAM,EAAE;YACV,CAAE;YACF0H,MAAM,EAAGvH,CAAC,IAAK1C,gBAAgB,CAAC0C,CAAC,CAAC2E,MAAM,EAAEtO,SAAS,KAAK,SAAS,GAAG,OAAO,GAAG,UAAU;UAAE;YAAA8O,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3F,CAAC,EACDjP,SAAS,KAAK,SAAS,IAAI,OAAON,eAAe,KAAK,QAAQ,iBAC7DX,OAAA;YAAKoD,KAAK,EAAE;cACV0O,QAAQ,EAAE,UAAU;cACpBM,MAAM,EAAE,OAAO;cACfC,IAAI,EAAE,KAAK;cACXH,SAAS,EAAE,kBAAkB;cAC7BP,QAAQ,EAAE,MAAM;cAChBC,UAAU,EAAE,KAAK;cACjBU,KAAK,EAAE,OAAO;cACdC,eAAe,EAAE,yBAAyB;cAC1CC,OAAO,EAAE,SAAS;cAClBC,YAAY,EAAE,MAAM;cACpBC,UAAU,EAAE,QAAQ;cACpBC,aAAa,EAAE,MAAM;cACrBC,SAAS,EAAE,2BAA2B;cACtCC,MAAM,EAAE;YACV,CAAE;YAAAnD,QAAA,GACC/O,eAAe,CAAC6F,QAAQ,EAAC,IAC1B,EAAC/E,aAAa,KAAKgC,mBAAmB,CAAClC,UAAU,CAAC,iBAChDvB,OAAA;cAAMoD,KAAK,EAAE;gBACXuO,QAAQ,EAAE,MAAM;gBAChBhH,OAAO,EAAE,GAAG;gBACZmI,UAAU,EAAE;cACd,CAAE;cAAApD,QAAA,GAAC,UACO,EAAC,CAAC,CAACjO,aAAa,GAAGgC,mBAAmB,CAAClC,UAAU,CAAC,GAAG,CAAC,IAAI,GAAG,EAAEwR,OAAO,CAAC,CAAC,CAAC,EAAC,IACpF;YAAA;cAAAhD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CACP;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CACN;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN,eAGDlQ,OAAA;QACEoD,KAAK,EAAE+N,MAAM,CAAC6B,UAAW;QACzBvD,SAAS,EAAEpO,QAAQ,GAAG,oBAAoB,GAAG,EAAG;QAChD+O,OAAO,EAAEzC,aAAc;QACvB,cAAYlN,UAAU,GAAG,iBAAiB,GAAG,SAAU;QAAAiP,QAAA,EAEtD,CAACjP,UAAU,gBACVT,OAAA;UAAKoD,KAAK,EAAE+N,MAAM,CAAC8B,YAAa;UAACxD,SAAS,EAAEpO,QAAQ,GAAG,qBAAqB,GAAG;QAAG;UAAA0O,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,gBAEzFlQ,OAAA;UAAKqF,KAAK,EAAC,IAAI;UAACG,MAAM,EAAC,IAAI;UAACgL,OAAO,EAAC,WAAW;UAACE,IAAI,EAAC,OAAO;UAAAhB,QAAA,eAC1D1P,OAAA;YAAMsR,CAAC,EAAC;UAA6D;YAAAvB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpE;MACN;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACK,CAAC,EAGRzP,UAAU,iBACTT,OAAA;QACEoD,KAAK,EAAE+N,MAAM,CAAC+B,QAAS;QACvB9C,OAAO,EAAEA,CAAA,KAAMvN,MAAM,CAACsQ,QAAQ,CAACC,MAAM,CAAC,CAAE;QACxC,cAAW,OAAO;QAAA1D,QAAA,eAElB1P,OAAA;UAAKqF,KAAK,EAAC,IAAI;UAACG,MAAM,EAAC,IAAI;UAACgL,OAAO,EAAC,WAAW;UAACE,IAAI,EAAC,OAAO;UAAAhB,QAAA,eAC1D1P,OAAA;YAAMsR,CAAC,EAAC;UAAyN;YAAAvB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChO;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACA,CACT;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,EAGLzP,UAAU,iBACTT,OAAA;MACEoD,KAAK,EAAE+N,MAAM,CAACkC,oBAAqB;MACnC5D,SAAS,EAAEpO,QAAQ,GAAG,YAAY,GAAG,EAAG;MACxC+O,OAAO,EAAEA,CAAA,KAAMxO,qBAAqB,CAAC,IAAI,CAAE;MAC3C,cAAW,mBAAmB;MAAA8N,QAAA,gBAE9B1P,OAAA;QAAKqF,KAAK,EAAC,IAAI;QAACG,MAAM,EAAC,IAAI;QAACgL,OAAO,EAAC,WAAW;QAACE,IAAI,EAAC,OAAO;QAAAhB,QAAA,eAC1D1P,OAAA;UAAMsR,CAAC,EAAC;QAA64B;UAAAvB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACp5B,CAAC,eACNlQ,OAAA;QAAMoD,KAAK,EAAE+N,MAAM,CAACmC,aAAc;QAAA5D,QAAA,GAAEjO,aAAa,EAAC,IAAE;MAAA;QAAAsO,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACrD,CACT,EAGAvO,kBAAkB,iBACjB3B,OAAA;MACEoD,KAAK,EAAE+N,MAAM,CAACoC,YAAa;MAC3BnD,OAAO,EAAEA,CAAA,KAAMxO,qBAAqB,CAAC,KAAK,CAAE;MAC5C6N,SAAS,EAAC,eAAe;MAAAC,QAAA,eAEzB1P,OAAA;QACEoD,KAAK,EAAE+N,MAAM,CAACqC,cAAe;QAC7BpD,OAAO,EAAGxF,CAAC,IAAKA,CAAC,CAAC6I,eAAe,CAAC,CAAE;QACpChE,SAAS,EAAC,eAAe;QAAAC,QAAA,gBAEzB1P,OAAA;UAAKoD,KAAK,EAAE+N,MAAM,CAACuC,WAAY;UAAAhE,QAAA,gBAC7B1P,OAAA;YAAIoD,KAAK,EAAE+N,MAAM,CAACwC,UAAW;YAAAjE,QAAA,EAAC;UAAiB;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACpDlQ,OAAA;YACEoD,KAAK,EAAE+N,MAAM,CAACyC,aAAc;YAC5BxD,OAAO,EAAEA,CAAA,KAAMxO,qBAAqB,CAAC,KAAK,CAAE;YAC5C,cAAW,OAAO;YAAA8N,QAAA,eAElB1P,OAAA;cAAKqF,KAAK,EAAC,IAAI;cAACG,MAAM,EAAC,IAAI;cAACgL,OAAO,EAAC,WAAW;cAACE,IAAI,EAAC,cAAc;cAAAhB,QAAA,eACjE1P,OAAA;gBAAMsR,CAAC,EAAC;cAAuG;gBAAAvB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9G;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,eAENlQ,OAAA;UAAKoD,KAAK,EAAE+N,MAAM,CAAC0C,YAAa;UAAAnE,QAAA,gBAE9B1P,OAAA;YAAKoD,KAAK,EAAE+N,MAAM,CAAC2C,eAAgB;YAAApE,QAAA,gBACjC1P,OAAA;cACEoD,KAAK,EAAE;gBACL,GAAG+N,MAAM,CAAC4C,YAAY;gBACtB,IAAIxS,UAAU,KAAK,KAAK,GAAG4P,MAAM,CAAC6C,kBAAkB,GAAG,CAAC,CAAC;cAC3D,CAAE;cACF5D,OAAO,EAAEA,CAAA,KAAMtC,kBAAkB,CAAC,KAAK,CAAE;cAAA4B,QAAA,EAC1C;YAED;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACTlQ,OAAA;cACEoD,KAAK,EAAE;gBACL,GAAG+N,MAAM,CAAC4C,YAAY;gBACtB,IAAIxS,UAAU,KAAK,OAAO,GAAG4P,MAAM,CAAC6C,kBAAkB,GAAG,CAAC,CAAC;cAC7D,CAAE;cACF5D,OAAO,EAAEA,CAAA,KAAMtC,kBAAkB,CAAC,OAAO,CAAE;cAAA4B,QAAA,EAC5C;YAED;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,eAGNlQ,OAAA;YAAKoD,KAAK,EAAE+N,MAAM,CAAC8C,eAAgB;YAAAvE,QAAA,gBACjC1P,OAAA;cAAOoD,KAAK,EAAE+N,MAAM,CAAC+C,WAAY;cAAAxE,QAAA,GAAC,cACpB,EAACjO,aAAa,EAAC,IAC3B,EAACA,aAAa,KAAKgC,mBAAmB,CAAClC,UAAU,CAAC,iBAChDvB,OAAA;gBAAMoD,KAAK,EAAE+N,MAAM,CAACgD,UAAW;gBAAAzE,QAAA,GAAC,GAC7B,EAACjO,aAAa,GAAGgC,mBAAmB,CAAClC,UAAU,CAAC,GAAG,GAAG,GAAG,EAAE,EAC3D,CAAC,CAACE,aAAa,GAAGgC,mBAAmB,CAAClC,UAAU,CAAC,GAAG,CAAC,IAAI,GAAG,EAAEwR,OAAO,CAAC,CAAC,CAAC,EAAC,IAC5E;cAAA;gBAAAhD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CACP;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI,CAAC,eACRlQ,OAAA;cACEmG,IAAI,EAAC,OAAO;cACZT,GAAG,EAAC,IAAI;cACRH,GAAG,EAAC,IAAI;cACR6O,KAAK,EAAE3S,aAAc;cACrB6O,QAAQ,EAAG1F,CAAC,IAAKoD,qBAAqB,CAACqG,QAAQ,CAACzJ,CAAC,CAAC2E,MAAM,CAAC6E,KAAK,CAAC,CAAE;cACjEhR,KAAK,EAAE+N,MAAM,CAACmD;YAAO;cAAAvE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtB,CAAC,eACFlQ,OAAA;cAAKoD,KAAK,EAAE+N,MAAM,CAACoD,YAAa;cAAA7E,QAAA,gBAC9B1P,OAAA;gBAAA0P,QAAA,EAAM;cAAI;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACjBlQ,OAAA;gBAAA0P,QAAA,EAAM;cAAI;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACd,CAAC,eAGNlQ,OAAA;cAAKoD,KAAK,EAAE+N,MAAM,CAACqD,aAAc;cAAA9E,QAAA,gBAC/B1P,OAAA;gBACEoD,KAAK,EAAE+N,MAAM,CAACsD,YAAa;gBAC3BrE,OAAO,EAAEA,CAAA,KAAMpC,qBAAqB,CAAC,EAAE,CAAE;gBAAA0B,QAAA,EAC1C;cAED;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACTlQ,OAAA;gBACEoD,KAAK,EAAE+N,MAAM,CAACsD,YAAa;gBAC3BrE,OAAO,EAAEA,CAAA,KAAMpC,qBAAqB,CAACvK,mBAAmB,CAAClC,UAAU,CAAC,CAAE;gBAAAmO,QAAA,GACvE,WACU,EAACjM,mBAAmB,CAAClC,UAAU,CAAC,EAAC,KAC5C;cAAA;gBAAAwO,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACTlQ,OAAA;gBACEoD,KAAK,EAAE+N,MAAM,CAACsD,YAAa;gBAC3BrE,OAAO,EAAEA,CAAA,KAAMpC,qBAAqB,CAAC,EAAE,CAAE;gBAAA0B,QAAA,EAC1C;cAED;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN,EAGAnP,oBAAoB,iBACnBf,OAAA;MACE2P,GAAG,EAAEhN,QAAS;MACdS,KAAK,EAAE;QACL,GAAG+N,MAAM,CAACuD,gBAAgB;QAC1BxC,SAAS,EAAE,cAAc7P,aAAa,KAAK;QAC3CsS,WAAW,EAAE;MACf,CAAE;MACFlF,SAAS,EAAEpO,QAAQ,GAAG,sBAAsB,GAAG,EAAG;MAClD,cAAW,MAAM;MACjBuT,IAAI,EAAC,QAAQ;MACbC,YAAY,EAAE9F,gBAAiB;MAC/B+F,WAAW,EAAE5F,eAAgB;MAC7B6F,UAAU,EAAE1F,cAAe;MAAAK,QAAA,gBAE3B1P,OAAA;QACEoD,KAAK,EAAE+N,MAAM,CAAC6D,UAAW;QACzB,eAAY,MAAM;QAClBH,YAAY,EAAE9F,gBAAiB;QAC/B+F,WAAW,EAAE5F,eAAgB;QAC7B6F,UAAU,EAAE1F;MAAe;QAAAU,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC5B,CAAC,eACFlQ,OAAA;QAAKoD,KAAK,EAAE+N,MAAM,CAAC8D,WAAY;QAAAvF,QAAA,gBAC7B1P,OAAA;UACEoD,KAAK,EAAE;YACL,GAAG+N,MAAM,CAAC+D,GAAG;YACb,IAAIjU,SAAS,KAAK,SAAS,GAAGkQ,MAAM,CAAClQ,SAAS,GAAG,CAAC,CAAC;UACrD,CAAE;UACFmP,OAAO,EAAEA,CAAA,KAAMlC,eAAe,CAAC,SAAS,CAAE;UAAAwB,QAAA,EAC3C;QAED;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACTlQ,OAAA;UACEoD,KAAK,EAAE;YACL,GAAG+N,MAAM,CAAC+D,GAAG;YACb,IAAIjU,SAAS,KAAK,WAAW,GAAGkQ,MAAM,CAAClQ,SAAS,GAAG,CAAC,CAAC;UACvD,CAAE;UACFmP,OAAO,EAAEA,CAAA,KAAMlC,eAAe,CAAC,WAAW,CAAE;UAAAwB,QAAA,EAC7C;QAED;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,eACNlQ,OAAA;QAAKoD,KAAK,EAAE+N,MAAM,CAACgE,aAAc;QAAC1F,SAAS,EAAC,gBAAgB;QAAAC,QAAA,EACzDZ,kBAAkB,CAAC,CAAC,CAACsG,GAAG,CAAC,CAAC/G,OAAO,EAAEgH,KAAK,KAAK;UAC5C;UACA,IAAI,CAAChH,OAAO,EAAE,OAAO,IAAI;UAEzB,MAAMiH,UAAU,GAAG,CAAC,OAAO3U,eAAe,KAAK,QAAQ,GAAGA,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAE2F,IAAI,GAAG3F,eAAe,MAAM0N,OAAO,CAAC/H,IAAI;UAEnH,oBACEtG,OAAA;YAEEoD,KAAK,EAAE;cACL,GAAG+N,MAAM,CAACoE,WAAW;cACrBC,WAAW,EAAEF,UAAU,GAAG,SAAS,GAAG,SAAS;cAC/C/C,eAAe,EAAE+C,UAAU,GAAG,SAAS,GAAG;YAC5C,CAAE;YACFG,KAAK,EAAE,GAAGpH,OAAO,CAAChI,IAAI,MAAMgI,OAAO,CAACxI,YAAY,IAAI,KAAK,IAAK;YAC9DuK,OAAO,EAAEA,CAAA,KAAMhC,mBAAmB,CAACC,OAAO,CAAE;YAC5C,cAAY,UAAUA,OAAO,CAAChI,IAAI,IAAIgI,OAAO,CAACxI,YAAY,IAAI,KAAK,IAAK;YAAA6J,QAAA,gBAExE1P,OAAA;cACEuH,GAAG,EAAE8G,OAAO,CAAC/H,IAAK;cAClB6J,GAAG,EAAE9B,OAAO,CAAChI,IAAK;cAClBjD,KAAK,EAAE+N,MAAM,CAACuE,YAAa;cAC3BC,OAAO,EAAG/K,CAAC,IAAK;gBACdA,CAAC,CAAC2E,MAAM,CAACpE,aAAa,CAAC/H,KAAK,CAACoE,OAAO,GAAG,MAAM;cAC/C;YAAE;cAAAuI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACFlQ,OAAA;cAAKoD,KAAK,EAAE+N,MAAM,CAACyE,YAAa;cAAAlG,QAAA,gBAC9B1P,OAAA;gBAAKoD,KAAK,EAAE+N,MAAM,CAAC0E,WAAY;gBAAAnG,QAAA,EAAErB,OAAO,CAAChI;cAAI;gBAAA0J,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,EACnDjP,SAAS,KAAK,SAAS,IAAIoN,OAAO,CAACxI,YAAY,iBAC9C7F,OAAA;gBAAKoD,KAAK,EAAE+N,MAAM,CAAC2E,WAAY;gBAAApG,QAAA,GAAErB,OAAO,CAACxI,YAAY,EAAC,IAAE;cAAA;gBAAAkK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAC9D;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC;UAAA,GAvBDmF,KAAK;YAAAtF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAwBJ,CAAC;QAEb,CAAC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;;AAED;AAAA7P,EAAA,CAtvCMF,KAAK;AAAA4V,EAAA,GAAL5V,KAAK;AAuvCX,MAAMgR,MAAM,GAAG;EACb6E,SAAS,EAAE;IACTlE,QAAQ,EAAE,UAAU;IACpBtM,MAAM,EAAE,4BAA4B;IACpCgC,OAAO,EAAE,MAAM;IACfyO,aAAa,EAAE,QAAQ;IACvB1D,eAAe,EAAE,SAAS;IAC1BD,KAAK,EAAE,MAAM;IACb4D,UAAU,EAAE,4EAA4E;IACxFC,QAAQ,EAAE,QAAQ;IAClBxB,WAAW,EAAE,cAAc;IAC3ByB,uBAAuB,EAAE,aAAa;IACtCC,uBAAuB,EAAE,OAAO,CAAC;EACnC,CAAC;EACDC,eAAe,EAAE;IACfC,IAAI,EAAE,CAAC;IACPzE,QAAQ,EAAE,UAAU;IACpBqE,QAAQ,EAAE,QAAQ;IAClB5D,eAAe,EAAE,MAAM;IACvB/K,OAAO,EAAE,MAAM;IACfuK,UAAU,EAAE,QAAQ;IACpBC,cAAc,EAAE;EAClB,CAAC;EACDwE,UAAU,EAAE;IACVnR,KAAK,EAAE,MAAM;IACbG,MAAM,EAAE,MAAM;IACdyM,SAAS,EAAE,OAAO;IAClBC,SAAS,EAAE,WAAW,CAAC;EACzB,CAAC;EACDuE,aAAa,EAAE;IACb3E,QAAQ,EAAE,UAAU;IACpB4E,GAAG,EAAE,CAAC;IACNrE,IAAI,EAAE,CAAC;IACPhN,KAAK,EAAE,MAAM;IACbG,MAAM,EAAE,MAAM;IACdyM,SAAS,EAAE,OAAO;IAClBzK,OAAO,EAAE,MAAM;IACfmP,eAAe,EAAE,WAAW,CAAC;EAC/B,CAAC;EAEDC,OAAO,EAAE;IACP9E,QAAQ,EAAE,UAAU;IACpB4E,GAAG,EAAE,MAAM;IACXrE,IAAI,EAAE,MAAM;IACZE,eAAe,EAAE,oBAAoB;IACrCD,KAAK,EAAE,OAAO;IACdE,OAAO,EAAE,MAAM;IACfC,YAAY,EAAE,KAAK;IACnBd,QAAQ,EAAE,MAAM;IAChBC,UAAU,EAAE,KAAK;IACjBiF,MAAM,EAAE,SAAS;IACjBhE,MAAM,EAAE,EAAE;IACViE,MAAM,EAAE,MAAM;IACdlE,SAAS,EAAE,+BAA+B;IAC1CmE,UAAU,EAAE,eAAe;IAC3BvP,OAAO,EAAE,MAAM;IACfuK,UAAU,EAAE,QAAQ;IACpBC,cAAc,EAAE,QAAQ;IACxB3M,KAAK,EAAE,MAAM;IACbG,MAAM,EAAE,MAAM;IACdwR,OAAO,EAAE;EACX,CAAC;EACDC,OAAO,EAAE;IACPnF,QAAQ,EAAE,UAAU;IACpB4E,GAAG,EAAE,MAAM;IACXrE,IAAI,EAAE,MAAM;IACZE,eAAe,EAAE,oBAAoB;IACrCD,KAAK,EAAE,OAAO;IACdE,OAAO,EAAE,MAAM;IACfC,YAAY,EAAE,KAAK;IACnBd,QAAQ,EAAE,MAAM;IAChBC,UAAU,EAAE,KAAK;IACjBiF,MAAM,EAAE,SAAS;IACjBhE,MAAM,EAAE,EAAE;IACViE,MAAM,EAAE,MAAM;IACdlE,SAAS,EAAE,+BAA+B;IAC1CmE,UAAU,EAAE,eAAe;IAC3BvP,OAAO,EAAE,MAAM;IACfuK,UAAU,EAAE,QAAQ;IACpBC,cAAc,EAAE,QAAQ;IACxB3M,KAAK,EAAE,MAAM;IACbG,MAAM,EAAE,MAAM;IACdwR,OAAO,EAAE;EACX,CAAC;EACDE,eAAe,EAAE;IACfpF,QAAQ,EAAE,UAAU;IACpB4E,GAAG,EAAE,MAAM;IACXS,KAAK,EAAE,MAAM;IACbtE,MAAM,EAAE,EAAE;IACVrL,OAAO,EAAE,MAAM;IACfyO,aAAa,EAAE,QAAQ;IACvBlE,UAAU,EAAE,QAAQ;IACpBqF,GAAG,EAAE,KAAK;IACV5E,OAAO,EAAE,MAAM;IACfD,eAAe,EAAE,oBAAoB;IACrCE,YAAY,EAAE,MAAM;IACpB4E,cAAc,EAAE,YAAY;IAC5BC,oBAAoB,EAAE,YAAY;IAClC1E,SAAS,EAAE,+BAA+B;IAC1CkE,MAAM,EAAE;EACV,CAAC;EACDS,WAAW,EAAE;IACXzF,QAAQ,EAAE,UAAU;IACpB4E,GAAG,EAAE,MAAM;IACXrE,IAAI,EAAE,MAAM;IACZhN,KAAK,EAAE,MAAM;IACbG,MAAM,EAAE,MAAM;IACd+M,eAAe,EAAE,oBAAoB;IACrCE,YAAY,EAAE,MAAM;IACpBqE,MAAM,EAAE,oCAAoC;IAC5CjE,MAAM,EAAE,CAAC;IACTkE,UAAU,EAAE;EACd,CAAC;EACDS,YAAY,EAAE;IACZ1F,QAAQ,EAAE,UAAU;IACpBzM,KAAK,EAAE,MAAM;IACbG,MAAM,EAAE,MAAM;IACdiN,YAAY,EAAE,KAAK;IACnBqE,MAAM,EAAE,MAAM;IACdD,MAAM,EAAE,SAAS;IACjBE,UAAU,EAAE,eAAe;IAC3BvP,OAAO,EAAE,MAAM;IACfuK,UAAU,EAAE,QAAQ;IACpBC,cAAc,EAAE,QAAQ;IACxBa,MAAM,EAAE,EAAE;IACV4E,MAAM,EAAE,KAAK;IACb7E,SAAS,EAAE,8BAA8B;IACzCoE,OAAO,EAAE,MAAM;IACf,SAAS,EAAE;MACT9E,SAAS,EAAE;IACb;EACF,CAAC;EACDwF,WAAW,EAAE;IACX/F,QAAQ,EAAE,MAAM;IAChBC,UAAU,EAAE,KAAK;IACjBU,KAAK,EAAE,OAAO;IACdqF,UAAU,EAAE,8BAA8B;IAC1CC,SAAS,EAAE,KAAK;IAChBpF,OAAO,EAAE,UAAU;IACnBC,YAAY,EAAE,MAAM;IACpBF,eAAe,EAAE,oBAAoB;IACrCsF,aAAa,EAAE;EACjB,CAAC;EACDC,gBAAgB,EAAE;IAChBhG,QAAQ,EAAE,UAAU;IACpB4E,GAAG,EAAE,KAAK;IACVrE,IAAI,EAAE,KAAK;IACXH,SAAS,EAAE,uBAAuB;IAClCW,MAAM,EAAE,EAAE;IACVkF,SAAS,EAAE,QAAQ;IACnBpF,aAAa,EAAE,MAAM;IACrBH,OAAO,EAAE,WAAW;IACpBD,eAAe,EAAE,oBAAoB;IACrCE,YAAY,EAAE,MAAM;IACpB4E,cAAc,EAAE,YAAY;IAC5BC,oBAAoB,EAAE,YAAY;IAClC1E,SAAS,EAAE;EACb,CAAC;EACDoF,eAAe,EAAE;IACfrG,QAAQ,EAAE,MAAM;IAChBC,UAAU,EAAE,KAAK;IACjBU,KAAK,EAAE,SAAS;IAChBqF,UAAU,EAAE,8BAA8B;IAC1CM,YAAY,EAAE,KAAK;IACnBC,SAAS,EAAE;EACb,CAAC;EACDC,aAAa,EAAE;IACbxG,QAAQ,EAAE,MAAM;IAChBC,UAAU,EAAE,KAAK;IACjBU,KAAK,EAAE,OAAO;IACdqF,UAAU,EAAE;EACd,CAAC;EACDS,aAAa,EAAE;IACbtG,QAAQ,EAAE,UAAU;IACpB4E,GAAG,EAAE,KAAK;IACVrE,IAAI,EAAE,KAAK;IACXH,SAAS,EAAE,uBAAuB;IAClCW,MAAM,EAAE,EAAE;IACVkF,SAAS,EAAE,QAAQ;IACnBpF,aAAa,EAAE,MAAM;IACrBH,OAAO,EAAE,WAAW;IACpBC,YAAY,EAAE,MAAM;IACpBF,eAAe,EAAE,oBAAoB;IACrC8E,cAAc,EAAE,YAAY;IAC5BC,oBAAoB,EAAE,YAAY;IAClC1E,SAAS,EAAE;EACb,CAAC;EACDyF,UAAU,EAAE;IACV1G,QAAQ,EAAE,MAAM;IAChBC,UAAU,EAAE,KAAK;IACjBU,KAAK,EAAE,OAAO;IACdqF,UAAU,EAAE,8BAA8B;IAC1CpF,eAAe,EAAE,0BAA0B;IAC3CC,OAAO,EAAE,WAAW;IACpBC,YAAY,EAAE,MAAM;IACpBwF,YAAY,EAAE,KAAK;IACnBlB,UAAU,EAAE;EACd,CAAC;EACDuB,aAAa,EAAE;IACb3G,QAAQ,EAAE,MAAM;IAChBC,UAAU,EAAE,KAAK;IACjBU,KAAK,EAAE,OAAO;IACdqF,UAAU,EAAE,8BAA8B;IAC1CpF,eAAe,EAAE,oBAAoB;IACrCC,OAAO,EAAE,UAAU;IACnBC,YAAY,EAAE;EAChB,CAAC;EACDrB,SAAS,EAAE;IACTU,QAAQ,EAAE,UAAU;IACpB4E,GAAG,EAAE,KAAK;IACVrE,IAAI,EAAE,KAAK;IACXH,SAAS,EAAE,uBAAuB;IAClC7M,KAAK,EAAE,KAAK;IACZkT,QAAQ,EAAE,OAAO;IACjB/S,MAAM,EAAE,MAAM;IACdmF,OAAO,EAAE,GAAG;IACZgI,aAAa,EAAE,MAAM;IACrBE,MAAM,EAAE,CAAC;IACTpI,MAAM,EAAE,iDAAiD;IACzD+N,YAAY,EAAE,iDAAiD;IAC/DzB,UAAU,EAAE;EACd,CAAC;EACDlF,eAAe,EAAE;IACfC,QAAQ,EAAE,UAAU;IACpB4E,GAAG,EAAE,KAAK;IACVrE,IAAI,EAAE,KAAK;IACXH,SAAS,EAAE,uBAAuB;IAClCW,MAAM,EAAE,CAAC;IACTrL,OAAO,EAAE,MAAM;IACfuK,UAAU,EAAE,QAAQ;IACpBC,cAAc,EAAE,QAAQ;IACxBW,aAAa,EAAE,MAAM;IACrB8F,QAAQ,EAAE,OAAO;IAAE;IACnBC,SAAS,EAAE,OAAO,CAAC;EACrB,CAAC;EACD1F,UAAU,EAAE;IACVlB,QAAQ,EAAE,UAAU;IACpBM,MAAM,EAAE,MAAM;IACdC,IAAI,EAAE,KAAK;IACXH,SAAS,EAAE,kBAAkB;IAC7B7M,KAAK,EAAE,MAAM;IACbG,MAAM,EAAE,MAAM;IACd+M,eAAe,EAAE,0BAA0B;IAC3CE,YAAY,EAAE,KAAK;IACnBjL,OAAO,EAAE,MAAM;IACfuK,UAAU,EAAE,QAAQ;IACpBC,cAAc,EAAE,QAAQ;IACxB6E,MAAM,EAAE,SAAS;IACjBhE,MAAM,EAAE,EAAE;IACVkE,UAAU,EAAE,eAAe;IAC3BD,MAAM,EAAE,oCAAoC;IAC5ClE,SAAS,EAAE,+BAA+B;IAC1CoE,OAAO,EAAE,MAAM;IACfxE,OAAO,EAAE,CAAC;IACV4D,uBAAuB,EAAE,aAAa;IACtCzB,WAAW,EAAE;EACf,CAAC;EACD1B,YAAY,EAAE;IACZ5N,KAAK,EAAE,MAAM;IACbG,MAAM,EAAE,MAAM;IACd+M,eAAe,EAAE,SAAS;IAC1BE,YAAY,EAAE,KAAK;IACnBsE,UAAU,EAAE;EACd,CAAC;EACD7D,QAAQ,EAAE;IACRpB,QAAQ,EAAE,UAAU;IACpBM,MAAM,EAAE,MAAM;IACd+E,KAAK,EAAE,MAAM;IACb9R,KAAK,EAAE,MAAM;IACbG,MAAM,EAAE,MAAM;IACd+M,eAAe,EAAE,oBAAoB;IACrCE,YAAY,EAAE,KAAK;IACnBjL,OAAO,EAAE,MAAM;IACfuK,UAAU,EAAE,QAAQ;IACpBC,cAAc,EAAE,QAAQ;IACxB6E,MAAM,EAAE,SAAS;IACjBhE,MAAM,EAAE,EAAE;IACVkE,UAAU,EAAE,eAAe;IAC3BD,MAAM,EAAE,MAAM;IACdlE,SAAS,EAAE,+BAA+B;IAC1CoE,OAAO,EAAE,MAAM;IACfxE,OAAO,EAAE;EACX,CAAC;EAED;EACAa,oBAAoB,EAAE;IACpBvB,QAAQ,EAAE,UAAU;IACpB4E,GAAG,EAAE,MAAM;IACXS,KAAK,EAAE,MAAM;IACb5E,eAAe,EAAE,SAAS;IAC1BD,KAAK,EAAE,OAAO;IACdE,OAAO,EAAE,WAAW;IACpBC,YAAY,EAAE,MAAM;IACpBd,QAAQ,EAAE,MAAM;IAChBC,UAAU,EAAE,KAAK;IACjBiF,MAAM,EAAE,SAAS;IACjBC,MAAM,EAAE,MAAM;IACdtP,OAAO,EAAE,MAAM;IACfuK,UAAU,EAAE,QAAQ;IACpBqF,GAAG,EAAE,KAAK;IACVJ,OAAO,EAAE,MAAM;IACfD,UAAU,EAAE,eAAe;IAC3BlE,MAAM,EAAE,EAAE;IACVD,SAAS,EAAE,mCAAmC;IAC9C8F,SAAS,EAAE,MAAM;IACjBD,QAAQ,EAAE,MAAM;IAChBrC,uBAAuB,EAAE,aAAa;IACtCzB,WAAW,EAAE;EACf,CAAC;EACDrB,aAAa,EAAE;IACb3B,QAAQ,EAAE,MAAM;IAChBC,UAAU,EAAE;EACd,CAAC;EAED;EACA2B,YAAY,EAAE;IACZzB,QAAQ,EAAE,OAAO;IACjB4E,GAAG,EAAE,CAAC;IACNrE,IAAI,EAAE,CAAC;IACP8E,KAAK,EAAE,CAAC;IACR/E,MAAM,EAAE,CAAC;IACTG,eAAe,EAAE,oBAAoB;IACrC/K,OAAO,EAAE,MAAM;IACfuK,UAAU,EAAE,QAAQ;IACpBC,cAAc,EAAE,QAAQ;IACxBa,MAAM,EAAE,EAAE;IACVL,OAAO,EAAE,MAAM;IACf6E,cAAc,EAAE,WAAW;IAC3BC,oBAAoB,EAAE,WAAW;IACjC3C,WAAW,EAAE;EACf,CAAC;EACDnB,cAAc,EAAE;IACdjB,eAAe,EAAE,OAAO;IACxBE,YAAY,EAAE,MAAM;IACpBpN,KAAK,EAAE,MAAM;IACbkT,QAAQ,EAAE,MAAM;IAChBI,SAAS,EAAE,MAAM;IACjBxC,QAAQ,EAAE,QAAQ;IAClBvD,SAAS,EAAE,gCAAgC;IAC3CpL,OAAO,EAAE,MAAM;IACfyO,aAAa,EAAE,QAAQ;IACvBwB,MAAM,EAAE,MAAM;IACd3F,QAAQ,EAAE;EACZ,CAAC;EACD4B,WAAW,EAAE;IACXlM,OAAO,EAAE,MAAM;IACfuK,UAAU,EAAE,QAAQ;IACpBC,cAAc,EAAE,eAAe;IAC/BQ,OAAO,EAAE,qBAAqB;IAC9BoG,YAAY,EAAE;EAChB,CAAC;EACDjF,UAAU,EAAE;IACVhC,QAAQ,EAAE,MAAM;IAChBC,UAAU,EAAE,KAAK;IACjBU,KAAK,EAAE,SAAS;IAChBmF,MAAM,EAAE;EACV,CAAC;EACD7D,aAAa,EAAE;IACbvO,KAAK,EAAE,MAAM;IACbG,MAAM,EAAE,MAAM;IACdiN,YAAY,EAAE,KAAK;IACnBqE,MAAM,EAAE,MAAM;IACdvE,eAAe,EAAE,SAAS;IAC1BD,KAAK,EAAE,MAAM;IACbuE,MAAM,EAAE,SAAS;IACjBrP,OAAO,EAAE,MAAM;IACfuK,UAAU,EAAE,QAAQ;IACpBC,cAAc,EAAE,QAAQ;IACxB+E,UAAU,EAAE,eAAe;IAC3BC,OAAO,EAAE;EACX,CAAC;EACDnD,YAAY,EAAE;IACZrB,OAAO,EAAE,qBAAqB;IAC9BhL,OAAO,EAAE,MAAM;IACfyO,aAAa,EAAE,QAAQ;IACvBmB,GAAG,EAAE,MAAM;IACXyB,SAAS,EAAE,MAAM;IACjBxC,uBAAuB,EAAE,OAAO;IAChCsC,SAAS,EAAE;EACb,CAAC;EAED;EACAG,gBAAgB,EAAE;IAChBtR,OAAO,EAAE,MAAM;IACfyO,aAAa,EAAE,QAAQ;IACvBlE,UAAU,EAAE,QAAQ;IACpBqF,GAAG,EAAE;EACP,CAAC;EACD2B,cAAc,EAAE;IACdpH,QAAQ,EAAE,MAAM;IAChBC,UAAU,EAAE,KAAK;IACjBU,KAAK,EAAE,SAAS;IAChBmF,MAAM,EAAE,CAAC;IACTM,SAAS,EAAE;EACb,CAAC;EACDiB,iBAAiB,EAAE;IACjBrH,QAAQ,EAAE,MAAM;IAChBW,KAAK,EAAE,MAAM;IACbmF,MAAM,EAAE,CAAC;IACTM,SAAS,EAAE,QAAQ;IACnBkB,UAAU,EAAE;EACd,CAAC;EACDnF,eAAe,EAAE;IACftM,OAAO,EAAE,MAAM;IACf4P,GAAG,EAAE,MAAM;IACX/R,KAAK,EAAE,MAAM;IACbkT,QAAQ,EAAE;EACZ,CAAC;EACDxE,YAAY,EAAE;IACZwC,IAAI,EAAE,CAAC;IACP/D,OAAO,EAAE,WAAW;IACpBC,YAAY,EAAE,MAAM;IACpBd,QAAQ,EAAE,MAAM;IAChBC,UAAU,EAAE,KAAK;IACjBiF,MAAM,EAAE,SAAS;IACjBE,UAAU,EAAE,eAAe;IAC3BD,MAAM,EAAE,mBAAmB;IAC3BvE,eAAe,EAAE,SAAS;IAC1BD,KAAK,EAAE,MAAM;IACb0E,OAAO,EAAE;EACX,CAAC;EACDhD,kBAAkB,EAAE;IAClBzB,eAAe,EAAE,SAAS;IAC1BD,KAAK,EAAE,SAAS;IAChBkD,WAAW,EAAE,SAAS;IACtB5C,SAAS,EAAE;EACb,CAAC;EACDqB,eAAe,EAAE;IACf5O,KAAK,EAAE,MAAM;IACbkT,QAAQ,EAAE,OAAO;IACjB/Q,OAAO,EAAE,MAAM;IACfyO,aAAa,EAAE,QAAQ;IACvBmB,GAAG,EAAE;EACP,CAAC;EACDlD,WAAW,EAAE;IACXvC,QAAQ,EAAE,MAAM;IAChBC,UAAU,EAAE,KAAK;IACjBU,KAAK,EAAE,MAAM;IACbyF,SAAS,EAAE,QAAQ;IACnBvQ,OAAO,EAAE,MAAM;IACfuK,UAAU,EAAE,QAAQ;IACpBC,cAAc,EAAE,QAAQ;IACxBoF,GAAG,EAAE;EACP,CAAC;EACDjD,UAAU,EAAE;IACVxC,QAAQ,EAAE,MAAM;IAChBC,UAAU,EAAE,KAAK;IACjBU,KAAK,EAAE,SAAS;IAChBC,eAAe,EAAE,yBAAyB;IAC1CC,OAAO,EAAE,SAAS;IAClBC,YAAY,EAAE;EAChB,CAAC;EACD6B,MAAM,EAAE;IACNjP,KAAK,EAAE,MAAM;IACbG,MAAM,EAAE,KAAK;IACbiN,YAAY,EAAE,KAAK;IACnByG,UAAU,EAAE,SAAS;IACrBlC,OAAO,EAAE,MAAM;IACfH,MAAM,EAAE,SAAS;IACjBsC,gBAAgB,EAAE,MAAM;IACxBC,UAAU,EAAE;EACd,CAAC;EACD7E,YAAY,EAAE;IACZ/M,OAAO,EAAE,MAAM;IACfwK,cAAc,EAAE,eAAe;IAC/BL,QAAQ,EAAE,MAAM;IAChBW,KAAK,EAAE,MAAM;IACbsF,SAAS,EAAE;EACb,CAAC;EACDpD,aAAa,EAAE;IACbhN,OAAO,EAAE,MAAM;IACf4P,GAAG,EAAE,KAAK;IACV/R,KAAK,EAAE,MAAM;IACbkT,QAAQ,EAAE;EACZ,CAAC;EACD9D,YAAY,EAAE;IACZ8B,IAAI,EAAE,CAAC;IACP/D,OAAO,EAAE,UAAU;IACnBC,YAAY,EAAE,KAAK;IACnBd,QAAQ,EAAE,MAAM;IAChBC,UAAU,EAAE,KAAK;IACjBiF,MAAM,EAAE,SAAS;IACjBE,UAAU,EAAE,eAAe;IAC3BD,MAAM,EAAE,mBAAmB;IAC3BvE,eAAe,EAAE,SAAS;IAC1BD,KAAK,EAAE,MAAM;IACb0E,OAAO,EAAE;EACX,CAAC;EACDqC,cAAc,EAAE;IACdhU,KAAK,EAAE,MAAM;IACbkT,QAAQ,EAAE,OAAO;IACjB/F,OAAO,EAAE,WAAW;IACpBD,eAAe,EAAE,SAAS;IAC1BD,KAAK,EAAE,SAAS;IAChBG,YAAY,EAAE,MAAM;IACpBd,QAAQ,EAAE,MAAM;IAChBC,UAAU,EAAE,KAAK;IACjBiF,MAAM,EAAE,SAAS;IACjBE,UAAU,EAAE,eAAe;IAC3BD,MAAM,EAAE,MAAM;IACdE,OAAO,EAAE,MAAM;IACfpE,SAAS,EAAE;EACb,CAAC;EAED8B,gBAAgB,EAAE;IAChB5C,QAAQ,EAAE,UAAU;IACpBM,MAAM,EAAE,CAAC;IACTC,IAAI,EAAE,CAAC;IACP8E,KAAK,EAAE,CAAC;IACR5E,eAAe,EAAE,2BAA2B;IAC5C8E,cAAc,EAAE,YAAY;IAC5BC,oBAAoB,EAAE,YAAY;IAClCgC,mBAAmB,EAAE,MAAM;IAC3BC,oBAAoB,EAAE,MAAM;IAC5B/G,OAAO,EAAE,MAAM;IACfmG,SAAS,EAAE,MAAM;IACjBnR,OAAO,EAAE,MAAM;IACfyO,aAAa,EAAE,QAAQ;IACvBpD,MAAM,EAAE,EAAE;IACVD,SAAS,EAAE,iCAAiC;IAC5CkE,MAAM,EAAE,MAAM;IACd5E,SAAS,EAAE,eAAe;IAC1B6E,UAAU,EAAE,yBAAyB;IACrCZ,QAAQ,EAAE,QAAQ;IAClBxB,WAAW,EAAE,MAAM;IACnB6E,UAAU,EAAE,WAAW;IACvBC,UAAU,EAAE,MAAM;IAClBC,gBAAgB,EAAE;EACpB,CAAC;EACDC,QAAQ,EAAE;IACR7H,QAAQ,EAAE,UAAU;IACpB4E,GAAG,EAAE,MAAM;IACXS,KAAK,EAAE,MAAM;IACb7E,KAAK,EAAE,MAAM;IACbuE,MAAM,EAAE,SAAS;IACjBhE,MAAM,EAAE,EAAE;IACVxN,KAAK,EAAE,MAAM;IACbG,MAAM,EAAE,MAAM;IACdgC,OAAO,EAAE,MAAM;IACfuK,UAAU,EAAE,QAAQ;IACpBC,cAAc,EAAE,QAAQ;IACxBS,YAAY,EAAE,KAAK;IACnBF,eAAe,EAAE,oBAAoB;IACrCwE,UAAU,EAAE,eAAe;IAC3BD,MAAM,EAAE,MAAM;IACdE,OAAO,EAAE,MAAM;IACfxE,OAAO,EAAE;EACX,CAAC;EACDyC,WAAW,EAAE;IACXzN,OAAO,EAAE,MAAM;IACfyQ,YAAY,EAAE,MAAM;IACpB1F,eAAe,EAAE,SAAS;IAC1BE,YAAY,EAAE,MAAM;IACpBD,OAAO,EAAE,KAAK;IACd4E,GAAG,EAAE;EACP,CAAC;EACDlC,GAAG,EAAE;IACHqB,IAAI,EAAE,CAAC;IACPwB,SAAS,EAAE,QAAQ;IACnBvF,OAAO,EAAE,WAAW;IACpBC,YAAY,EAAE,KAAK;IACnBd,QAAQ,EAAE,MAAM;IAChBC,UAAU,EAAE,KAAK;IACjBiF,MAAM,EAAE,SAAS;IACjBE,UAAU,EAAE,eAAe;IAC3BzE,KAAK,EAAE,MAAM;IACb0E,OAAO,EAAE,MAAM;IACfF,MAAM,EAAE,MAAM;IACdvE,eAAe,EAAE,aAAa;IAC9B6D,uBAAuB,EAAE,aAAa;IACtCzB,WAAW,EAAE;EACf,CAAC;EACD1T,SAAS,EAAE;IACTsR,eAAe,EAAE,SAAS;IAC1BD,KAAK,EAAE,SAAS;IAChBM,SAAS,EAAE;EACb,CAAC;EACDuC,aAAa,EAAE;IACb3N,OAAO,EAAE,MAAM;IACfoS,mBAAmB,EAAE,uCAAuC;IAC5DxC,GAAG,EAAE,MAAM;IACXuB,SAAS,EAAE,oBAAoB;IAC/BE,SAAS,EAAE,MAAM;IACjBgB,aAAa,EAAE,MAAM;IACrBC,cAAc,EAAE,MAAM;IACtBC,cAAc,EAAE,cAAc;IAC9B1D,uBAAuB,EAAE;EAC3B,CAAC;EACDd,WAAW,EAAE;IACXzD,QAAQ,EAAE,UAAU;IACpBzM,KAAK,EAAE,MAAM;IACb2U,WAAW,EAAE,KAAK;IAClBzH,eAAe,EAAE,SAAS;IAC1BE,YAAY,EAAE,MAAM;IACpBjL,OAAO,EAAE,MAAM;IACfuK,UAAU,EAAE,QAAQ;IACpBC,cAAc,EAAE,QAAQ;IACxB6E,MAAM,EAAE,SAAS;IACjBE,UAAU,EAAE,eAAe;IAC3BD,MAAM,EAAE,mBAAmB;IAC3BX,QAAQ,EAAE,QAAQ;IAClBvD,SAAS,EAAE,+BAA+B;IAC1CJ,OAAO,EAAE,KAAK;IACdwE,OAAO,EAAE,MAAM;IACfZ,uBAAuB,EAAE,aAAa;IACtCzB,WAAW,EAAE;EACf,CAAC;EACDe,YAAY,EAAE;IACZrQ,KAAK,EAAE,MAAM;IACbG,MAAM,EAAE,MAAM;IACdyM,SAAS,EAAE,SAAS;IACpBQ,YAAY,EAAE,KAAK;IACnBF,eAAe,EAAE;EACnB,CAAC;EACDqD,YAAY,EAAE;IACZ9D,QAAQ,EAAE,UAAU;IACpBM,MAAM,EAAE,KAAK;IACbC,IAAI,EAAE,KAAK;IACX8E,KAAK,EAAE,KAAK;IACZxF,QAAQ,EAAE,KAAK;IACfW,KAAK,EAAE,MAAM;IACbyF,SAAS,EAAE,QAAQ;IACnBxF,eAAe,EAAE,2BAA2B;IAC5CE,YAAY,EAAE,KAAK;IACnBD,OAAO,EAAE,SAAS;IAClB2D,QAAQ,EAAE;EACZ,CAAC;EACDN,WAAW,EAAE;IACXlE,QAAQ,EAAE,KAAK;IACfC,UAAU,EAAE,KAAK;IACjBc,UAAU,EAAE,QAAQ;IACpBuH,YAAY,EAAE,UAAU;IACxB9D,QAAQ,EAAE,QAAQ;IAClB8B,YAAY,EAAE;EAChB,CAAC;EACDnC,WAAW,EAAE;IACXnE,QAAQ,EAAE,KAAK;IACfC,UAAU,EAAE,KAAK;IACjBU,KAAK,EAAE,SAAS;IAChBI,UAAU,EAAE;EACd,CAAC;EACDsC,UAAU,EAAE;IACV3P,KAAK,EAAE,MAAM;IACbG,MAAM,EAAE,KAAK;IACb+M,eAAe,EAAE,SAAS;IAC1BE,YAAY,EAAE,KAAK;IACnBgF,MAAM,EAAE,aAAa;IACrBZ,MAAM,EAAE,MAAM;IACdlC,WAAW,EAAE,MAAM;IACnB8E,UAAU,EAAE,MAAM;IAClBC,gBAAgB,EAAE;EACpB;AACF,CAAC;AAED,eAAevZ,KAAK;AAAC,IAAA4V,EAAA;AAAAmE,YAAA,CAAAnE,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}